<div class="table-content">
    <ol class="on-breadcrumb">
        <li><a routerLink="/console/load-balance">负载均衡</a>
        </li>
        <li><span>用量详情</span></li>
    </ol>

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">{{ lbName ? lbName + ' - ' : '' }}用量详情</h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <nz-select [(ngModel)]="checkTime"
                    (ngModelChange)="checkTimeChange($event)"
                    style="width: 120px;"
                    nzPlaceHolder="请选择查询间隔时间">
                    <nz-option *ngFor="let item of timeList"
                        [nzValue]="item.val"
                        [nzLabel]="item.name"></nz-option>
                </nz-select>
                <div class="pull-right">
                    <button nz-button nzType="primary"
                        (click)="refresh();">
                        <i nz-icon nzType="reload"
                            nzTheme="outline"></i>
                        刷新
                    </button>
                </div>
            </div>
            <div class="container-fluid">
                <nz-spin [nzSpinning]="isLoading"
                    [nzDelay]="300">
                    <div echarts [options]="httpChartOption"
                        class="chart-item"
                        [ngClass]="{'no-data': httpDataEmpty}"
                        [merge]="chartOptionConfig"
                        (chartInit)="onChartInit($event)">
                    </div>
                    <div echarts [options]="sessionChartOption"
                        class="chart-item"
                        [ngClass]="{'no-data': sessionDataEmpty}"
                        [merge]="chartOptionConfig"
                        (chartInit)="onChartInit($event)">
                    </div>
                </nz-spin>
            </div>
        </div>
    </div>
</div>
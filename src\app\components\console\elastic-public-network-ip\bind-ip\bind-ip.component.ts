import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CloudServerService } from 'src/app/service/console/cloud-server/cloud-server.service';
import { FormGroup, FormBuilder, AbstractControl, Validators } from '@angular/forms';
import { ElasticPublicNetworkService } from 'src/app/service/console/elastic-public-network-ip/elastic-public-network.service';
import { MessageService } from 'src/app/service/console/utils/message.service';

@Component({
    selector: 'app-bind-ip',
    templateUrl: './bind-ip.component.html',
    styleUrls: ['./bind-ip.component.less']
})
export class BindIpComponent implements OnInit {
    constructor(
        private fb: FormBuilder,
        private msg: MessageService,
        private pubNetworkService: ElasticPublicNetworkService,
        private cloudServerService: CloudServerService
    ) { }

    @Input() isVisible: boolean = false;
    @Input() network = {
        ipAddress: null,
    };

    @Output() submit = new EventEmitter<boolean>();
    @Output() close = new EventEmitter<boolean>();

    targetType: number = 1;
    okLoading: boolean = false;
    bind: FormGroup;
    instanceList = [];
    formSubmitAttempt: boolean = false;
    protocol = [{
        name: 'any',
        title: '任何'
    }, {
        name: 'tcp',
        title: 'TCP'
    }, {
        name: 'udp',
        title: 'UDP'
    }];
    protocolShow: boolean;

    ngOnInit() {
        this.bind = this.fb.group({
            elasticIpId: [''],
            vmId: ['', {
                validators: [
                    Validators.required
                ]
            }],
            vm_port: [''],
            public_port: [''],
            protocol: [null, {
                validators: [
                    Validators.required
                ]
            }],
        });

        this.getInstanceList();
    }

    getInstanceList() {
        this.cloudServerService.getCompleteCloudServerList({
            pageNum: 0,
            pageSize: 9999
        })
            .then(rs => {
                if (rs.success) {
                    let data = rs.data.dataList || [];
                    this.instanceList = data;
                    if (data.length) {
                        let initVmId = data[0].id;
                        this.bind.patchValue({
                            vmId: initVmId,
                            protocol: this.protocol[1].name
                        });
                    }
                }
            });
    }

    protocolChange(event) {
        let vm_port = this.bind.get('vm_port');
        let public_port = this.bind.get('public_port');
        if ('any' === event) {
            this.protocolShow = false;
            vm_port.clearValidators();
            public_port.clearValidators();
        } else {
            this.protocolShow = true;
            var chackData = [
                Validators.required,
                Validators.min(0),
                Validators.max(65535),
            ];
            vm_port.setValidators(chackData);
            public_port.setValidators(chackData);
        }
        vm_port.updateValueAndValidity();
        public_port.updateValueAndValidity();
    }

    handleCancel() {
        this.formSubmitAttempt = false;
        this.close.emit(true);
        this.bind.reset();
        if (this.instanceList.length) {

            this.bind.patchValue({
                vmId: this.instanceList[0].id,
                protocol: this.protocol[1].name
            });
        }
    }

    handleOk() {
        if (this.bind.invalid) {
            this.formSubmitAttempt = true;
            return;
        }

        this.formSubmitAttempt = false;
        let data = this.bind.value;
        data.elasticIpId = this.network['id'];
        this.okLoading = true;
        this.pubNetworkService.bind(data)
            .then(rs => {
                if (rs.success) {
                    this.msg.success('弹性公网IP绑定成功');
                    this.handleCancel();
                    this.submit.emit();
                } else {
                    this.msg.error(`弹性公网IP绑定失败${rs.message ? ': ' + rs.message : ''}`);
                }

                this.okLoading = false;
            })
            .catch(err => {
                this.msg.error('弹性公网IP绑定失败');
                this.okLoading = false;
            });
    }

    isInvalid(fc: AbstractControl): boolean {
        return (!fc.valid && fc.touched) ||
            (fc.untouched && this.formSubmitAttempt);
    }
}

import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

const BUSY_TEXT_MAP = {
    'delete': '删除中',
};

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrls: ['./index.component.less']
})
export class IndexComponent implements OnInit {
    openFlag: boolean = true;
    constructor() {}
    isAdmin = window.localStorage.getItem('isAdmin');
    resLimit: boolean = false;
    isLoading: boolean = false;
    pager = {
        page: 1,
        pageSize: environment.pageSize,
        total: 0,
    };
    filters = {
        pageNum: 0,
        pageSize: environment.pageSize,
        condition: ''
    };
    busyStatus = {};
    sort;
    index;

    cols = [
        {
            title: '名称',
            ColumnKey: "name",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '330px'
        },
        {
            title: '源',
            ColumnKey: "redisStatus",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '200px'
        },
        {
            title: '目标',
            ColumnKey: "redisType",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '200px'
        },
        {
            title: '服务',
            ColumnKey: "ipList",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '200px'
        },
        {
            title: '优先权',
            ColumnKey: "diskGb",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '已启用',
            ColumnKey: "sharding",
            allowSort: '',
            sortFlag: true,
            showSort: true,
            width: '150px'
        },
        {
            title: '操作',
            allowSort: '',
            sortFlag: true,
            showSort: false,
            ColumnKey: ""
        }
    ];

    ngOnInit() {}

    getSecurityList() {

    }

    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
        if (this.index !== undefined && this.index !== '') {
            this.cols[this.index].allowSort = this.sort;
        }
        this.openFlag = false;
    }

    pageChanged(pageNum) {
    }

    search() {
        this.filters.pageNum = 1;
        this.getSecurityList();
    }

    getBusyText(item): string {
        return BUSY_TEXT_MAP[this.busyStatus[item.id]] || '';
    }

    showEditModal(item) {
    }

    deleteMonitor(item) {
    }
}

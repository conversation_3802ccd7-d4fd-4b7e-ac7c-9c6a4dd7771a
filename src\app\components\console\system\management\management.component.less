.logo{
  display: flex;
  li{
    display: flex;
    align-items: center;
    height: 76px;
    width: 276px;
    img{height: 76px;}
  }
}
.active{
  background-color: #fff !important;
  border: 2px solid #155eef !important;
}

.logo-container{
  display:flex;
  flex-wrap: wrap;
  gap: 30px;
  padding:15px;
  .logo-item{
    //width:20%;
    display: flex;
    height: 80px;
    cursor: pointer;
    border: 1px solid #e8e8e8;
    padding:10px;
    align-items: center;
    align-content: center;
    img{
      //max-width: 100%;height: 80px;
      display: block;
    }
  }
}

.upload-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  nz-upload {
    margin-right: 10px;
  }
}

.small.tip {
  color: #999;
  font-size: 12px;
  display: block;
  margin-top: 5px;
}

.form-hint.error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 5px;
}

// LOGO上传区域左右排列样式
.logo-upload-row {
  display: flex;
  gap: 120px;
  align-items: flex-start;

  .logo-upload-item {
    flex: 1;
    min-width: 0; // 防止flex项目溢出
  }
}

.logo-preview {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;

  .preview-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
  }

  .logo-preview-image {
    display: block;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;

    &.full-logo {
      max-width: 140px;
      max-height: 80px;
    }

    &.mini-logo {
      max-width: 30px;
      max-height: 30px;
    }
  }
}
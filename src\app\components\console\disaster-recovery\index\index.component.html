<div class="cloud-bg">
  <section class="content-header">
    <ol class="CS-navigation">
      <li>灾备系统</li>
    </ol>
  </section>
  <section class="CS_box">
    <div class="CSbox-header">
      <div class="CSbox-title">灾备系统</div>

    </div>
    <div class="CSbox-top clearfix">
      <button class="create" [disabled]="refreshCheck" [ngClass]="refreshCheck?'disabled':''" (click)="clickRefresh()" href="javascript:void(0)">刷新</button>
    </div>

    <div class="CSbox-body">
      <div class="table-responsive">
        <table class="table no-margin table-striped">
          <thead>
          <tr>
            <th>主机名</th>
            <th>恢复状态</th>
            <th>复制状态</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let data of tableList; index as i">
            <td>{{data.placeholderVappName}}</td>
            <td>{{data.recoveryState}}</td>
            <td>{{data.replicationState}}</td>
            <td>
              <div class="table-btn-box" *ngIf="data.recoveryState === 'notStarted'">
                <div class="box">
                  <i class="icon_check iconfont icon-beifenhuifu" (click)="failOver(i)"></i>
                  <div class="link">
                    <p>恢复</p>
                  </div>
                </div>
              </div>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
</div>

<div id="console-layout" [ngClass]="{'fold': mainMenuFold}">
    <div class="console-bot-container">
        <div class="menu-list-container">

<!--                    <div>1-->
<!--                        <i class="icon-exclusiveServer iconfont icon"></i>-->
<!--                        <i class="iconfontBianji3 iconfont icon"></i>-->
<!--                        <i class="iconfontjingxiang iconfont icon"></i>-->
<!--                        <i class="icon-bianji3 iconfont icon"></i>-->
<!--                        <i class="icon-jingxiang iconfont icon"></i>-->
<!--                        <i class="icon-VPChutong iconfont icon"></i>-->
<!--                        <i class="icon-xinchuangkoudakai iconfont icon"></i>-->
<!--                        <i class="icon-biangengguanli iconfont icon"></i>-->
<!--                        <br/>-->
<!--                        <i class="icon-ceshi iconfont icon"></i>-->
<!--                        <i class="icon-huifu iconfont icon"></i>-->
<!--                        <i class="icon-yunzhujibeifen iconfont icon"></i>-->
<!--                        <i class="icon-yunzhuanxian iconfont icon"></i>-->
<!--                        <i class="icon-changjing-luojinshu iconfont icon"></i>-->
<!--                        <i class="icon-jiankongguanli iconfont icon"></i>-->
<!--                        <i class="icon-gerenzhongxin-zhong1 iconfont icon"></i>-->
<!--                        <i class="icon-securityPolicy iconfont icon"></i>-->
<!--                        <br/>2-->
<!--                        <i class="icon-elasticScaling iconfont icon"></i>-->
<!--                        <i class="icon-security iconfont icon"></i>-->
<!--                        <i class="icon-yunrizhi iconfont icon"></i>-->
<!--                        <i class="icon-vpn iconfont icon"></i>-->
<!--                        <i class="icon-yunpan iconfont icon"></i>-->
<!--                        <i class="icon-cloudDisk iconfont icon"></i>-->
<!--                        <i class="icon-fileStorage iconfont icon"></i>-->
<!--                        <br/>3-->
<!--                        <i class="icon-objectStorage iconfont icon"></i>-->
<!--                        <i class="icon-rds iconfont icon"></i>-->
<!--                        <i class="icon-xiaoshoutongji iconfont icon"></i>-->
<!--                        <i class="icon-file1 iconfont icon"></i>-->
<!--                        <i class="icon-cq iconfont icon"></i>-->
<!--                        <i class="icon-yuancheng01-copy iconfont icon"></i>-->
<!--                        <i class="icon-jiaoyixingneng iconfont icon"></i>-->
<!--                        <i class="icon-iconaddvm iconfont icon"></i>-->
<!--                        <br/>4-->
<!--                        <i class="icon-tree iconfont icon"></i>-->
<!--                        <i class="icon-seedetails iconfont icon"></i>-->
<!--                        <i class="icon-qidong iconfont icon"></i>-->
<!--                        <i class="icon-close1 iconfont icon"></i>-->
<!--                        <i class="icon-ic_restore iconfont icon"></i>-->
<!--                        <i class="icon-beifenhuifu iconfont icon"></i>-->
<!--                        <i class="icon-kuaizhaobeifenyuhuifu iconfont icon"></i>-->
<!--                        <i class="icon-file iconfont icon"></i>-->
<!--                        <br/>5-->
<!--                        <i class="icon-redis iconfont icon"></i>-->
<!--                        <i class="icon-shifang iconfont icon"></i>-->
<!--                        <i class="icon-shifang1 iconfont icon"></i>-->
<!--                        <i class="icon-shifang2 iconfont icon"></i>-->
<!--                        <i class="icon-download iconfont icon"></i>-->
<!--                        <i class="icon-monitoring iconfont icon"></i>-->
<!--                        <i class="icon-alipay iconfont icon"></i>-->
<!--                        <i class="icon-wechart iconfont icon"></i>-->
<!--                        <br/>6-->
<!--                        <i class="icon-chongzhi iconfont icon"></i>-->
<!--                        <i class="icon-zhangdan iconfont icon"></i>-->
<!--                        <i class="icon-qiandai iconfont icon"></i>-->
<!--                        <i class="icon-hongbao iconfont icon"></i>-->
<!--                        <i class="icon-tubiaozhizuomoban1 iconfont icon"></i>-->
<!--                        <i class="icon-tubiaozhizuomoban iconfont icon"></i>-->
<!--                        <i class="icon-chakan1 iconfont icon"></i>-->
<!--                        <i class="icon-bianji2 iconfont icon"></i>-->
<!--                        <br/>7-->
<!--                        <i class="icon-dianji iconfont icon"></i>-->
<!--                        <i class="icon-sousuo iconfont icon"></i>-->
<!--                        <i class="icon-open iconfont icon"></i>-->
<!--                        <i class="icon-close iconfont icon"></i>-->
<!--                        <i class="icon-iconset0401 iconfont icon"></i>-->
<!--                        <i class="icon-wallet iconfont icon"></i>-->
<!--                        <i class="icon-EPCzuodaohangtubiao- iconfont icon"></i>-->
<!--                        <br/>8-->
<!--                        <i class="icon-yunpanxuanzhong iconfont icon"></i>-->
<!--                        <i class="icon-feiyong iconfont icon"></i>-->
<!--                        <i class="icon-saoyisao iconfont icon"></i>-->
<!--                        <i class="icon-yunfuwuqi iconfont icon"></i>-->
<!--                        <i class="icon-dijia iconfont icon"></i>-->
<!--                        <i class="icon-jiangdichengben iconfont icon"></i>-->
<!--                        <i class="icon-shijian iconfont icon"></i>-->
<!--                        <i class="icon-yingyongbushu iconfont icon"></i>-->
<!--                        <br/>9-->
<!--                        <i class="icon-wangzhanyingyong iconfont icon"></i>-->
<!--                        <i class="icon-kaifaceshi iconfont icon"></i>-->
<!--                        <i class="icon-gaobingfayingyong iconfont icon"></i>-->
<!--                        <i class="icon-yinshipin iconfont icon"></i>-->
<!--                        <i class="icon-hulianhutong iconfont icon"></i>-->
<!--                        <i class="icon-webfuwu1 iconfont icon"></i>-->
<!--                        <i class="icon-shujuku1 iconfont icon"></i>-->
<!--                        <i class="icon-shujucunchu iconfont icon"></i>-->
<!--                        <br/>10-->
<!--                        <i class="icon-yewuxitong iconfont icon"></i>-->
<!--                        <i class="icon-yinpin iconfont icon"></i>-->
<!--                        <i class="icon-linghuokuozhan iconfont icon"></i>-->
<!--                        <i class="icon-linghuokuozhan1 iconfont icon"></i>-->
<!--                        <i class="icon-xingnengqianghan iconfont icon"></i>-->
<!--                        <i class="icon-xingnengqianghan1 iconfont icon"></i>-->
<!--                        <i class="icon-jiandanyiyong iconfont icon"></i>-->
<!--                        <i class="icon-jiandanyiyong1 iconfont icon"></i>-->
<!--                        <br/>11-->
<!--                        <i class="icon-yunjisuan- iconfont icon"></i>-->
<!--                        <i class="icon-yunjisuan-1 iconfont icon"></i>-->
<!--                        <i class="icon-yunjisuan-2 iconfont icon"></i>-->
<!--                        <i class="icon-yunjisuan-3 iconfont icon"></i>-->
<!--                        <i class="icon-yunjisuan-4 iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong3 iconfont icon"></i>-->
<!--                        <i class="icon-gaodanxing iconfont icon"></i>-->
<!--                        <br/>12-->
<!--                        <i class="icon-gaodanxing1 iconfont icon"></i>-->
<!--                        <i class="icon-gaokekao iconfont icon"></i>-->
<!--                        <i class="icon-gaokekao1 iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong6 iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong7 iconfont icon"></i>-->
<!--                        <i class="icon-gaolinghuosvg- iconfont icon"></i>-->
<!--                        <i class="icon-gaolinghuosvg-1 iconfont icon"></i>-->
<!--                        <br/>13-->
<!--                        <i class="icon-gaoxingjiabi iconfont icon"></i>-->
<!--                        <i class="icon-gaoxingjiabi1 iconfont icon"></i>-->
<!--                        <i class="icon-zidingyiwangluo iconfont icon"></i>-->
<!--                        <i class="icon-zidingyiwangluo1 iconfont icon"></i>-->
<!--                        <i class="icon-anquangeli iconfont icon"></i>-->
<!--                        <i class="icon-anquangeli1 iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong1 iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong2 iconfont icon"></i>-->
<!--                        <br/>14-->
<!--                        <i class="icon-gaoyiyong iconfont icon"></i>-->
<!--                        <i class="icon-gaoyiyong1 iconfont icon"></i>-->
<!--                        <i class="icon-shiyonggaoxiao iconfont icon"></i>-->
<!--                        <i class="icon-shiyonggaoxiao1 iconfont icon"></i>-->
<!--                        <i class="icon-linghuobushu iconfont icon"></i>-->
<!--                        <i class="icon-linghuobushu1 iconfont icon"></i>-->
<!--                        <i class="icon-bgp iconfont icon"></i>-->
<!--                        <i class="icon-bgp1 iconfont icon"></i>-->
<!--                        <br/>15-->
<!--                        <i class="icon-jikaijiyong iconfont icon"></i>-->
<!--                        <i class="icon-jikaijiyong1 iconfont icon"></i>-->
<!--                        <i class="icon-bianji1 iconfont icon"></i>-->
<!--                        <i class="icon-huanyuan iconfont icon"></i>-->
<!--                        <i class="icon-duigou1 iconfont icon"></i>-->
<!--                        <i class="icon-yijianxiezaiAPP iconfont icon"></i>-->
<!--                        <i class="icon-mount iconfont icon"></i>-->
<!--                        <br/>16-->
<!--                        <i class="icon-Connection-trends iconfont icon"></i>-->
<!--                        <i class="icon-webfuwu iconfont icon"></i>-->
<!--                        <i class="icon-lianjie1 iconfont icon"></i>-->
<!--                        <i class="icon-xinxihulianhutong iconfont icon"></i>-->
<!--                        <i class="icon-anquan iconfont icon"></i>-->
<!--                        <i class="icon-zhuanyouwangluo iconfont icon"></i>-->
<!--                        <i class="icon-gaokeyong iconfont icon"></i>-->
<!--                        <i class="icon-dashujucunchu iconfont icon"></i>-->
<!--                        <br/>17-->
<!--                        <i class="icon-yewugongneng iconfont icon"></i>-->
<!--                        <i class="icon-shujuku iconfont icon"></i>-->
<!--                        <i class="icon-danxingkuorong iconfont icon"></i>-->
<!--                        <i class="icon-fangbian iconfont icon"></i>-->
<!--                        <i class="icon-analysis iconfont icon"></i>-->
<!--                        <i class="icon-jianting iconfont icon"></i>-->
<!--                        <i class="icon-fabu iconfont icon"></i>-->
<!--                        <i class="icon-jiebang iconfont icon"></i>-->
<!--                        <i class="icon-bangding iconfont icon"></i>-->
<!--                        <br/>18-->
<!--                        <i class="icon-linghuo iconfont icon"></i>-->
<!--                        <i class="icon-BGP iconfont icon"></i>-->
<!--                        <i class="icon&#45;&#45;jikaijiyong-morenzhuangtai iconfont icon"></i>-->
<!--                        <i class="icon-gaoxiaoshuai iconfont icon"></i>-->
<!--                        <i class="icon-liuliang1 iconfont icon"></i>-->
<!--                        <i class="icon-ip iconfont icon"></i>-->
<!--                        <i class="icon-right iconfont icon"></i>-->
<!--                        <i class="icon-liuliang iconfont icon"></i>-->
<!--                        <i class="icon-chakan iconfont icon"></i>-->
<!--                        <br/>19-->
<!--                        <i class="icon-bianji iconfont icon"></i>-->
<!--                        <i class="icon-zhuyi iconfont icon"></i>-->
<!--                        <i class="icon-shuaxin iconfont icon"></i>-->
<!--                        <i class="icon-duihao iconfont icon"></i>-->
<!--                        <i class="icon-icon iconfont icon"></i>-->
<!--                        <i class="icon-shang iconfont icon"></i>-->
<!--                        <i class="icon-space iconfont icon"></i>-->
<!--                        <i class="icon-danxinggongwangIP iconfont icon"></i>-->
<!--                        <br/>20-->
<!--                        <i class="icon-fuzaijunheng iconfont icon"></i>-->
<!--                        <i class="icon-wangluo iconfont icon"></i>-->
<!--                        <i class="icon-yunfuwuqi-yijicaidanzuhu iconfont icon"></i>-->
<!--                        <i class="icon-lianjie2 iconfont icon"></i>-->
<!--                        <i class="icon-lianjie iconfont icon"></i>-->
<!--                        <i class="icon-guanji iconfont icon"></i>-->
<!--                        <i class="icon-fangwen iconfont icon"></i>-->
<!--                        <i class="icon-kaiji iconfont icon"></i>-->
<!--                        <br/>21-->
<!--                        <i class="icon-shanchu iconfont icon"></i>-->
<!--                        <i class="icon-haikezhangguizhushou_zhongqi iconfont icon"></i>-->
<!--                        <i class="icon-duigou iconfont icon"></i>-->
<!--                        <i class="icon-kekao iconfont icon"></i>-->
<!--                        <i class="icon-bushu iconfont icon"></i>-->
<!--                        <i class="icon-yonghu iconfont icon"></i>-->
<!--                        <i class="icon-shouye iconfont icon"></i>-->
<!--                        <i class="icon-wangzhan iconfont icon"></i>-->
<!--                        <i class="icon-shousuo iconfont icon"></i>-->
<!--                        <br/>22-->
<!--                        <i class="icon-shousuo1 iconfont icon"></i>-->
<!--                        <i class="icon-ziliao iconfont icon"></i>-->
<!--                        <i class="icon-chuangjian iconfont icon"></i>-->
<!--                        <i class="icon-tuichu iconfont icon"></i>-->
<!--                        <i class="icon-kongzhitai iconfont icon"></i>-->
<!--                        <i class="icon-gaoxingjiabidingzhi iconfont icon"></i>-->
<!--                        <i class="icon-developmentandtest iconfont icon"></i>-->
<!--                        <br/>23-->
<!--                        <i class="icon-fuwuqi iconfont icon"></i>-->
<!--                        <i class="icon-cunchu iconfont icon"></i>-->
<!--                    </div>-->

            <div class="logo-class">
                <a (click)="navigateToConsole()" routerLink="/console/" style="display:flex;align-items: center;">
<!--                    <img class="unfold-logo" src="./assets/images/logo/{{logo}}.png" style="height:28px;"/>-->
<!--                    <img class="fold-logo" style="height:28px;" [src]="'./assets/images/logo/' + logo + '-simple.png'" (error)="onImageError($event)"/>-->
                    <img class="unfold-logo" src="/cloud/logo/full-logo.png" style="height:28px;" (error)="onFullImageError($event)"/>
                    <img class="fold-logo" style="height:28px;" [src]="'/cloud/logo/mini-logo.png'" (error)="onMiniImageError($event)"/>
                </a>
                <a class="menu-toggle-button">
                    <i class="menu-fold-toggle" (click)="toggleMainMenu()" nz-icon [title]="mainMenuFold ? '展开菜单' : '折叠菜单'" [nzType]="mainMenuFold ? 'menu-unfold' : 'menu-fold'"
                       nzTheme="outline"></i>
                </a>
            </div>
            <!-- 菜单标题 -->
            <div *ngIf="showMenuTitle" class="menu-title">
                <span (click)="navigateToConsole()" class="back-to-main">
                    <i nz-icon nzType="left" nzTheme="outline"></i>
                    <span>{{ currentMenuTitle }}</span>
                </span>
            </div>
            <ul class="menu-list" [ngClass]="{'with-title': showMenuTitle, 'without-title': !showMenuTitle}">
                <li class="menu-item" *ngFor="let menu of filtedMenuData" routerLinkActive="active">
                    <a *ngIf="menu.name !== 'system'" [routerLink]="generateRouterLink(menu.routerLink)"
                        [title]="mainMenuFold ? menu.title : ''"
                        (click)="setSubMenu(menu);">
                        <i class="iconfont icon-{{ menu.icon }} icon"></i>
                        <span class="text">{{ menu.title }}</span>
                    </a>
                    <a *ngIf="menu.name === 'system'" href="javascript:void(0)"
                        [title]="mainMenuFold ? menu.title : ''"
                        (click)="navigateToOperation()">
                        <i class="iconfont icon-{{ menu.icon }} icon"></i>
                        <span class="text">{{ menu.title }}</span>
                    </a>

                    <!-- 集成子菜单 -->
                    <div class="console-sub-menu" *ngIf="currentMenu && currentMenu.title === menu.title && !mainMenuFold && menu.subMenus && menu.subMenus.length > 0">
                        <ul class="sub-menu-list">
                            <li class="menu-item" *ngFor="let subMenu of currentMenu.subMenus">
                                <!-- Submenu level 1 -->
                                <a *ngIf="!subMenu.children"
                                   class="{{subMenu.active}}"
                                   (click)="navigateToSubMenu(currentMenu, subMenu); stopClick($event)"
                                   href="javascript:void(0)">
                                    <span>{{ subMenu.title }}</span>
                                </a>
                                <!-- Submenu level 1 (Parent) -->
                                <a *ngIf="subMenu.children"
                                    (click)="unfoldSubMenu($event, subMenu)"
                                    href="javascript:void(0)"
                                    routerLinkActive="active-sub"
                                    [ngClass]="{'unfold': subMenu.unfold, 'fold': !subMenu.unfold}">
                                    {{ subMenu.title }}
                                    <span class="submenu-indicator">
                                        <i nz-icon [nzType]="subMenu.unfold ? 'up' : 'down'" nzTheme="outline"></i>
                                    </span>
                                    <ul class="sub-menu-list">
                                        <!-- Submenu level 2 -->
                                        <li class="menu-item" *ngFor="let s3 of subMenu.children">
                                            <a *ngIf="!s3.outlet"
                                               class="{{s3.active}}"
                                               (click)="navigateToSubMenu(currentMenu, s3); stopClick($event)"
                                               href="javascript:void(0)"> <!-- Use click handler -->
                                                {{ s3.title }}</a>
                                            <!-- Handle outlet routes if necessary (keeping original logic for now) -->
                                            <a *ngIf="s3.outlet"
                                               routerLinkActive="active"
                                               [routerLink]="[currentMenu.routerLink, {outlets:  s3.outlet}]"
                                               (click)="stopClick($event)">
                                                {{ s3.title }}</a>
                                        </li>
                                    </ul>
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>

        <div class="feature-container" id="feature-container">
            <div class="console-nav-container">
                <app-console-nav (toggleMenu)="toggleMainMenu()" (navigate)="setSubMenuByNav($event)" (aiButton)="AIModalVisible = true"></app-console-nav>
            </div>
            <div class="content-model" style="position: relative">
                <router-outlet ></router-outlet>
                <app-ai [isVisible]="AIModalVisible" (close)="AIModalVisible = false"></app-ai>
            </div>
        </div>
    </div>

</div>
<div class="table-content">
<!--    <ol class="on-breadcrumb">-->
<!--        <li><a routerLink="../">运维管理</a></li>-->
<!--        <li><span>服务器配置</span></li>-->
<!--    </ol>-->
    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">服务器配置</h3>
        </div>
        <div class="on-panel-body config-content">
            <div class="content-body-item">
                <div class="action-bar clearfix">
                    <form nz-form [formGroup]="managementGroup" style="width:100%">
                        <section class="field-section">
                            <div class="field-title">
                                基本信息
                            </div>
                            <div class="field-group">
                                <div class="field-item required">
                                    <label>
                                        <span class="label-text">连接类型</span>
                                        <nz-select formControlName="type" nzShowSearch nzPlaceHolder="请选择类型" style="width:240px">
                                            <nz-option *ngFor="let item of typeList" [nzValue]="item.key"
                                                       [nzLabel]="item.value">
                                            </nz-option>
                                        </nz-select>
                                    </label>
                                </div>
                            </div>
                            <div class="field-group">
                                <div class="field-item required">
                                    <label>
                                        <span class="label-text">AK</span>
                                        <input required maxlength="200" type="text" formControlName="as" placeholder="请输入AK" style="width:480px"/>
                                    </label>
                                    <div class="form-hint error" *ngIf="isInvalid(managementGroup.get('as'))">
                                        <div *ngIf="managementGroup.get('as').hasError('required')">
                                            AS不能为空
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="field-group">
                                <div class="field-item required">
                                    <label>
                                        <span class="label-text">SK</span>
                                        <input required maxlength="200" type="password" formControlName="sk" placeholder="请输入SK" style="width:480px"/>
                                    </label>
                                    <div class="form-hint error" *ngIf="isInvalid(managementGroup.get('sk'))">
                                        <div *ngIf="managementGroup.get('sk').hasError('required')">
                                            SK不能为空
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;" (click)="testConnection()">
                                <i nz-icon nzType="search"></i>
                                测试连接
                            </button>
                            <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;" (click)="submitData()">
                                <i nz-icon nzType="save"></i>
                                确认
                            </button>
                        </section>
                    </form>
                </div>
            </div>
        </div>

        <div class="on-panel-body config-content">
            <div class="content-body-item">
                <div class="action-bar clearfix">
                    <form nz-form [formGroup]="systemConfigGroup" style="width:100%">
                        <section class="field-section">
                            <div class="field-title">
                                网站基础信息
                            </div>

                            <div class="field-group">
                                <div class="field-item">
                                    <label>
                                        <span class="label-text">网站名称</span>
                                        <input required maxlength="200" type="text" formControlName="title" placeholder="请输入" style="width:480px"/>
                                    </label>
                                </div>
                            </div>
                            <!-- LOGO上传区域 - 左右排列 -->
                            <div class="field-group logo-upload-row">
                                <!-- 全尺寸LOGO上传 -->
                                <div class="field-item logo-upload-item">
                                    <label>
                                        <span class="label-text">全尺寸LOGO（必选）</span>
                                        <div class="upload-container">
                                            <nz-upload
                                                [nzDisabled]="isUploadingFullLogo"
                                                [nzBeforeUpload]="beforeUploadFullLogo"
                                                [nzFileList]="fullLogoFileList"
                                                [nzRemove]="removeFullLogo"
                                                nzAccept="image/*"
                                                [nzShowUploadList]="true">
                                                <button type="button" nz-button [nzLoading]="isUploadingFullLogo">
                                                    <i nz-icon nzType="upload"></i>
                                                    {{ isUploadingFullLogo ? '上传中' : '选择文件' }}
                                                </button>
                                            </nz-upload>
                                            <button nz-button nzType="primary"
                                                    (click)="uploadFullLogo()"
                                                    style="margin-left: 10px;">
                                                <i nz-icon nzType="save"></i>
                                                上传
                                            </button>
                                        </div>
                                        <span class="small tip">上传正常尺寸LOGO，140x80</span>
                                        <div class="form-hint error" *ngIf="fullLogoError">
                                            {{ fullLogoError }}
                                        </div>
                                        <!-- 全尺寸LOGO预览 -->
                                        <div class="logo-preview" *ngIf="showFullLogoPreview">
                                            <span class="preview-label">当前LOGO预览：</span>
                                            <img [src]="fullLogoPreviewUrl"
                                                 alt="全尺寸LOGO预览"
                                                 class="logo-preview-image full-logo"
                                                 (error)="showFullLogoPreview = false" />
                                        </div>
                                    </label>
                                </div>

                                <!-- 缩略版LOGO上传 -->
                                <div class="field-item logo-upload-item">
                                    <label>
                                        <span class="label-text">缩略版LOGO（选填）</span>
                                        <div class="upload-container">
                                            <nz-upload
                                                [nzDisabled]="isUploadingMiniLogo"
                                                [nzBeforeUpload]="beforeUploadMiniLogo"
                                                [nzFileList]="miniLogoFileList"
                                                [nzRemove]="removeMiniLogo"
                                                nzAccept="image/*"
                                                [nzShowUploadList]="true">
                                                <button type="button" nz-button [nzLoading]="isUploadingMiniLogo">
                                                    <i nz-icon nzType="upload"></i>
                                                    {{ isUploadingMiniLogo ? '上传中' : '选择文件' }}
                                                </button>
                                            </nz-upload>
                                            <button type="button" nz-button nzType="primary"
                                                    (click)="uploadMiniLogo()"
                                                    style="margin-left: 10px;">
                                                <i nz-icon nzType="save"></i>
                                                上传
                                            </button>
                                        </div>
                                        <span class="small tip">上传小尺寸LOGO，30x30</span>
                                        <div class="form-hint error" *ngIf="miniLogoError">
                                            {{ miniLogoError }}
                                        </div>
                                        <!-- 缩略版LOGO预览 -->
                                        <div class="logo-preview" *ngIf="showMiniLogoPreview">
                                            <span class="preview-label">当前LOGO预览：</span>
                                            <img [src]="miniLogoPreviewUrl"
                                                 alt="缩略版LOGO预览"
                                                 class="logo-preview-image mini-logo"
                                                 (error)="showMiniLogoPreview = false" />
                                        </div>
                                    </label>
                                </div>
                            </div>
<!--                            <button nz-button nzType="primary" nzSearch style=" margin-left: 10px;" (click)="submitSystemConfigData()">-->
<!--                                <i nz-icon nzType="save"></i>-->
<!--                                确认-->
<!--                            </button>-->
                        </section>
                    </form>
                </div>
            </div>
        </div>

        <div class="on-panel-body config-content">
            <div class="content-body-item">
                <div class="action-bar clearfix">
                        <section class="field-section">
                            <div class="field-title">
                                AI Chat配置
                            </div>

                            <div class="field-group">
                                <div class="field-item">
                                    <label>
                                        <span class="label-text">AI Chat地址</span>
                                        <input required type="text" [(ngModel)]="ai_url" placeholder="请输入" style="width:480px"/>
                                    </label>
                                </div>
                            </div>

                            <button nz-button nzType="primary" nzSearch style="margin-left: 10px;" (click)="saveAiUrl()">
                                <i nz-icon nzType="save"></i>
                                确认
                            </button>
                        </section>
                </div>
            </div>
        </div>
    </div>
</div>

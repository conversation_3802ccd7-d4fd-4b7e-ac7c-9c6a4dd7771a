.on-panel {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;

    .on-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .on-panel-title {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
        }

        .on-panel-actions {
            .action-group {
                display: flex;
                gap: 8px;
            }
        }
    }

    .on-panel-body {
        padding: 24px;

        .search-bar {
            margin-bottom: 16px;
            width: 300px;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .title {
                font-size: 14px;
                font-weight: 500;
                color: #262626;
            }
        }

        nz-table {
            .ant-table-thead > tr > th {
                background-color: #fafafa;
                font-weight: 500;
            }

            .ant-table-tbody > tr > td {
                vertical-align: middle;
            }

            .ant-btn-link {
                padding: 0;
                height: auto;
                line-height: 1.5;
            }
        }
    }
}

.on-table-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .on-table-action-item {
        cursor: pointer;
        //padding: 4px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
            background-color: #f5f5f5;
        }

        .icon {
            //font-size: 16px;
            color: #666;

            &:hover {
                color: #1890ff;
            }
        }
    }
}

.dot {
    display: inline-flex;
    align-items: center;

    &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
        background-color: #d9d9d9;
    }

    &.dot-green::before {
        background-color: #52c41a;
    }

    &.dot-gray::before {
        background-color: #d9d9d9;
    }
}

// 弹框样式
.ant-modal {
    .ant-modal-header {
        border-bottom: 1px solid #f0f0f0;
    }

    .ant-modal-body {
        padding: 24px;

        .ant-form-item {
            margin-bottom: 16px;

            .ant-form-item-label {
                padding-bottom: 4px;
            }

            .ant-select,
            .ant-input {
                width: 100%;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .on-panel {
        .on-panel-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }

        .on-panel-body {
            padding: 16px;

            .search-bar {
                width: 100%;
            }
        }
    }
}

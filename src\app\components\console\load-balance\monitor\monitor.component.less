@import '../../../../style/common/_variable.less';

.sub-title {
    text-align: left;
    font-weight: bold;
    margin: 10px 0;
    line-height: 24px;

    &:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 24px;
        background-color: @primary;
        vertical-align: top;
        margin-right: 5px;
    }

    button {
        font-size: 12px;
    }
}

.sub-table-section {
    margin-top: 0;
    margin-bottom: 20px;
    width: 100%;

    &:last-child {
        margin-bottom: 0;
    }

    td {
        transition: none;
        transition: background-color 0.3s ease 0s;
        span {
            display: inline-block;
            line-height: 20px;
        }
    }

    tr.editing {
        td {
            padding-top: 7px;
            padding-bottom: 7px;
        }
    }

    input {
        text-align: center;
    }
}
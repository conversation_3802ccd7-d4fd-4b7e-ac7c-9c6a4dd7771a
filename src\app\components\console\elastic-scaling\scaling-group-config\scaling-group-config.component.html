<div class="scaling-group-config config-content">
    <h4 class="title">{{ getCurrentTitle() }}
        <a routerLink=".." class="back"><i class="icon" nz-icon nzType="left" nzTheme="outline"></i>返回列表</a>
    </h4>

    <div class="panel">
        <div class="panel-body">
            <!-- 伸缩组配置 -->
            <form [formGroup]="scalingGroup" [hidden]="!(currentStep === 'groupConfig')">
                <section class="field-section">
                    <div class="field-title">
                        监控规则
                    </div>
                    <div class="field-group">
                        <div class="field-item required select-group">
                            <label for="">
                                <span class="label-text">监控规则</span>
                                <nz-select style="width: 120px" (ngModelChange)="chooseruleType($event)"
                                    formControlName="ruleType" nzPlaceHolder="请选择监控规则">
                                    <nz-option *ngFor="let item of initData.monitorRuleTypes" [nzValue]="item.name"
                                        [nzLabel]="item.title">
                                    </nz-option>
                                </nz-select>
                                <nz-select style="width: 90px; margin-left: 5px;"
                                    (ngModelChange)="choosemonitorType($event)" formControlName="monitorType"
                                    nzPlaceHolder="请选择监控周期">
                                    <nz-option *ngFor="let item of initData.monitorTypes" [nzValue]="item.name"
                                        [nzLabel]="item.title">
                                    </nz-option>
                                </nz-select>
                                <nz-input-number style="width: 70px; margin-left: 5px;" formControlName="value1"
                                    *ngIf="scalingGroup.value.condition === 'bt'" [nzMin]="0" [nzMax]="monitorMaxSize"
                                    [nzStep]="1"></nz-input-number>
                                <nz-select style="width: 70px; margin-left: 5px;"
                                    (ngModelChange)="choosecondition($event)" formControlName="condition"
                                    nzPlaceHolder="请选择告警条件">
                                    <nz-option *ngFor="let item of initData.monitorConditionTypes" [nzValue]="item.name"
                                        [nzLabel]="item.title">
                                    </nz-option>
                                </nz-select>
                                <nz-input-number style="width: 70px; margin-left: 5px;" formControlName="value2"
                                    [nzMin]="0" [nzMax]="monitorMaxSize" [nzStep]="1"></nz-input-number>
                                <span class="small tip" *ngIf="showUnit">%</span>
                                <span class="small tip" *ngIf="!showUnit">KB/S</span>
                                <div *ngIf="isInvalid(scalingGroup.get('value1')) || isInvalid(scalingGroup.get('value2'))"
                                    class="form-hint error">
                                    <div
                                        *ngIf="scalingGroup.get('value1').hasError('required') || scalingGroup.get('value2').hasError('required')">
                                        请填写监控阈值
                                    </div>
                                    <div
                                        *ngIf="scalingGroup.get('value1').hasError('pattern') || scalingGroup.get('value2').hasError('pattern')">
                                        请填写正确的监控阈值
                                    </div>
                                    <div
                                        *ngIf="scalingGroup.get('value1').hasError('min') || scalingGroup.get('value2').hasError('min')">
                                        监控阈值不能低于0
                                    </div>
                                    <div
                                        *ngIf="scalingGroup.get('value1').hasError('max') || scalingGroup.get('value2').hasError('max')">
                                        监控阈值不能高于100
                                    </div>
                                </div>
                                <div class="form-hint error"
                                    *ngIf="(scalingGroup.errors && scalingGroup.errors.maxMonitorError)">
                                    阈值上限必须大于阈值下限
                                </div>
                            </label>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        基础信息
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <span class="label-text">名称</span>
                                <input type="text" required maxlength="50" name="scalingGroupName"
                                    formControlName="scalingGroupName" placeholder="请输入伸缩组名称">
                            </label>
                            <div *ngIf="isInvalid(scalingGroup.get('scalingGroupName'))" class="form-hint error">
                                <div *ngIf="scalingGroup.get('scalingGroupName').hasError('required')">
                                    伸缩组名不能为空
                                </div>
                                <div *ngIf="scalingGroup.get('scalingGroupName').hasError('maxlength')">
                                    伸缩组名长度不能超过{{ scalingGroup.get('scalingGroupName').errors.maxlength.requiredLength }}个字符
                                </div>
                                <div *ngIf="scalingGroup.get('scalingGroupName').hasError('pattern')">
                                    伸缩组名名称不符合规范
                                </div>
                            </div>
                        </div>
                        <div class="field-item required">
                            <label>
                                <span class="label-text">最小实例数(台)</span>
                                <input type="text" required formControlName="minimumInstances" placeholder="请输入最小实例数">
                            </label>
                            <div *ngIf="isInvalid(scalingGroup.get('minimumInstances'))" class="form-hint error">
                                <div *ngIf="scalingGroup.get('minimumInstances').hasError('required')">
                                    最小实例数不能为空
                                </div>
                                <div *ngIf="scalingGroup.get('minimumInstances').hasError('pattern')">
                                    请输入正确的数字
                                </div>
                                <div *ngIf="scalingGroup.get('minimumInstances').hasError('min')">
                                    最小实例数至少为{{ scalingGroup.get('minimumInstances').errors.min.min }}台
                                </div>
                                <div *ngIf="scalingGroup.get('minimumInstances').hasError('max')">
                                    最小实例数至多为{{ scalingGroup.get('minimumInstances').errors.max.max }}台
                                </div>
                            </div>                            
                        </div>
                        <div class="field-item required">
                            <label>
                                <span class="label-text">最大实例数(台)</span>
                                <input type="text" required formControlName="maximumInstances" placeholder="请输入最大实例数">
                            </label>
                            <div *ngIf="isInvalid(scalingGroup.get('maximumInstances'))" class="form-hint error">
                                <div *ngIf="scalingGroup.get('maximumInstances').hasError('required')">
                                    最大实例数不能为空
                                </div>
                                <div *ngIf="scalingGroup.get('maximumInstances').hasError('pattern')">
                                    请输入正确的数字
                                </div>
                                <div *ngIf="scalingGroup.get('maximumInstances').hasError('max')">
                                    最大实例数至多为{{ scalingGroup.get('maximumInstances').errors.max.max }}台
                                </div>
                            </div>
                            <div class="form-hint error" *ngIf="(scalingGroup.errors && scalingGroup.errors.maxInstanceError)">
                                最大实例数必须大于最小实例数
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        负载均衡
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label for="">
                                <span class="label-text">模式选择</span>
                                <nz-select style="margin-right: 10px; margin-bottom: 10px;"
                                    (ngModelChange)="chooseloadBalancerVirutalServerId($event)"
                                    formControlName="loadBalancerVirutalServerId" 
                                    nzPlaceHolder="请选择负载均衡"
                                    [nzLoading]="isRefreshing">
                                    <nz-option *ngFor="let item of initData.loadBalancerList" 
                                        [nzValue]="item.id"
                                        [nzLabel]="item.name"></nz-option>
                                </nz-select>
                                <app-refresh-btn (refresh)="refreshLb();"></app-refresh-btn>
                                <span class="samll tip">创建后请点击以刷新负载均衡列表</span>
                            </label>
                            <div class="label-padding">
                                <a nz-button nzType="default" target="_blank"
                                    routerLink="/console/load-balance/load-balance-config">
                                    <i nz-icon nzType="plus" nzTheme="outline"></i>
                                    创建新负载均衡模式
                                </a>
                            </div>
                            <div *ngIf="isInvalid(scalingGroup.get('loadBalancerVirutalServerId'))"
                                class="form-hint error mt10">
                                <div *ngIf="scalingGroup.get('loadBalancerVirutalServerId').hasError('required')">
                                    请选择或创建负载均衡
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section">
                    <div class="field-title">
                        伸缩策略
                    </div>
                    <div class="field-group">
                        <div class="field-item required">
                            <label for="">
                                <span class="label-text">实例移除策略</span>
                                <nz-select style="width: 276px;"
                                    (ngModelChange)="chooseremovealPolicy($event)"
                                    formControlName="removealPolicy" 
                                    nzPlaceHolder="请选择">
                                    <nz-option *ngFor="let item of initData.autoScalingRemovePolicies "
                                        [nzValue]="item.name" 
                                        [nzLabel]="item.title"></nz-option>
                                </nz-select>
                                <div *ngIf="isInvalid(scalingGroup.get('removealPolicy'))"
                                class="form-hint error">
                                <div *ngIf="scalingGroup.get('removealPolicy').hasError('required')">
                                    请选择或创建负载均衡
                                </div>
                            </div>
                            </label>
                        </div>
                        <div class="field-item required">
                            <label>
                                <span class="label-text">默认冷却时间(秒)</span>
                                <input type="text" required formControlName="defaultCooldownTime" placeholder="请输入冷却时间">
                            </label>
                            <div *ngIf="isInvalid(scalingGroup.get('defaultCooldownTime'))" class="form-hint error">
                                <div *ngIf="scalingGroup.get('defaultCooldownTime').hasError('required')">
                                    冷却时间不能为空
                                </div>
                                <div *ngIf="scalingGroup.get('defaultCooldownTime').hasError('pattern')">
                                    请输入正确的冷却时间
                                </div>
                                <div *ngIf="scalingGroup.get('defaultCooldownTime').hasError('min')">
                                    冷却时间不能低于{{ scalingGroup.get('defaultCooldownTime').errors.min.min }}秒
                                </div>
                                <div *ngIf="scalingGroup.get('defaultCooldownTime').hasError('max')">
                                    冷却时间不能超过{{ scalingGroup.get('defaultCooldownTime').errors.max.max }}秒
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="field-section action">
                    <button *ngIf="!isEdit" type="button" nz-button [nzLoading]="isCreating" nzSize="large" nzType="primary"
                        (click)="createScalingGroup()">创建</button>
                    <button *ngIf="isEdit" [nzLoading]="isSaving" type="submint" nz-button nzSize="large"
                        nzType="primary" (click)="saveScalingGroup()">保存</button>
                </section>
            </form>

        </div>

        <div class="panel-aside pined">
            <!-- 组配置详情 -->
            <section class="field-section">
                <div class="field-title">
                    伸缩组信息
                </div>
                <table class="form-info">
                    <tbody>
                        <tr>
                            <td width="23%">监控规则</td>
                            <td>{{ titlea +"在  "+ titleb +"  "+ titlec +"  " || '-'}}
                                {{ utils.addUnit(scalingGroup.value.value2,showname || '-')}}
                            </td>
                        </tr>
                        <tr>
                            <td>名称</td>
                            <td>{{ scalingGroup.value.scalingGroupName || '-'}}</td>
                        </tr>
                        <tr>
                            <td>最小实例数</td>
                            <td>{{ addUnit(scalingGroup.value.minimumInstances, '台') }}</td>
                        </tr>
                        <tr>
                            <td>最大实例数</td>
                            <td>{{ addUnit(scalingGroup.value.maximumInstances, '台') }}</td>
                        </tr>
                        <tr>
                            <td>负载均衡</td>
                            <td>{{ loadname || '-' }}</td>
                        </tr>
                        <tr>
                            <td>实例移除策略</td>
                            <td>{{ removealPolicytitle || '-' }}</td>
                        </tr>
                        <tr>
                            <td>冷却时间</td>
                            <td>{{ addUnit(scalingGroup.value.defaultCooldownTime, '秒') }}</td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </div>
    </div>
</div>
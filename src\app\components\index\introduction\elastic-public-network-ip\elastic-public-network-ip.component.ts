import {Component, OnInit} from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-elastic-public-network-ip',
    templateUrl: './elastic-public-network-ip.component.html',
    styleUrls: ['./elastic-public-network-ip.component.less']
})
export class ElasticPublicNetworkIpComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '弹性公网IP',
        enName: 'Elastic Public Network IP',
        desc: '弹性公网IP（Elastic IP）提供独立的公网IP资源，包括公网IP地址与公网出口带宽服务。可以与弹性云服务器、负载均衡等资源灵活地绑定及解绑。可以满足各种业务场景的需要。',
        bgColor: '#1a284d',
        orderLink: '/console/elastic-pub-network-ip/public-network-config',
        type: 'ElasticPublicIpProduction',
    }

    ngOnInit() {}

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}



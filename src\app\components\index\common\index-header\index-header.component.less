@import '../../../../style/common/_variable.less';

@min-nav-item-width: 60px;

.index-header {
    background-color: @header-bg;
    height: @header-height;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;

    nav {
        line-height: @header-height;
        .brand {
            display: inline-block;
            margin-right: 40px;

            * {
                line-height: @header-height;
                vertical-align: top;
            }

            .main-logo {
                display: inline-block;
                width: 140px;
                height: 28px;
                vertical-align: middle;
                background: url(src/assets/images/logo.png) 0 0 no-repeat;
                background-size: contain;
            }

            .divider {
                display: inline-block;
                border-right: 1px solid #666;
                height: 32px;
                margin: 0 25px;
                vertical-align: middle;
            }

            .app-logo {
                vertical-align: middle;
                display: inline-block;
                width: 36px;
                height: 36px;
                background: url(src/assets/images/logo-icon.png) 0 0 no-repeat;
                background-size: contain;
            }

            .app-name {
                display: inline-block;
                margin-left: 10px;
                font-size: 16px;
                color: #fff;
                color: rgba(255, 255, 255, 0.9);
            }
        }

        .nav-list-container {
            display: inline-block;
            position: relative;
            
            .indicator {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                transform: translateX(0);
                width: @min-nav-item-width;
                height: 2px;
                background-color: @primary;
                transition: all .3s ease 0s;
                pointer-events: none;
            }
        }

        .nav-list {
            padding: 0;
            margin: 0;
            display: inline-block;
            font-size: 0;
            vertical-align: top;
            height: @header-height;

            .nav-item {
                display: inline-block;
                color: #fff;
                font-size: 14px;
                vertical-align: top;

                &.active {
                    >a, >div>a {
                        color: @primary;
                    }
                }

                >a, >div>a, .dropdown-toggle, .username {
                    display: inline-block;
                    height: 100%;
                    padding: 0 16px;
                    min-width: @min-nav-item-width;
                    color: #fff;
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 14px;
                    transition: color .3s ease 0s;
                    position: relative;

                    &:hover {
                        color: @primary;
                    }
                }

                .on-dropdown {
                    .on-dropdown-menu {
                        background-color: fadeout(@header-bg, 30%);
                        backdrop-filter: blur(8px);
                        .on-dropdown-item {
                            &.active {
                                a {
                                    color: @primary;
                                }
                            }

                            &:hover {
                                background-color: transparent;
                            }
                            
                            a {
                                color: #fff;
                                color: rgba(255, 255, 255, 0.9);

                                &:hover {
                                    color: @primary;
                                }
                            }
                        }

                        &.extend {
                            width: 350px;
                            font-size: 0;
                            line-height: 40px;
                            padding: 10px 15px;
                            .on-dropdown-item {
                                width: 100% / 3;
                                display: inline-block;
                            }
                        }
                    }
                }
            }

            &.nav-right {
                float: right;
                
                &:before {
                    content: '';
                    display: inline-block;
                    height: 18px;
                    border-left: 1px solid #666;
                    vertical-align: middle;
                    margin-right: 5px;
                }
                
                .nav-item {
                    >a:hover {
                        color: @primary;
                    }
                }
            }
        }
    }

    .register {
        a {
            background-color: @primary;
            color: #fff;

            &:hover {
                color: #fff!important;
            }
        }
    }
}
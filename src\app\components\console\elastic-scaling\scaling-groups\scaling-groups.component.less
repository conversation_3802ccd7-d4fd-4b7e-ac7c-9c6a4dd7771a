@import '../../../../style/common/_variable.less';

.power-on-btn {
    display: inline-block;
    margin-left: 14px;
    padding-left: 14px;
    position: relative;
    line-height: 19px;

    &:before {
        content: '';
        position: absolute;
        left: 0;
        height: 16px;
        top: 50%;
        margin-top: -16px / 2;
        border-left: 1px solid @light-border;
    }
}

.small.tip {
    color: #999;
    margin-left: 10px;
}

.monitor-info {
    tbody {
        tr {
            td {
                padding: 10px 0;
            }

            .th {
                font-weight: bold;
                color: #555;
                width: 140px;
            }
        }
    }
}
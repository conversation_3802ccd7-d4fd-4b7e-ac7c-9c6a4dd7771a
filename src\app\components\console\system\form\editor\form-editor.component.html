<!-- 表单编辑器 -->
<div class="form-editor-container">
    <!-- 顶部工具栏 -->
    <div class="form-toolbar">
        <div class="toolbar-left">
            <button nz-button nzType="text" (click)="goBack()" class="back-btn">
                <i nz-icon nzType="arrow-left"></i>
                返回
            </button>
            <span class="divider">|</span>
            <div class="form-title-editor">
                <span class="form-title-label">表单名称：</span>
                <input
                    nz-input
                    [(ngModel)]="formName"
                    placeholder="请输入表单名称"
                    class="form-title-input"
                    (blur)="onFormNameBlur()"
                    (keyup.enter)="onFormNameEnter($event)"
                    maxlength="50">
            </div>
        </div>

        <div class="toolbar-right">
<!--            <button nz-button nzType="default" (click)="manualTranslate()" nz-tooltip="手动汉化">-->
<!--                <i nz-icon nzType="translation"></i>-->
<!--                汉化-->
<!--            </button>-->
<!--            <button nz-button nzType="default" (click)="importJson()" nz-tooltip="导入JSON" style="margin-left: 8px;">-->
<!--                <i nz-icon nzType="upload"></i>-->
<!--                导入-->
<!--            </button>-->
<!--            <button nz-button nzType="default" (click)="downloadJson()" nz-tooltip="下载JSON" style="margin-left: 8px;">-->
<!--                <i nz-icon nzType="download"></i>-->
<!--                导出-->
<!--            </button>-->
            <button nz-button nzType="default" (click)="preview()" nz-tooltip="预览表单" style="margin-left: 8px;">
                <i nz-icon nzType="eye"></i>
                预览
            </button>
            <button nz-button nzType="default" (click)="reset()" nz-tooltip="重置表单" style="margin-left: 8px;">
                <i nz-icon nzType="reload"></i>
                重置
            </button>
            <button nz-button
                    nzType="primary"
                    (click)="save()"
                    style="margin-left: 16px;"
                    [disabled]="saving">
                <i nz-icon nzType="save" [nzSpin]="saving"></i>
                {{ saving ? '保存中...' : '保存' }}
            </button>
        </div>
    </div>

    <!-- 表单编辑器主体 -->
    <div class="form-editor-main" [class.loading]="loading">
        <!-- form-js编辑器容器 -->
        <div class="form-editor-content" #formContainer>
            <!-- form-js会在这里自动渲染完整的编辑器界面，包括：
                 - 左侧工具面板 (Palette)
                 - 中间画布区域 (Canvas)
                 - 右侧属性面板 (Properties Panel)
            -->
        </div>

        <!-- 加载遮罩 -->
        <div class="loading-mask" *ngIf="loading">
            <nz-spin nzSize="large" nzTip="加载中..."></nz-spin>
        </div>
    </div>
</div>
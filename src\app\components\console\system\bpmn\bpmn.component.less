/*自有样式*/
.operation-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin: 0 5px;
}

.operation-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: #808080;
  background-size: 100% 100%;
}

.able-icon:hover {
  color: #6EB8FF;
}

.disabled-btn:hover {
  color: #aaa;
}

.disabled-btn a{
  color: #aaa;
  cursor: not-allowed;
}

/*状态样式*/
.status-icon {
  display: inline-block;
  margin-right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
}

.fa-spin {
  margin-right: 5px;
}

.loading-status {
  color: #97a4b6;
}

.run-status {
  color: #45bb79;
}

.run-icon {
  background-color: #45bb79;
  border: 1px solid #45bb79;
}

.error-status {
  color: #ec5960;
}

.error-icon {
  background-color: #ec5960;
  border: 1px solid #ec5960;
}

.stop-status {
  color: #f5a623;
}

.stop-icon {
  background-color: #f5a623;
  border: 1px solid #f5a623;
}

.draft-status {
  color: #d9d9d9;
}

.draft-icon {
  background-color: #d9d9d9;
  border: 1px solid #d9d9d9;
}

/* 配置弹窗样式 */
.config-content {
  .modal-footer {
    text-align: right;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e8e8e8;

    button {
      margin-left: 8px;
    }
  }
}

/* 表格操作按钮样式 */
.on-table-actions {
  display: flex;
  align-items: center;
  gap: 8px;

  .on-table-action-item {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }

    .icon {
      font-size: 16px;
      color: #666;

      &:hover {
        color: #1890ff;
      }
    }
  }
}

/* 流程状态标签样式 */
:host ::ng-deep {
  .ant-tag {
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    line-height: 1.5;
  }
}

/* 搜索框样式 */
.on-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .right-button-group {
    .pull-right {
      .primary {
        background-color: #1890ff;
        border-color: #1890ff;

        &:hover {
          background-color: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
}

/* 表格标题样式 */
.action-bar {
  margin-bottom: 16px;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

/* 表格样式优化 */
:host ::ng-deep {
  .ant-table {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 500;
      color: #262626;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: middle;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  .ant-pagination {
    margin-top: 16px;
    text-align: right;
  }
}

/* 加载状态样式 */
.loading-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;

  .ant-spin {
    font-size: 24px;
  }
}

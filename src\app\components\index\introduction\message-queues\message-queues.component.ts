import { Component, OnInit } from '@angular/core';
declare let $: any;

@Component({
    selector: 'app-message-queues',
    templateUrl: './message-queues.component.html',
    styleUrls: ['./message-queues.component.less']
})
export class MessageQueuesComponent implements OnInit {

    constructor(
    ) {}

    intro = {
        title: '消息队列(Kafka)',
        enName: 'AIC Message Queue(Kafka)',
        desc: '消息队列(Kafka)是一个分布式的、高吞吐量、低延迟、高可扩展性消息队列服务。100%兼容开源 Kafka API（2.0.X及以上），帮助用户快速尚云。作为消息中间件，帮助用户实现消息生产者与消费者之间的解耦，无需彼此等待。',
        bgColor: '#1a284d',
        orderLink: '/console/message-queues/kafka',
        type: 'MQProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

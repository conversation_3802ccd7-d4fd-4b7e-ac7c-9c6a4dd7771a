<div class="on-container">
    <aside class="help-content">
        <div class="help-section-list">
            <p class="help-menu-title">操作指南</p>
            <ul nz-menu [nzMode]="'inline'" style="width: 200px;">
                <ng-container *ngTemplateOutlet="menuTpl; context: { $implicit: menus }"></ng-container>
                <ng-template #menuTpl let-menus>
                    <ng-container *ngFor="let menu of menus">
                        <li nz-menu-item (click)="(this.content = menu.content)" [nzPaddingLeft]="menu.level * 24"
                            *ngIf="!menu.children" [nzSelected]="menu.selected">
                            <span title>
                                <span>{{ menu.title }}</span>
                            </span>
                        </li>
                        <li nz-submenu [nzPaddingLeft]="menu.level * 24" *ngIf="menu.children" [nzOpen]="menu.open">
                            <span title>
                                <span>{{ menu.title }}</span>
                            </span>
                            <ul>
                                <ng-container *ngTemplateOutlet="menuTpl; context: { $implicit: menu.children }">
                                </ng-container>
                            </ul>
                        </li>
                    </ng-container>
                </ng-template>
            </ul>
        </div>
    </aside>
    <main>
        <section class="help-main-content">
            <!--云服务器-->
            <!--云服务器-实例-->
            <article *ngIf="content === 'production'">
                <div class="article-content">
                    <div class="original-content">
                        <span class="p-title">产品概述——云服务器</span>
                        <p class="p-indent">云服务器 是在云中提供可扩展的计算服务，避免了使用传统服务器时需要预估资源用量及前期投入的情况。通过云服务器，
                            您可以在短时间内快速启动任意数量的云服务器并即时部署应用程序，
                            云服务器支持用户自定义一切资源：CPU、内存、硬盘、网络、安全等等，并可在访问量和负载等需求发生变化时轻松地调整它们。
                        </p>
                        <p class="step-text">主要功能：</p>
                        <ul class="article-ul">
                            <li>
                                <p>云盘：将高可用性和持久性块存储附加到您的云服务器实例。</p>
                            </li>
                            <li>
                                <p>快照功能：随时随地的回滚到制定快照时刻状态。将繁琐应用程序部署变得更高效。</p>
                            </li>
                            <li>
                                <p>弹性公网IP：您可以将弹性公网IP与账户下的某台云服务器实例绑定，当某个实例发生故障时，
                                    您可以快速将此IP从故障的实例解绑，绑定到正常的实例上，保证业务的可用性。</p>
                            </li>
                            <li>
                                <p>负载均衡：一种高度可用的完全管理服务，开启即用。帮助您在基础架构中分配流量，以增加应用程序的可用性。</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--云服务器-创建实例-->
            <article *ngIf="content === 'cloudServerCreate'">
                <p class="p-title">创建实例——云服务器</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">前提条件:</p>
                        <p style="margin-top: 10px">在创建云服务器实例前，您需要完成以下工作:</p>
                        <ul class="article-ul">
                            <li>
                                <p>注册平台账号并登录</p>
                            </li>
                            <li>
                                <p>需要创建一个专有网络(VPC)，并且在 VPC 下创建一个子网。</p>
                            </li>
                            <li>
                                <p>若需绑定弹性公网IP则需要创建弹性公网IP并进行对应云服务器的绑定</p>
                            </li>
                            <li>
                                <p>若云服务器的登录凭证采用密钥对的方式则需要先创建密钥对</p>
                            </li>
                        </ul>
                        <p class="margin-text red-text">
                            注意事项：云服务器创建成功，根据安全原因，默认是不可以被访问和访问其他网络资源(例如：虚拟机配置弹性公网后无法访问外网和通过Linux SSH登陆虚拟机)，如果有这方面需求，请前往安全组设置相应访问规则。
                        </p>
                        <div>
                            <p class="title-l2">步骤:</p>
                            <p class="step-text">1. 进入创建云服务器页面</p>
                            <span>提供两种进入创建云服务器页面的入口：</span>
                            <div>
                                <p class="margin-text">
                                    （1）进入官网云服务器页面，点击【立即购买】按钮
                                </p>
                                <img src="./assets/manual-images/001001.png" class="step-img">
                                <p class="margin-text">
                                    （2）进入控制台云服务器页面，点击【创建云服务器】按钮。
                                </p>
                                <img src="./assets/manual-images/001002.png" class="step-img">
                            </div>
                            <p class="step-text">2. 选择计费方式</p>
                            <img src="./assets/manual-images/001003.png" class="step-img">
                            <p class="step-text">3.选择配置</p>
                            <img src="./assets/manual-images/001004.png" class="step-img">
                            <p class="step-text">4.选择镜像</p>
                            <img src="./assets/manual-images/001005.png" class="step-img">
                            <p class="step-text">5.设置存储空间(系统盘空间固定,可设置数据盘空间)</p>
                            <img src="./assets/manual-images/001006.png" class="step-img">
                            <p class="step-text">6.选择网络</p>
                            <img src="./assets/manual-images/001007.png" class="step-img">
                            <p class="step-text">7.配置弹性公网IP</p>
                            <img src="./assets/manual-images/001008.png" class="step-img">
                            <p class="step-text">8.设置登录凭证，可采用密码和密钥对两种方式登录</p>
                            <img src="./assets/manual-images/001009.png" class="step-img">
                            <img src="./assets/manual-images/001010.png" class="step-img">
                            <p class="step-text">9.填写实例信息</p>
                            <img src="./assets/manual-images/001011.png" class="step-img">
                            <p class="step-text">10.设置购买数量</p>
                            <img src="./assets/manual-images/001012.png" class="step-img">
                            <p class="step-text">11.查看配置的实例信息,点击【创建实例】按钮创建实例</p>
                            <img src="./assets/manual-images/001013.png" class="step-img">
                        </div>
                    </div>
                </div>
            </article>
            <!--云服务器-登录实例-->
            <article *ngIf="content === 'cloudServerLogin'">
                <p class="p-title">登录实例——云服务器</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">前提条件:</p>
                        <ul class="article-ul">
                            <li>
                                <p>使用密码登录需要使用管理员账号(root)及设置的密码</p>
                            </li>
                            <li>
                                <p>若密钥登录的方式则需要先创建 SSH 密钥、下载私钥、绑定 Linux 云服务器,下载及安装远程登录软件(本示例中使用Xshell)</p>
                            </li>
                        </ul>
                        <div>
                            <p class="title-l2">步骤:</p>
                            <p class="step-text">使用密码登录云服务器</p>
                            <p class="margin-text">（1）点击云服务器实例中【访问】按钮进入云桌面</p>
                            <img src="./assets/manual-images/002001.png" class="step-img">
                            <p class="margin-text">（2）进入云桌面点击【Not listed?】按钮后使用账号及密码登录</p>
                            <img src="./assets/manual-images/002002.png" class="step-img">
                            <img src="./assets/manual-images/002003.png" class="step-img">
                            <p class="step-text">使用密钥登录云服务器</p>
                            <p class="margin-text">（1） 进入控制台密钥对页面，点击【下载】按钮下载私有密钥到本地</p>
                            <img src="./assets/manual-images/001014.png" class="step-img">
                            <br>
                            <p class="margin-text">（2） 打开远程登录软件(Xshell)导入密钥</p>
                            <img src="./assets/manual-images/001015.png" class="step-img">
                            <br>
                            <p class="margin-text">（3） 使用远程登录软件(Xshell)登录时选择密钥登录方法</p>
                            <img src="./assets/manual-images/001016.png" class="step-img">
                        </div>
                    </div>
                </div>
            </article>
            <!--云服务器-操作实例-->
            <article *ngIf="content === 'cloudServerOperate'">
                <p class="p-title">操作实例——云服务器</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">步骤:</p>
                        <p class="step-text">一、开启云服务器</p>
                        <p class="margin-text">（1）点击【开机】按钮开启云服务器实例</p>
                        <img src="./assets/manual-images/003003.png" class="step-img">
                        <p class="step-text">二、重启云服务器</p>
                        <p class="margin-text">（1）点击【重启】按钮重启云服务器实例</p>
                        <img src="./assets/manual-images/003002.png" class="step-img">
                        <p class="step-text">三、关闭云服务器</p>
                        <p class="margin-text">（1）点击【关机】按钮关闭云服务器实例</p>
                        <img src="./assets/manual-images/003001.png" class="step-img">
                        <p class="step-text">四、查看云服务器用量</p>
                        <p class="margin-text">（1）点击【性能】按钮查看云服务器CPU使用率、磁盘读写、内存使用率</p>
                        <img src="./assets/manual-images/003009.png" class="step-img">
                        <img src="./assets/manual-images/003004.png" class="step-img">
                        <img src="./assets/manual-images/003005.png" class="step-img">
                        <img src="./assets/manual-images/003006.png" class="step-img">
                        <img src="./assets/manual-images/003007.png" class="step-img">
                        <p class="step-text">五、访问服务器</p>
                        <p class="margin-text">（1）点击【访问】按钮使用云桌面登录云服务器实例</p>
                        <img src="./assets/manual-images/003008.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云服务器-快照-->
            <article *ngIf="content === 'cloudServerSnapshot'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——快照</p>
                        <p class="p-indent">快照，指某一个时间点上某一个磁盘的数据备份。</p>
                        <p class="step-text">快照作为一种便捷高效的数据保护服务手段，推荐应用于以下业务场景中：</p>
                        <ul class="article-ul">
                            <li>
                                <p>系统盘、数据盘的日常备份，您可以利用快照定期的对重要业务数据进行备份，来应对误操作、攻击、病毒等导致的数据丢失风险。</p>
                            </li>
                            <li>
                                <p>更换操作系统，应用软件升级或业务数据迁移等重大操作前，您可以创建一份或多份数据快照，一旦升级、迁移过程中出现任何问题，可以通过数据快照及时恢复到正常的系统数据状态。
                                </p>
                            </li>
                            <li>
                                <p>生产数据的多副本应用，用户可以通过对生产数据创建快照，从而为数据挖掘、报表查询、开发测试等应用提供近实时的真实生产数据。</p>
                                <p><br></p>
                            </li>
                        </ul>
                        <p class="title-l2">相关操作:</p>
                        <p class="step-text">一、创建快照</p>
                        <p class="margin-text">（1） 进入控制台云服务器页面,点击快照选项进入快照页面</p>
                        <img src="./assets/manual-images/005001.png" class="step-img">
                        <p class="margin-text">（2） 点击【创建快照】进入快照创建页面</p>
                        <img src="./assets/manual-images/005002.png" class="step-img">
                        <p class="margin-text">（3） 选择需要创建快照的云服务器及填写快照名后点击【确认】按钮进行创建</p>
                        <img src="./assets/manual-images/005003.png" class="step-img">
                        <p class="step-text">二、还原快照(目前系统盘和每个数据盘只能创建一个快照)</p>
                        <p class="margin-text">快照创建完成后，您可以随时将您的云服务器还原至某一快照</p>
                        <img src="./assets/manual-images/005004.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云服务器-备份-->
            <article *ngIf="content === 'cloudServerBackup'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——备份</p>
                        <p class="p-indent">备份,是指针对于服务器所产生的数据信息进行相应的存储。<br><br></p>
                        <p class="step-text">备份作为一种便捷高效的数据保护服务手段，推荐应用于以下业务场景中：</p>
                        <ul class="article-ul">
                            <li>
                                <p>系统盘、数据盘的日常备份，您可以配置备份策略来对重要业务数据进行备份，来应对误操作、攻击、病毒等导致的数据丢失风险。</p>
                            </li>
                            <li>
                                <p>更换操作系统，应用软件升级或业务数据迁移等重大操作前，您可以创建一份或多份数据备份，一旦升级、迁移过程中出现任何问题，可以通过备份及时恢复到正常的系统数据状态。</p>
                            </li>
                            <li>
                                <p>生产数据的多副本应用，用户可以通过对生产数据创建备份，从而为数据挖掘、报表查询、开发测试等应用提供近实时的真实生产数据。</p>
                                <p><br></p>
                            </li>
                        </ul>
                        <p class="title-l2">相关操作:</p>
                        <p class="step-text">一、创建备份</p>
                        <p class="margin-text">（1） 进入控制台云服务器页面,点击备份选项进入备份页面</p>
                        <img src="./assets/manual-images/006001.png" class="step-img">
                        <p class="margin-text">（2） 点击【创建备份】进入备份创建页面</p>
                        <img src="./assets/manual-images/006002.png" class="step-img">
                        <p class="margin-text">（3） 选择需要创建备份的云服务器及填写备份名或自定义备份策略后点击【确认】按钮进行创建</p>
                        <img src="./assets/manual-images/006003.png" class="step-img">
                        <img src="./assets/manual-images/006004.png" class="step-img">
                        <p class="step-text">二、还原备份</p>
                        <p class="margin-text">备份创建完成后，您可以随时通过某一备份来还原对应的云服务器实例</p>
                        <img src="./assets/manual-images/006005.png" class="step-img">
                        <img src="./assets/manual-images/006006.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云服务器-监控-->
            <article *ngIf="content === 'cloudServerMonitor'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——监控</p>
                        <p class="p-indent">
                            监控是一项针对云服务器实例，通过自定义的监控规则对其CPU使用率、内存使用率、磁盘读写及时预警发现故障的手段，并通过邮件或者站内通知两种方式及时告知预警信息
                            事后提供翔实的数据用于追查定位问题的云服务，监控系统是整个运维环节，乃至整个产品生命周期中最重要的一环。使用云监控服务，可以全面了解云服务器实例的资源使用情况、性能和运行状况。借助报警服务，可以及时的感知故障并做出反应，保证应用程序顺畅运行。
                        </p>
                        <p class="step-text">监控推荐应用于以下业务场景中：</p>
                        <ul class="article-ul">
                            <li>
                                <p>基础指标监控：通过监控云服务器实例的CPU、内存、磁盘读写等基础指标，确保实例在高负载的情况下能及时被用户感知，避免导致业务无法正常运转。</p>
                            </li>
                            <li>
                                <p>服务异常通知：根据用户配置的告警策略，在监控数据符合告警条件后，用户会及时收到异常通知。</p>
                            </li>
                            <li>
                                <p>异常定位：在异常发生后，根据历史监控数据及事件监控，快速确定故障原因。</p>
                            </li>
                        </ul>
                        <p class="title-l2">相关操作:</p>
                        <p class="step-text">一、新建监控规则</p>
                        <p class="margin-text">（1）进入控制台云服务器页面,点击监控选项进入监控页面</p>
                        <img src="./assets/manual-images/007001.png" class="step-img">
                        <p class="margin-text">（2）点击【新建监控规则】进入新建监控规则页面</p>
                        <img src="./assets/manual-images/007002.png" class="step-img">
                        <p class="margin-text">（3）配置监控规则、选择资源范围、填写规则名称、选择报警方式后点击【确认】按钮进行创建</p>
                        <img src="./assets/manual-images/007003.png" class="step-img">
                        <p class="step-text">二、修改监控规则</p>
                        <p class="margin-text">（1）点击监控实例的【编辑】按钮，对弹出框中的监控规则进行修改</p>
                        <img src="./assets/manual-images/007004.png" class="step-img">
                        <img src="./assets/manual-images/007005.png" class="step-img">
                        <p class="step-text">三、查看监控规则</p>
                        <p class="margin-text">（1）点击监控实例的【详情】按钮，在弹出框中查看监控规则</p>
                        <img src="./assets/manual-images/007006.png" class="step-img">
                        <img src="./assets/manual-images/007007.png" class="step-img">
                        <p class="step-text">四、删除监控规则</p>
                        <p class="margin-text">（1）点击监控实例的【删除】按钮，删除在监控实例</p>
                        <img src="./assets/manual-images/007008.png" class="step-img">
                        <p class="step-text">五、查看站内通知</p>
                        <p class="margin-text">（1）点击右上角账号，选择消息通知按钮查看预警信息</p>
                        <img src="./assets/manual-images/007009.png" class="step-img">
                        <img src="./assets/manual-images/007010.png" class="step-img">

                    </div>
                </div>
            </article>
            <!--云服务器-网络安全—密钥对-->
            <article *ngIf="content === 'cloudServerKeyPair'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——密钥对</p>
                        <p class="p-indent">本云平台允许使用公有密钥密码术加密和解密对于 Linux
                            实例的登录信息。公有密钥密码术使用公有密钥加密某个数据（如一个密码），然后收件人可以使用私有密钥解密数据。公有和私有密钥被称为密钥对。用户可以通过密钥对安全地与云服务器进行连接，是一种比常规密码更安全的登录云服务器的方式。
                            该平台只会存储公有密钥，您需要存储私有密钥。拥有您的私有密钥的任何人都可以解密您的登录信息，因此将您的私有密钥保存在一个安全的位置非常重要。
                        </p>
                        <p class="step-text">主要优势</p>
                        <ul class="article-ul">
                            <li>
                                <p>SSH 密钥登录认证更为安全可靠，可以杜绝暴力破解威胁。</p>
                            </li>
                            <li>
                                <p>SSH 密钥登录方式更简便，只需在控制台和本地客户端做简单配置即可远程登录实例，再次登录时无需再输入密码。</p>
                            </li>
                        </ul>
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、创建密钥对</p>
                        <p class="margin-text">（1） 进入控制台密钥对页面,点击网络安全下的密钥对选项进入安全组页面</p>
                        <img src="./assets/manual-images/400101.png" class="step-img">
                        <p class="margin-text">（2） 点击【新建密钥对】按钮在弹框中填写密钥对名称，点击【确认】按钮创建</p>
                        <img src="./assets/manual-images/400102.png" class="step-img">
                        <img src="./assets/manual-images/400103.jpg" class="step-img">
                        <p class="step-text">二、下载密钥对</p>
                        <p class="margin-text">（1） 点击【下载】按钮下载私有密钥到本地</p>
                        <img src="./assets/manual-images/400104.png" class="step-img">
                        <p class="step-text">三、删除密钥对</p>
                        <p class="margin-text">（2） 点击【删除】按钮删除该密钥对</p>
                        <img src="./assets/manual-images/400105.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云服务器-网络安全—安全组-->
            <article *ngIf="content === 'cloudServerSecurityGroup'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——安全组</p>
                        <p class="p-indent">安全组的设置用来管理云服务器是否可以被访问，您可以通过配置安全组的入方向和出方向规则，设置您的服务器是否可以被访问以及访问其他网络资源。
                            默认情况下，安全组的入站规则和出站规则如下：                            
                        </p>
                        <ul class="article-ul">
                            <li>
                                <p>为了数据安全，安全组的入方向规则为拒绝策略，禁止外部网络的远程访问。如果您需要您的云服务器被外部访问，则需要放通相应端口的入站规则。</p>
                            </li>
                            <li>
                                <p>安全组的出方向规则用于设置您的云服务器是否可以访问外部网络资源。出站规则默认为全部拒绝，您需要放通相应端口的出站规则来访问外部网络资源。</p>
                            </li>
                        </ul>
                        <p class="title-l2">常见场景</p>
                        <p class="step-text">场景一：允许SSH远程连接Linux云服务器</p>
                        <p class="margin-text">案例：您创建了一台 Linux 云服务器，并希望可以通过 SSH 远程连接到云服务器。
                            解决方法：添加安全组规则 时，在 “协议/应用” 中选择 “TCP”，开通22号协议端口，放通 Linux SSH 登录。
                            您还可以根据实际需求，放通全部 IP 或指定 IP（IP 段），配置可通过 SSH 远程连接到云服务器的 IP 来源。
                        </p>
                        <nz-table nzTemplateMode class="help-menu-table" [nzBordered]=true>
                            <thead>
                                <tr>
                                    <th>方向</th>
                                    <th>协议/应用</th>
                                    <th>IP源地址</th>
                                    <th>端口范围</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span>入方向</span></td>
                                    <td><span>TCP</span></td>
                                    <td>
                                        <p>全部IP：0.0.0.0/0</p>
                                        <p>指定IP：输入您指定的IP段</p>
                                    </td>
                                    <td>TCP：22</td>
                                </tr>
                            </tbody>
                        </nz-table>
                        <p class="step-text">场景二：允许特定云服务器SSH 登录外部 IP</p>
                        <p class="margin-text">案例：您希望只有特定网段下您创建的云服务器可以SSH 登录外部特定的 IP 地址。
                            解决方法：参考如下配置，添加安全组规则。
                        </p>
                        <nz-table nzTemplateMode class="help-menu-table" [nzBordered]=true>
                            <thead>
                                <tr>
                                    <th>方向</th>
                                    <th>协议/应用</th>
                                    <th>IP源地址</th>
                                    <th>端口范围</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span>出方向</span></td>
                                    <td><span>TCP</span></td>
                                    <td>
                                        <p>全部IP：0.0.0.0/0</p>
                                        <p>指定IP：输入您指定的IP段</p>
                                    </td>
                                    <td>TCP：22</td>
                                </tr>
                            </tbody>
                        </nz-table>
                            
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、创建安全组</p>
                        <p class="margin-text">（1） 进入控制台-云服务器-网络安全组页面,点击网络安全下的安全组选项进入安全组页面,点击【+ 创建安全组】按钮进入安全组创建页面</p>
                        <img src="./assets/manual-images/4-1.png" class="step-img">
                        <p class="margin-text">（2） 输入安全组名称、描述，选择专有网络，点击【+ 添加规则按钮】</p>
                        <img src="./assets/manual-images/4-2.png" class="step-img">
                        <p class="margin-text">（3） 配置入方向、出方向规则（也可后续配置），点击【创建】即可</p>
                        <img src="./assets/manual-images/4-3.png" class="step-img">
                        <p class="step-text">二、入方向、出方向规则操作</p>
                        <p class="margin-text">（1） 点击【详情】按钮进入安全组详情页</p>
                        <img src="./assets/manual-images/4-4.png" class="step-img">
                        <p class="margin-text">（2） 点击【+ 添加规则】按钮输入相关信息</p>
                        <img src="./assets/manual-images/4-5.png" class="step-img">
                        <p class="margin-text">（3） 入方向配置允许外部IP通过SSH登录该网段下的虚拟机</p>
                        <img src="./assets/manual-images/4-6.png" class="step-img">
                        <p class="margin-text">（4） 出方向配置运行在该网段下的指定虚拟机通过SSH访问外部IP</p>
                        <img src="./assets/manual-images/4-6.png" class="step-img">
                        <p class="margin-text">（5） 点击【编辑】【删除】按钮可对当前规则进行编辑、删除操作</p>
                    </div>
                </div>
            </article>
            <!--云盘-->
            <article *ngIf="content === 'cloudDiskProduction'">
                <header>
                </header>
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——云盘</p>
                        <p class="p-indent">
                            云盘为云服务器提供持久化块级数据存储服务。在创建云服务器实例时会分别生成数据盘和系统盘挂载到对应的云服务器实例中，您也可以动态地将云盘挂载到云服务器上，像对待普通的硬盘一样；也可以对挂载后的云盘进行分区、创建文件系统等操作，并实现数据的持久化存储。
                        </p>
                        <p class="step-text">云盘分类：</p>
                        <ul class="article-ul">
                            <li>
                                <p>系统盘：不可删除</p>
                            </li>
                            <li>
                                <p>数据盘：可删除</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--云盘-创建云盘-->
            <article *ngIf="content === 'cloudDiskCreate'">
                <header>
                </header>
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建云盘</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1） 进入控制台云服务器页面,点击存储下的云盘选项进入云盘页面</p>
                        <img src="./assets/manual-images/004001.png" class="step-img">
                        <p class="margin-text">（2） 点击【创建云盘】进入云盘创建页面</p>
                        <img src="./assets/manual-images/004002.png" class="step-img">
                        <p class="margin-text">（3） 选择计费方式、配置云盘名称及容量大小、选择绑定的云服务器后创建实例</p>
                        <img src="./assets/manual-images/004003.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云盘-操作实例-->
            <article *ngIf="content === 'cloudDiskOperate'">
                <p class="p-title">云盘操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、删除云盘</p>
                        <p class="margin-text">（1）系统盘不可删除，点击【删除】按钮删除数据盘</p>
                        <img src="./assets/manual-images/004004.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--弹性公网IP-->
            <article *ngIf="content === 'ElasticPublicIpProduction'">
                <header>
                </header>
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——弹性公网IP</p>
                        <p class="p-indent">弹性IP地址，是一个可以绑定到云服务器实例的静态IP地址。通过控制台您可以将弹性IP地址快速地解绑并重新绑定到任意一个云服务实例，从而屏蔽故障实例。
                            弹性IP地址是公网IPv4地址，在全球范围内都可访问。如果您的实例没有关联弹性IP地址，则可以将弹性IP地址与您的实例关联以启用与Internet的通信，例如，从本地计算机连接到您的云服务器实例。
                        </p>
                        <p class="step-text">主要功能：</p>
                        <ul class="article-ul">
                            <li>
                                <p>要使用弹性IP地址，您首先需要购买一个弹性IP，并与指定云服务器实例相关联。</p>
                            </li>
                            <li>
                                <p>没有关联任何弹性IP地址的云服务器实例没有公网IP地址（仅有内网IP地址），不能直接与Internet通信。</p>
                            </li>
                            <li>
                                <p>您可以解绑弹性IP地址与云服务器实例的关联，然后重新将此地址与其他实例关联</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--弹性公网IP—创建弹性公网IP-->
            <article *ngIf="content === 'ElasticPublicIpCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建弹性公网IP</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1） 进入控制台弹性公网IP页面，点击【创建弹性公网IP】按钮。</p>
                        <img src="./assets/manual-images/200101.png" class="step-img">
                        <p class="margin-text">（2） 选择计费方式</p>
                        <img src="./assets/manual-images/200102.png" class="step-img">
                        <p class="margin-text">（3） 选择带宽峰值</p>
                        <img src="./assets/manual-images/200103.png" class="step-img">
                        <p class="margin-text">（4） 选择计费周期</p>
                        <img src="./assets/manual-images/200104.png" class="step-img">
                        <p class="margin-text">（5） 绑定相关资源(只支持绑定云服务器实例,可创建后在绑定)</p>
                        <img src="./assets/manual-images/200105.png" class="step-img">
                        <p class="margin-text">（6） 查看配置的实例信息,点击【创建实例】按钮创建实例</p>
                        <img src="./assets/manual-images/200106.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--弹性公网IP—操作实例-->
            <article *ngIf="content === 'ElasticPublicIpOperate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">弹性公网IP操作</p>
                        <p class="step-text">一、绑定实例</p>
                        <p class="margin-text">（1）点击【绑定】按钮在弹出框中选定需要绑定实例</p>
                        <img src="./assets/manual-images/200202.png" class="step-img">
                        <img src="./assets/manual-images/200203.png" class="step-img">
                        <p class="step-text">二、解绑实例</p>
                        <p class="margin-text">（1）点击【解绑】按钮解绑实例，只能解绑云服务器实例</p>
                        <img src="./assets/manual-images/200201.png" class="step-img">
                        <p class="step-text">三、释放弹性公网</p>
                        <p class="margin-text">（1）点击【释放】按钮释放该弹性公网IP(需解绑实例后才可以释放弹性公网IP)</p>
                        <img src="./assets/manual-images/200204.png" class="step-img">
                        <p class="step-text">四、访问弹性公网</p>
                        <p class="margin-text">（1）通过弹性公网IP访问云服务器实例</p>
                    </div>
                </div>
            </article>
            <!--专有网络-->
            <article *ngIf="content === 'VPCProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——专有网络</p>
                        <p class="p-indent">专有网络VPC（Proprietary
                            Network）提供用户安全、隔离、IP地址可自定义配置的网络环境。您可以完全掌控自己的专有网络，包括申请弹性公网IP、创建子网、设置地址范围、子网段、设置安全组策略，部署云服务器、负载均衡等云服务资源。
                            此外您还可以通过专线/VPN等连接方式将VPC与传统数据中心互联互通，灵活整合资源。
                        </p>
                        <P class="step-text">主要功能：</P>
                        <ul class="article-ul">
                            <li>
                                <p>将一个或多个弹性公网IP地址连接到VPC中的某个实例，以便直接从Internet访问该实例。</p>
                            </li>
                            <li>
                                <p>将VPC的私有IP地址范围分割成一个或多个公有或私有子网，以便在VPC中运行应用程序和服务。</p>
                            </li>
                            <li>
                                <p>为VPC中的实例分配多个IP地址并连接多个弹性网络接口。将VPC安全组与云服务器实例进行关联。</p>
                            </li>
                        </ul>
                        <p class="step-text">在云平台可扩展基础设施中创建VPC，并从所选的任何范围中指定其私有IP地址范围，包括：</p>
                        <ul class="article-ul">
                            <li>
                                <p>10.0.0.0/8</p>
                            </li>
                            <li>
                                <p>**********/12</p>
                            </li>
                            <li>
                                <p>***********/16</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--专有网络—创建专有网络-->
            <article *ngIf="content === 'VPCCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建专有网络(VPC)</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1） 进入控制台专有网络页面，点击【创建专有网络】按钮。</p>
                        <img src="./assets/manual-images/101001.png" class="step-img">
                        <p class="margin-text">（2） 填写基本信息，输入VPC名称，从三个默认网段中，选择一个VPC网段</p>
                        <p class="margin-text">（3） 子网配置，为子网设置自定义网段，最后点击【立即创建】完成VPC网络的创建(暂无需配置DNS)</p>
                        <img src="./assets/manual-images/101002.png" class="step-img">

                    </div>
                </div>
            </article>
            <!--专有网络—操作实例-->
            <article *ngIf="content === 'VPCOperate'">
                <p class="p-title">专有网络操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、创建子网</p>
                        <p class="margin-text">（1）在专有网络(VPC)列表,点击【查看】按钮进入子网列表页。</p>
                        <img src="./assets/manual-images/101001.png" class="step-img">
                        <p class="margin-text">（2）点击【新建子网】弹出新建子网框</p>
                        <p class="margin-text">（3）子网配置，为子网设置自定义网段，最后点击【立即创建】完成VPC网络的创建(暂无需配置DNS)</p>
                        <img src="./assets/manual-images/101002.png" class="step-img">
                        <p class="step-text">二、在专有网络特定子网下创建云服务器实例</p>
                        <p class="margin-text">（1）点击【创建ECS】按钮在专有网络特定子网下创建云服务器实例</p>
                        <img src="./assets/manual-images/101003.png" class="step-img">
                        <p class="step-text">三、在专有网络特定子网下创建负载均衡实例</p>
                        <p class="margin-text">（1）点击【创建SLB】按钮在专有网络特定子网下创建负载均衡实例</p>
                        <img src="./assets/manual-images/101004.png" class="step-img">
                        <p class="step-text">四、删除子网</p>
                        <p class="margin-text">（1）点击【删除】按钮删除子网</p>
                        <img src="./assets/manual-images/101005.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--负载均衡-->
            <article *ngIf="content === 'loadBalanceProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——负载均衡</p>
                        <p class="p-indent">负载均衡(Load Balancing)是对多台 云服务器
                            进行流量分发的服务。负载均衡可以通过流量分发扩展应用系统对外的服务能力，通过消除单点故障提升应用系统的可用性。负载均衡器通过设置虚拟服务地址，将位于同一机房的多台云服务器实例虚拟成一个高性能、高可用的应用服务池；再根据应用指定的均衡算法，将来自客户端的网络请求分发到具体的云服务器实例中。
                        </p>
                        <p class="step-text">主要优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>负载均衡器可以通过流量分发，快速提高应用系统对外的服务能力；</p>
                            </li>
                            <li>
                                <p>隐藏实际服务端口，增强内部系统的安全性；</p>
                            </li>
                            <li>
                                <p>自动隔离异常状态的实例，消除服务单点故障，提升应用系统的可靠性。</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--负载均衡—创建负载均衡-->
            <article *ngIf="content === 'loadBalanceCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建负载均衡</p>
                        <p class="title-l2">前提条件：</p>
                        <p style="margin-top: 10px">在创建云服务器实例前，您需要完成以下工作:</p>
                        <ul class="article-ul">
                            <li>
                                <p>需要创建一个专有网络(VPC)，并且在 VPC 下创建一个子网。</p>
                            </li>
                            <li>
                                <p>需要创建弹性公网IP并且未绑定实例</p>
                            </li>
                        </ul>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台负载均衡页面，点击【创建负载均衡】按钮</p>
                        <img src="./assets/manual-images/300107.png" class="step-img">
                        <p class="margin-text">（2）选择弹性公网IP</p>
                        <img src="./assets/manual-images/300101.png" class="step-img">
                        <p class="margin-text">（3）填写实例名称</p>
                        <img src="./assets/manual-images/300102.png" class="step-img">
                        <p class="margin-text">（4）选择专有网络</p>
                        <img src="./assets/manual-images/300103.png" class="step-img">
                        <p class="margin-text">（5）设置监听规则,点击【新增监听】按钮增加监听规则</p>
                        <img src="./assets/manual-images/300104.png" class="step-img">
                        <p class="margin-text">（6）添加云服务器，点击【新增云服务器】按钮增加云服务器</p>
                        <img src="./assets/manual-images/300105.png" class="step-img">
                        <p class="margin-text">（7）查看配置的实例信息,点击【创建实例】按钮创建实例</p>
                        <img src="./assets/manual-images/300106.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--负载均衡—操作实例-->
            <article *ngIf="content === 'loadBalanceOperate'">
                <p class="p-title">负载均衡操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看HTTP及Session请求总数</p>
                        <p class="margin-text">（1） 点击【查看】按钮查看HTTP及Session请求总数</p>
                        <img src="./assets/manual-images/300201.png" class="step-img">
                        <p class="step-text">二、监听管理</p>
                        <p class="margin-text">（1） 点击【编辑】按钮可以对监听规则进行更改</p>
                        <img src="./assets/manual-images/300205.png" class="step-img">
                        <img src="./assets/manual-images/300206.png" class="step-img">
                        <p class="margin-text">（2） 点击【查看】按钮可更改健康检查信息及增删监听的服务器</p>
                        <img src="./assets/manual-images/300210.png" class="step-img">
                        <img src="./assets/manual-images/300207.png" class="step-img">
                        <img src="./assets/manual-images/300208.png" class="step-img">
                        <p class="margin-text">（3） 点击【删除】按钮可删除监听规则</p>
                        <img src="./assets/manual-images/300210.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--rds-->
            <article *ngIf="content === 'rdsProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">RDS</p>
                        <p class="p-indent">AIC应用托管平台提供云上关系型数据库（Cloud Relational Database
                            Service，简称云RDS），让用户可以更方便、快速使用高可用的关系型数据库服务，为用户提供免费的服务运维和管理工作，节省运维资源，支持灵活的规模扩展，适应高速的业务发展。
                            RDS支持业内熟知的MySQL数据库和MariaDB，我们各方面测试了MySQL和MariaDB的一些常用的版本，然后基于这些版本做了定制开发，提供了高可靠、高可用数据库服务，提供了双机热备、故障恢复、业务监控，安全隔离等特性，并且免费提供服务运维，用户可以快速、便捷的使用。创新性的开放了从库使用，将数据库的维护能力真正开放给DBA，使其借助RDS完成灵活、自助的高级设置。
                        </p>
                        <p class="step-text">主要优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>高可靠：提供多副本的持久备份存储，实时上传更新日志，保障数据安全；可以在一周内恢复到任意时间点的快照，可靠性达99.99999%。</p>
                            </li>
                            <li>
                                <p>高可用：提供自动HA能力，确保在出现物理硬件损坏时仍保持业务可用。</p>
                            </li>
                            <li>
                                <p>弹性灵活：一键创建， 支持基于需求的升降配。</p>
                            </li>
                            <li>
                                <p>价格低廉：无需采购、部署和运维费用，基于自有机房进行大规模部署，成本低廉。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    新生互联网/APP产品：云关系数据库可作为产品可靠的服务端数据库存储介质，快速便捷的拥有完整的数据库服务，集中精力在业务开发，为发展争分夺秒。
                                </p>
                            </li>
                            <li>
                                <p>
                                    高速发展的企业：企业迅速发展过程中，业务量快速增长，需要高性能的数据库服务支持，同时为应对不断增加的业务量，可使用一键创建功能快速扩展数据库，大幅提高查询能力。
                                </p>
                            </li>
                            <li>
                                <p>
                                    传统企业及转型中企业：被陈旧、繁琐、复杂的上一代数据库系统拖累，急于使用更好、更快、更稳定的新一代数据库服务，同时保留DBA灵活、高级的操作空间，助力企业转型。
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--rds—创建rds-->
            <article *ngIf="content === 'rdsCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建RDS</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台RDS页面，点击【创建RDS数据库】按钮</p>
                        <img src="./assets/manual-images/800001.png" class="step-img">
                        <p class="margin-text">（2）选择计费方式、数据库版本、存储空间、内存规格、网络，输入RDS名称后点击创建按钮即可创建RDS数据库</p>
                        <img src="./assets/manual-images/800002.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--rds—操作实例-->
            <article *ngIf="content === 'rdsOperate'">
                <p class="p-title">RDS操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看RDS详情</p>
                        <p class="margin-text">（1） 点击RDS首页【查看】按钮进入RDS详情页面</p>
                        <img src="./assets/manual-images/800003.png" class="step-img">
                        <p class="margin-text">（2） RDS详情页第一页为RDS基本信息，点击名称、字符集可修改对应项目</p>
                        <img src="./assets/manual-images/800004.png" class="step-img">
                        <img src="./assets/manual-images/800005.png" class="step-img">
                        <img src="./assets/manual-images/800006.png" class="step-img">
                        <p class="margin-text">（3） RDS详情页第二页为RDS用量信息，在此页可清晰地看到该RDS各项资源的用量详情，可选择不同时间段分区查看</p>
                        <img src="./assets/manual-images/800007.png" class="step-img">
                        <img src="./assets/manual-images/800008.png" class="step-img">
                        <p class="margin-text">（4.1）
                            RDS详情页第三页为RDS账号管理页，点击【创建账号】按钮即可新增账号，选择账号类型(高权限账号有该RDS数据库中所有数据库的读写操作权限，普通账户只能操作指定数据库)输入账户名称、密码，选择授权数据库(创建时非必选操作，后期可修改)，点击【确认】按钮即可创建新账户
                        </p>
                        <img src="./assets/manual-images/800009.png" class="step-img">
                        <img src="./assets/manual-images/800010.png" class="step-img">
                        <p class="margin-text">（4.2） 点击【修改权限】按钮可修改当前账户的数据库权限</p>
                        <img src="./assets/manual-images/800011.png" class="step-img">
                        <img src="./assets/manual-images/800012.png" class="step-img">
                        <p class="margin-text">（4.3） 若遗忘账户密码，可点击【重置密码】按钮重新设置该账户的登录密码</p>
                        <img src="./assets/manual-images/800013.png" class="step-img">
                        <img src="./assets/manual-images/800014.png" class="step-img">
                        <p class="margin-text">（4.4） 点击【删除】按钮可删除该账户</p>
                        <img src="./assets/manual-images/800015.png" class="step-img">
                        <p class="margin-text">（5.1）
                            RDS详情页第四页为RDS数据库管理页，点击【创建数据库】按钮即可新增数据库，输入数据库名称、选择字符集，点击【确定】按钮即可创建新数据库</p>
                        <img src="./assets/manual-images/800016.png" class="step-img">
                        <img src="./assets/manual-images/800017.png" class="step-img">
                        <p class="margin-text">（5.2） 点击【删除】按钮可删除该数据库</p>
                        <img src="./assets/manual-images/800018.png" class="step-img">
                        <p class="margin-text">（6） RDS详情页第五页为RDS备份页，点击【下载】按钮即可下载并查看该备份</p>
                        <img src="./assets/manual-images/800019.png" class="step-img">
                        <p class="margin-text">（7） RDS详情页第六页为RDS日志页，点击【下载】按钮即可下载并查看该日志，可通过MySQL相关工具恢复</p>
                        <img src="./assets/manual-images/800020.png" class="step-img">

                        <p class="step-text">二、启动/重启RDS</p>
                        <p class="margin-text">（1） 在非欠费情况下，点击RDS列表的【启动】按钮即可启动因欠费导致停止的RDS</p>
                        <img src="./assets/manual-images/800021.png" class="step-img">
                        <p class="margin-text">（2） 进入RDS详情页，点击【重启】按钮可重启RDS</p>
                        <img src="./assets/manual-images/800022.png" class="step-img">

                        <p class="step-text">三、删除RDS</p>
                        <p class="margin-text">（1） 点击列表中的【删除】按钮或进入详情页点击【删除】按钮可删除RDS</p>
                        <img src="./assets/manual-images/800023.png" class="step-img">
                        <img src="./assets/manual-images/800024.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--对象存储-->
            <article *ngIf="content === 'objectStorageProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——对象存储</p>
                        <p class="p-indent">OOS（AIC Object
                            Storage，AIC对象存储）是面向企业和开发者的具有高安全、高可靠、大容量、低成本等特点的对象存储产品，用于存储图片、音视频、文档等非结构化数据，并实现在线管理数据。存储空间可以根据业务系统承载情况按需使用、灵活伸缩。支持高并发访问，具有完备的API及SDK接口，帮助用户数据快速上云。
                        </p>
                        <p class="step-text">主要优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>价格低廉：无需采购、部署和运维费用，基于自有机房进行大规模部署，成本低廉。</p>
                            </li>
                            <li>
                                <p>按需扩展：存储空间可以根据业务系统承载情况按需扩展，系统可灵活扩容。</p>
                            </li>
                            <li>
                                <p>稳定可靠：对象存储数据以多副本形式保存，数据存储持久性（SLA）99.999999999%，服务可用性高达99.9%。</p>
                            </li>
                            <li>
                                <p>高安全性：严密的签名访问授权机制保证用户接入安全性；系统内建ACL机制保证数据访问安全性；传输过程支持SSL加密，保证数据传输安全性。</p>
                            </li>
                            <li>
                                <p>方便易用：提供RESTFul API、控制台、多种开发语言SDK等多种使用方式，您可以随时随地通过网络管理您的数据。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    通用场景：企业网盘、视频存储、医疗PACS、云硬盘归档等，对象存储提供海量文件的存储、访问和管理，包括多种格式的办公文档、文本、图片、XML、HTML、各类报表、音频和视频等。
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--对象存储—创建对象存储-->
            <article *ngIf="content === 'objectStorageCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建对象存储容器</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台对象存储页面，点击【创建容器】按钮</p>
                        <img src="./assets/manual-images/600001.png" class="step-img">
                        <p class="margin-text">（2）输入容器名称，名称仅允许包含小写字母、数字、-，需要以小写字母或数字开头和结尾、长度为3-63个字符</p>
                        <img src="./assets/manual-images/600002.png" class="step-img">
                        <p class="margin-text">（3）选择访问权限，权限有私有读写、公有读私有写和公有读写三种</p>
                        <img src="./assets/manual-images/600003.png" class="step-img">
                        <p class="margin-text">（4）点击【确定】按钮即可创建容器</p>
                    </div>
                </div>
            </article>
            <!--对象存储—操作实例-->
            <article *ngIf="content === 'objectStorageOperate'">
                <p class="p-title">对象存储操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看API认证</p>
                        <p class="margin-text">（1） 点击对象存储首页【API认证】按钮查看用户API认证</p>
                        <img src="./assets/manual-images/600004.png" class="step-img">
                        <p class="step-text">二、修改容器权限</p>
                        <p class="margin-text">（1） 点击【权限】按钮可以对容器的访问规则进行更改</p>
                        <img src="./assets/manual-images/600006.png" class="step-img">
                        <img src="./assets/manual-images/600007.png" class="step-img">
                        
                        <p class="step-text">三、删除容器</p>
                        <p class="margin-text">（1） 点击【删除】按钮可删除容器，请务必保证该容器为空！否则无法删除</p>
                        <img src="./assets/manual-images/600008.png" class="step-img">
                        <p class="step-text">四、容器详情</p>
                        <p class="margin-text">（1） 点击容器名称即可进入容器详情页，可以查看容器用量详情</p>
                        <img src="./assets/manual-images/600009.png" class="step-img">
                        <p class="step-text">五、文件管理</p>
                        <p class="margin-text">（1）
                            点击【上传文件】按钮，并选择访问权限，然后选择文件，点击【确定】即可上传文件，若容器未在本系统页面中创建，您需要前往控制台配置CORS规则才能成功上传文件！</p>
                        <img src="./assets/manual-images/600010.png" class="step-img">
                        <img src="./assets/manual-images/600011.png" class="step-img">
                        <p class="margin-text">（2）
                            配置CORS规则。前往S3cmd使用<code>s3cmd setcors FILE s3://BUCKET</code>命令配置，FILE为以下内容，BUCKET为对象存储容器名称。
                        </p>
                        <pre style="display: inline-block;margin-left: 10px;">{{corsConfigText}}</pre>
                        <p class="margin-text">（3） 点击【创建文件夹】按钮，输入文件夹名称，文件夹名称只允许输入中文、大小写字母、. 、- 、/
                            ，/ 用于分割路径，可快速创建子目录，但不能以/开头，不能存在连续的/
                            ，不能存在连续的 .
                            ，长度不能超过200个字符，点击【确定】即可创建文件夹</p>
                        <img src="./assets/manual-images/600012.png" class="step-img">
                        <img src="./assets/manual-images/600013.png" class="step-img">
                        <p class="margin-text">（4） 点击【查看详情】可查看文件相关信息，点击【下载】按钮可以下载当前文件</p>
                        <img src="./assets/manual-images/600014.png" class="step-img">
                        <img src="./assets/manual-images/600015.png" class="step-img">
                        <p class="margin-text">（5） 点击【编辑】按钮可修改当前文件权限</p>
                        <img src="./assets/manual-images/600016.png" class="step-img">
                        <img src="./assets/manual-images/600017.png" class="step-img">
                        <p class="step-text">六、跨域规则</p>
                        <p class="margin-text">（1） 在容器详情模块点击顶部菜单中的【跨域规则】进入跨域规则管理模块</p>
                        <img src="./assets/manual-images/600020.png" class="step-img">
                        <p class="margin-text">（2） 点击【+创建规则】按钮，输入相应内容，点击确定即可创建(来源最多16组，且每组最多包含一个“*”号，允许Headers和暴露Headers最多16组)</p>
                        <img src="./assets/manual-images/600021.png" class="step-img">
                        <img src="./assets/manual-images/600022.png" class="step-img">
                        <p class="margin-text">（3） 点击更多操作中的【修改】按钮，即可修改当前所选规则</p>
                        <img src="./assets/manual-images/600023.png" class="step-img">
                        <img src="./assets/manual-images/600024.png" class="step-img">
                        <p class="margin-text">（4） 点击更多操作中的【删除】按钮，即可删除当前所选规则</p>
                        <img src="./assets/manual-images/600025.png" class="step-img">
                        <p class="margin-text">（5） 点击【清空规则】按钮，即可删除当前容器内的所有规则</p>
                        <img src="./assets/manual-images/600026.png" class="step-img">
                        <p class="step-text">七、生命周期</p>
                        <p class="margin-text">（1） 在容器详情模块点击顶部菜单中的【生命周期】进入生命周期管理模块</p>
                        <img src="./assets/manual-images/600027.png" class="step-img">
                        <p class="margin-text">（2） 点击【+创建生命周期】按钮，输入相应内容，点击确定即可创建(其中文件过期策略和碎片过期策略必须启用一项)</p>
                        <img src="./assets/manual-images/600028.png" class="step-img">
                        <img src="./assets/manual-images/600029.png" class="step-img">
                        <p class="margin-text">（3） 点击更多操作中的【修改】按钮，即可修改当前所选生命周期</p>
                        <img src="./assets/manual-images/600030.png" class="step-img">
                        <img src="./assets/manual-images/600031.png" class="step-img">
                        <p class="margin-text">（4） 点击更多操作中的【删除】按钮，即可删除当前所选生命周期</p>
                        <img src="./assets/manual-images/600032.png" class="step-img">
                        <p class="margin-text">（5） 点击【清空生命周期】按钮，即可删除当前容器内的所有生命周期</p>
                        <img src="./assets/manual-images/600033.png" class="step-img">
                        <p class="step-text">八、静态网站托管服务</p>
                        <p class="margin-text">（1） 在容器详情模块点击顶部菜单中的【静态网站托管】进入静态网站托管管理模块</p>
                        <img src="./assets/manual-images/600034.png" class="step-img">
                        <p class="margin-text">（2） 点击【+创建静态网站托管】按钮，输入相应内容，点击确定即可创建(默认首页和默认404页均为非必填，目前最多创建一条静态网站托管)</p>
                        <img src="./assets/manual-images/600035.png" class="step-img">
                        <img src="./assets/manual-images/600036.png" class="step-img">
                        <p class="margin-text">（3） 点击更多操作中的【修改】按钮，即可修改当前所选静态网站托管</p>
                        <img src="./assets/manual-images/600037.png" class="step-img">
                        <img src="./assets/manual-images/600038.png" class="step-img">
                        <p class="margin-text">（4） 点击更多操作中的【删除】按钮，即可删除当前所选静态网站托管</p>
                        <img src="./assets/manual-images/600039.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--对象存储—文档下载-->
            <article *ngIf="content === 'objectStorageDonwload'">
                <p class="p-title">使用手册</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">文档下载</p>
                        <p class="step-text">一、SDK手册</p>
                        <p class="margin-text"><a href="/cloud/objectStorage/download/sdk01.pdf"
                                download="AIC应用托管平台对象存储服务SDK手册-C" class="download-link">（1） C 版</a>
                        </p>
                        <p class="margin-text"><a href="/cloud/objectStorage/download/sdk02.pdf"
                                download="AIC应用托管平台对象存储服务SDK手册-Go" class="download-link">（2） Go 版</a>
                        </p>
                        <p class="margin-text"><a href="/cloud/objectStorage/download/sdk03.pdf"
                                download="AIC应用托管平台对象存储服务SDK手册-Java" class="download-link">（3） Java 版</a>
                        </p>
                        <p class="margin-text"><a href="/cloud/objectStorage/download/sdk04.pdf"
                                download="AIC应用托管平台对象存储服务SDK手册-Python" class="download-link">（4） Python 版</a>
                        </p>
                        <p class="step-text">二、S3工具使用手册</p>
                        <p class="margin-text">
                            <a href="/cloud/objectStorage/download/s301.pdf"
                                download="AIC应用托管平台对象存储服务S3工具使用手册-Browser" class="download-link">（1） S3
                                Browser使用手册</a>
                        </p>
                        <p class="margin-text">
                            <a href="/cloud/objectStorage/download/s302.pdf" download="AIC应用托管平台对象存储服务S3工具使用手册-CMD"
                                class="download-link">（2） S3 CMD使用手册</a>
                        </p>
                        <p class="margin-text">
                            <a href="/cloud/objectStorage/download/s303.pdf" download="AIC应用托管平台对象存储服务S3工具使用手册-S3FS"
                                class="download-link">（3） S3FS用户手册</a>
                        </p>
                    </div>
                </div>
            </article>
            <!--对象存储—常见问题-->
            <article *ngIf="content === 'statisticalProblem'">
                <p class="p-title">统计显示问题</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、为什么文件管理页面统计的"当前容器容量"、"当前对象数"与文件列表的不符？</p>
                        <p class="margin-text indent-text">
                            如果有用户正在调用multi-upload
                            （分片上传）向当前容器中上传对象，在multi-upload完成合并操作之前，每个临时分片将被统计到"当前对象数"，每个分片的大小也会被统计到"当前容器容量"中；在multi-upload完成合并或者删除分片之后，"当前对象数"和"当前容器容量"数据恢复。
                        </p>
                    </div>
                </div>
            </article>
            <!--对象存储—其他问题-->
            <article *ngIf="content === 'otherProblem'">
                <p class="p-title">其他问题</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、什么是容器？</p>
                        <p class="margin-text indent-text">
                            容器是对象存储的容器。每个对象都必须存储在容器中，因此上传对象之前必须要先有容器。容器的标识就是容器的名称，因此容器的命名在对象存储对象存储系统中需要唯一存在，只要用户的账户存在容器就一直存在。一个容器只能被一个用户所拥有，一个用户可以最多拥有1000个容器。
                        </p>
                        <p class="step-text">二、什么是对象？</p>
                        <p class="margin-text indent-text">
                            对象是对象存储存储数据的实体，对象包含数据和元数据，元数据是由一系列name-value对构成，定义了对象的属性，其中包含了一些默认属性，例如最后一次修改时间，HTTP标准消息头ContentType等。上层应用可以在上传对象的时候提供可定义的元数据配置。
                        </p>
                        <p class="margin-text indent-text">
                            对象名在容器中是唯一的，对象的标识由容器名，对象名和版本号构成。因此任何一个对象存储中的对象的访问URL为：web服务地址+容器名称+对象名称+版本号（可选）。例如：http://hostname/bucket1/picture.jpg，“bucket1”容器名称，“picture.jpg”为对象名称。
                        </p>
                        <p class="step-text">三、什么是控制列表？</p>
                        <p class="margin-text indent-text">
                            控制列表（Access Control List）：用于对存储系统中可访问单元（对象或容器）的权限描述。访问控制由属主和权限组成。
                        </p>
                        <p class="margin-text indent-text">
                            属主包括（拥有者Owner、注册用户、指定用户、匿名用户）。
                        </p>
                        <p class="margin-text indent-text">
                            Owner为容器或对象的拥有者；注册用户是对象存储系统内的全部注册用户；指定用户是指由容器或对象拥有者指定授权的某些注册用户；匿名用户指无权属的任意用户。
                        </p>
                        <nz-table nzTemplateMode class="help-menu-table" [nzBordered]=true>
                            <thead>
                                <tr>
                                    <th>ACL宏</th>
                                    <th>容器级访问控制功能</th>
                                    <th>对象级访问控制功能</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>私有</td>
                                    <td>拥有者具有全部权限注册用户和匿名用户无访问权限</td>
                                    <td>拥有者具有全部权限（包括读取ACL策略）</td>
                                </tr>
                                <tr>
                                    <td>公共可读</td>
                                    <td>拥有者具有全部权限匿名用户具有读权限</td>
                                    <td>拥有者具有全部权限匿名用户可读取对象数据和元数据（除ACL策略）</td>
                                </tr>
                                <tr>
                                    <td>公共可读写</td>
                                    <td>拥有者具有全部权限匿名用户具有读写权限</td>
                                    <td>拥有者具有全部权限匿名用户可读取对象数据和元数据（除ACL策略）</td>
                                </tr>
                                <tr>
                                    <td>注册用户可读</td>
                                    <td>拥有者具有全部权限任意注册用户具有可读权限</td>
                                    <td>拥有者具有全部权限任意注册用户可读取对象数据和元数据（除ACL策略）</td>
                                </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </div>
            </article>
            <!--对象存储—静态网站功能说明-->
            <article *ngIf="content === 'staticWebFunctionDes'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">功能说明——对象存储静态网站托管</p>
                        <p class="p-indent">
                            您可以在OOS上托管静态网站。静态网站网页包含html、图片、音频等静态内容，也可能包含客户端执行的脚本，例如Javascript。OOS不支持依赖服务端处理的内容，例如PHP、JSP、ASP.NET等。
                        </p>
                        使用OOS的静态网站托管服务，可快速托管基于静态内容的网站，并提供高可用与高可靠的服务保障，大幅简化建站的操作流程，同时大幅降低网站的日常运营与维护成本。
                        <p class="indent"></p>
                    </div>
                </div>
            </article>
            <!--对象存储—静态网站使用说明-->
            <article *ngIf="content === 'staticWebUsageDes'">
                <p class="p-title">使用说明——对象存储静态网站托管</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、配置容器</p>
                        <p class="p-indent">（1） 要托管静态网站，您需要为网站托管创建容器，然后将网站内容上传到容器中。此容器必须拥有公共读取访问权限。</p>
                        <p class="step-text">二、配置静态网站</p>
                        <p class="p-indent">为了使您更方便地管理在 OOS 上托管的静态网站，OOS 支持三种功能：</p>
                        <ul class="article-ul">
                            <li>索引页面支持
                                <p class="p-indent">静态页是指当用户直接访问静态网站根域名或文件夹根路径时，OSS 返回的默认静态页（相当于网站的 index.html）。如果您为一个容器
                                    配置了静态网站托管模式，就必须指定一个索引页面。</p>
                            </li>
                            <li>错误页面支持
                                <p class="p-indent">错误页面是指在用户访问该静态网站时，如果遇到 HTTP 4XX 错误时（最典型的是 404 NOT FOUND 错误），OOS
                                    返回给用户错误页面。通过指定错误页面，您可以为您的用户提供恰当的出错提示。</p>
                            </li>
                            <li>网页重定向支持
                                <p class="p-indent">网页重定向是指当用户访问该静态网站的请求在满足指定条件时，返回301或302重定向至其他对象或者外部URL。</p>
                                <p class="p-indent">例如：设置索引页面为 index.html，错误页面为 error.html，容器 为 sample-bucket，Endpoint 为
                                    oos-cn-wuxi-website.heclouds.com，那么：</p>
                                <ol class="article-ol">
                                    <li>用户访问 http://sample-bucket.oos-cn-wuxi-website.heclouds.com ，相当于访问
                                        http://sample-bucket.oos-cn-wuxi-website.heclouds.com/index.html。</li>
                                    <li>用户访问 http://sample-bucket.oos-cn-wuxi-website.heclouds.com/photos/ ，相当于访问
                                        http://sample-bucket.oos-cn-wuxi-website.heclouds.com/photos/index.html。</li>
                                    <li>用户访问 http://sample-bucket.oos-cn-wuxi-website.heclouds.com/photos
                                        的时候，如果未找到photos对象，则将搜索索引文档photos/index.html，如果找到该文档，则返回http://sample-bucket.oos-cn-wuxi-website.heclouds.com/photos/index.html，否则返回http://sample-bucket.oos-cn-wuxi-website.heclouds.com/error.html。
                                    </li>
                                </ol>
                            </li>
                        </ul>
                        <p class="step-text">三、关闭静态网站</p>
                        <p class="p-indent">您可以根据需要关闭静态网站功能，容器的静态网站设置会被清除。关闭后如果想要启用静态网站功能，重新进行配置即可。</p>
                    </div>
                </div>
            </article>
            <!--对象存储—域名绑定-->
            <article *ngIf="content === 'staticWebDomainBind'">
                <p class="p-title">域名绑定——对象存储静态网站托管</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-indent">如果您想使用自己的域名来访问托管的静态网站，可以通过绑定自定义域名来实现。</p>
                        <p class="p-indent">假设已有域名example.com，希望使用OOS托管网站响应对 http://example.com（根域） 和
                            http://www.example.com（子域）的请求。</p>
                        <p class="step-text">配置步骤如下：</p>
                        <ol class="article-ol">
                            <li>
                                <p class="margin-text">
                                    创建容器，容器名为：example.com和www.example.com（与托管的网站名称相同），并设置两个容器权限为公共读取访问权限。</p>
                            </li>
                            <li>
                                <p class="margin-text">将网站数据上传至根域容器example.com。</p>
                            </li>
                            <li>
                                <p class="margin-text">配置容器example.com，启用静态网站托管，设置索引文档。</p>
                            </li>
                            <li>
                                <p class="margin-text">配置容器www.example.com重定向，将对其发起的全部请求重定向至容器example.com。</p>
                            </li>
                            <li>
                                <p class="margin-text">DNS配置CNAME记录。</p>
                            </li>
                        </ol>
                        <p class="p-indent">example.com. IN CNAME example.com.oos-cn-wuxi-website.heclouds.com.</p>
                        <p class="p-indent">www.example.com. IN CNAME www.example.com.oos-cn-wuxi-website.heclouds.com.
                        </p>
                    </div>
                </div>
            </article>
            <!--对象存储—配置示例-->
            <article *ngIf="content === 'staticWebExp'">
                <p class="p-title">配置示例——对象存储静态网站托管</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、基础静态网站配置</p>
                        <p class="margin-text">（1） 索引页面为 index.html，错误页面为 error.html</p>
                        <code>
<pre>
    &#60;WebsiteConfiguration xmlns='http://s3.amazonaws.com/doc/2006-03-01/'&#62;
        &#60;IndexDocument&#62;
            &#60;Suffix&#62;index.html&#60;/Suffix&#62;
        &#60;/IndexDocument&#62;
        &#60;ErrorDocument&#62;
            &#60;Key&#62;error.html&#60;/Key&#62;
        &#60;/ErrorDocument&#62;
    &#60;/WebsiteConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">二、配置重定向所有请求</p>
                        <p class="margin-text">（1） 将该静态网站的所有请求重定向至http://www.redirect.com</p>
                        <code>
<pre>
    &#60;WebsiteConfiguration xmlns='http://s3.amazonaws.com/doc/2006-03-01/'&#62;
        &#60;RedirectAllRequestsTo&#62;
            &#60;HostName&#62;www.redirect.com&#60;/HostName&#62;
        &#60;/RedirectAllRequestsTo&#62;
    &#60;/WebsiteConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">三、配置重定向规则</p>
                        <p class="margin-text">（1） 假设您的容器包含以下对象:</p>
                        <p class="p-indent">index.html</p>
                        <p class="p-indent">docs/article1.html</p>
                        <p class="p-indent">docs/article2.html</p>
                        <p class="p-indent">您决定将文件夹从 docs/ 重命名为 documents/。做出此更改后，您需要将对前缀 docs/ 的请求重定向至 documents/。例如，对
                            docs/article1.html 的请求将重定向至 documents/article1.html。</p>
                        <p class="p-indent">在这种情况下，您可以将以下路由规则添加到网站配置：</p>
                        <code>
<pre>
    &#60;WebsiteConfiguration xmlns='http://s3.amazonaws.com/doc/2006-03-01/'&#62;
        &#60;IndexDocument&#62;
            &#60;Suffix&#62;index.html&#60;/Suffix&#62;
        &#60;/IndexDocument&#62;
        &#60;ErrorDocument&#62;
            &#60;Key&#62;error.html&#60;/Key&#62;
        &#60;/ErrorDocument&#62;
        &#60;RoutingRules&#62;
            &#60;RoutingRule&#62;
            &#60;Condition&#62;
            &#60;KeyPrefixEquals&#62;docs/&#60;/KeyPrefixEquals&#62;
            &#60;/Condition&#62;
            &#60;Redirect&#62;
            &#60;ReplaceKeyPrefixWith&#62;documents/&#60;/ReplaceKeyPrefixWith&#62;
            &#60;/Redirect&#62;
            &#60;/RoutingRule&#62;
        &#60;/RoutingRules&#62;
    &#60;/WebsiteConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">四、为 HTTP 错误进行重定向</p>
                        <p class="margin-text">（1） 可以为不同的HTTP错误码设置特定的路由规则。重定向请求插入对象键前缀
                            report-404/。例如请求页面examplePage.html并且它导致了HTTP 404错误，请求就重定向至
                            http://www.redirect.com/report-404/examplePage.html，如果没有配置这个路由规则，则返回error.html</p>
                        <code>
<pre>
    &#60;WebsiteConfiguration xmlns='http://s3.amazonaws.com/doc/2006-03-01/'&#62;
        &#60;IndexDocument&#62;
            &#60;Suffix&#62;index.html&#60;/Suffix&#62;
        &#60;/IndexDocument&#62;
        &#60;ErrorDocument&#62;
            &#60;Key&#62;error.html&#60;/Key&#62;
        &#60;/ErrorDocument&#62;
        &#60;RoutingRules&#62;
            &#60;RoutingRule&#62;
            &#60;Condition&#62;
                &#60;HttpErrorCodeReturnedEquals&#62;404&#60;/HttpErrorCodeReturnedEquals &#62;
            &#60;/Condition&#62;
            &#60;Redirect&#62;
                &#60;HostName&#62;www.redirect.com&#60;/HostName&#62;
                &#60;ReplaceKeyPrefixWith&#62;report-404/&#60;/ReplaceKeyPrefixWith&#62;
                &#60;HttpRedirectCode&#62;301&#60;/HttpRedirectCode&#62;
            &#60;/Redirect&#62;
            &#60;/RoutingRule&#62;
        &#60;/RoutingRules&#62;
    &#60;/WebsiteConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">五、配置静态网站并将某个目录请求全部转发到同一个页面</p>
                        <p class="margin-text">（1） 假设您的容器包含以下对象:</p>
                        <p class="p-indent">photos/photo1.jpg</p>
                        <p class="p-indent">photos/photo2.jpg</p>
                        <p class="p-indent">photos/photo3.jpg</p>
                        <p class="p-indent">我们想重定向所有以 photos/ 前缀的请求到同一个页面error.html</p>
                        <code>
<pre>
    &#60;WebsiteConfiguration xmlns='http://s3.amazonaws.com/doc/2006-03-01/'&#62;
        &#60;IndexDocument&#62;
            &#60;Suffix&#62;index.html&#60;/Suffix&#62;
        &#60;/IndexDocument&#62;
        &#60;ErrorDocument&#62;
            &#60;Key&#62;error.html&#60;/Key&#62;
        &#60;/ErrorDocument&#62;
        &#60;RoutingRules&#62;
            &#60;RoutingRule&#62;
            &#60;Condition&#62;
                &#60;KeyPrefixEquals&#62;photos/&#60;/KeyPrefixEquals&#62;
            &#60;/Condition&#62;
            &#60;Redirect&#62;
                &#60;ReplaceKeyWith&#62;error.html&#60;/ReplaceKeyWith&#62;
                &#60;HttpRedirectCode&#62;301&#60;/HttpRedirectCode&#62;
            &#60;/Redirect&#62;
            &#60;/RoutingRule&#62;
        &#60;/RoutingRules&#62;
    &#60;/WebsiteConfiguration&#62;
</pre>
                        </code>


                    </div>
                </div>
            </article>


            <!--对象存储—静态网站功能说明-->
            <article *ngIf="content === 'corsFunctionDes'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">功能说明——对象存储跨域访问规则</p>
                        <p class="p-indent">
                            跨域访问管理实现了跨源资源共享 (CORS)，定义了在一个域中加载的客户端 Web 应用与另一个域中资源交互的方式。利用 CORS 支持，您可以使用 OOS构建丰富的客户端Web 应用，同时可以选择性地允许跨域访问您的OOS资源。 
                        </p>
                    </div>
                </div>
            </article>
            <!--对象存储—静态网站使用说明-->
            <article *ngIf="content === 'corsUsageDes'">
                <p class="p-title">使用说明——对象存储跨域访问规则</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、使用场景</p>
                        <ul class="article-ul">
                            <li>
                                <p>场景 1：假设您在名为 website 的OOS容器中托管网站。您的用户加载了网站客户端页面 http://website.oos-cn-wuxi-website.heclouds.com 。现在，您想要使用此容器中存储的网页上的 JavaScript，通过该容器的API接入点 website.oos-cn-wuxi.heclouds.com 向同一容器发起具有身份认证信息的 GET 和 PUT 请求，通常浏览器会阻止 JavaScript 发起这些请求，但借助 CORS，您可以配置您的容器显式地支持来自 website.oos-cn-wuxi-website.heclouds.com 的跨域请求</p>
                            </li>
                            <li>
                                <p>场景 2：假设您在OOS容器中托管了 Web 字体。当其他域的Web应用加载该 Web 字体时，浏览器会进行 CORS 检查 (也称为预检)。您可以配置 Web 字体所在容器的跨域访问规则，按照需要配置哪些域能够发出这些请求。</p>
                            </li>
                        </ul>
                        <p class="step-text">二、使用说明</p>
                        <p class="p-indent">
                            为了使用跨域访问功能，您可以通过API或者控制台页面配置容器的跨域访问规则，每条规则定义了允许来源、允许方法等信息，您最多可以配置100条跨域访问规则。
                        </p>
                        <p class="p-indent">规则中各字段含义如下：</p>
                        <ul class="article-ul">
                            <li>AllowedOrigin
                                <p class="p-indent">通过该字段可以指定您希望能够发起跨域请求的源，例如： http://www.example.com ，源字符串中最多只能包含一个*通配符，例如  http://*.example.com 。如直接将*指定为源，那么所有源都将能够发起跨域请求。您也可以指定https只允许安全的源能够发起请求。 </p>
                            </li>
                            <li>AllowedMethod
                                <p class="p-indent">该字段定义了跨域请求能够使用的 HTTP 方法，可以从以下范围内选择： </p>
                                <ul class="article-ul">
                                    <li><p>GET</p></li>
                                    <li><p>PUT</p></li>
                                    <li><p>POST</p></li>
                                    <li><p>DELETE</p></li>
                                    <li><p>HEAD</p></li>
                                </ul>
                            </li>
                            <li>AllowedHeader
                                <p class="p-indent">控制预检请求Access-Control-Request-Headers标头中指定的header是否被允许。在Access-ControlRequest-Headers中指定的每个header都必须在AllowedHeader中有对应的项方可允许跨域。
                                    每个 AllowedHeader 字符串最多只能包含一个*通配符，例如 &lt;AllowedHeader>test* &lt;AllowedHeader> 将允许所有以test开头的标头</p>
                            </li>
                            <li>ExposeHeader
                                <p class="p-indent">指定允许用户从应用程序(例如， JavaScript的 XMLHttpRequest 对象) 进行访问的响应标头。</p>
                            </li>
                            <li>MaxAgeSeconds
                                <p class="p-indent">指定在预检请求被资源、HTTP 方法和源识别之后，浏览器将为预检请求缓存响应的时间 (以秒为单位)。</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--对象存储—跨域访问规则配置示例-->
            <article *ngIf="content === 'corsExp'">
                <p class="p-title">配置示例——对象存储跨域访问规则</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、示例一</p>
                        <code>
<pre>
&#60;CORSConfiguration&#62; 
    &#60;CORSRule&#62;   
        &#60;AllowedOrigin&#62;http://www.example1.com&#60;/AllowedOrigin&#62;
        &#60;AllowedMethod&#62;PUT&#60;/AllowedMethod&#62;   
        &#60;AllowedMethod&#62;POST&#60;/AllowedMethod&#62;   
        &#60;AllowedMethod&#62;DELETE&#60;/AllowedMethod&#62;
        &#60;AllowedHeader&#62;*&#60;/AllowedHeader&#62; 
    &#60;/CORSRule&#62; 
    &#60;CORSRule&#62;   
        &#60;AllowedOrigin&#62;http://www.example2.com&#60;/AllowedOrigin&#62;
          &#60;AllowedMethod&#62;PUT&#60;/AllowedMethod&#62;   
          &#60;AllowedMethod&#62;POST&#60;/AllowedMethod&#62;   
          &#60;AllowedMethod&#62;DELETE&#60;/AllowedMethod&#62;
          &#60;AllowedHeader&#62;*&#60;/AllowedHeader&#62; 
    &#60;/CORSRule&#62; 
    &#60;CORSRule&#62;   
        &#60;AllowedOrigin&#62;*&#60;/AllowedOrigin&#62;
        &#60;AllowedMethod&#62;GET&#60;/AllowedMethod&#62;
    &#60;/CORSRule&#62;
&#60;/CORSConfiguration&#62;
</pre>
                        </code>
                        <ul class="article-ul">
                            <li><p>第一个规则允许来自 http://www.example1.com 源的跨域 PUT、POST 和 DELETE 请求。该规则还通过 Access-Control-Request-Headers 标头允许预检 OPTIONS 请求中的所有标头。作为对预检 OPTIONS 请求的响应，OOS将返回请求的标头。</p></li>
                            <li><p>第二个规则允许与第一个规则具有相同的跨源请求，但第二个规则应用于另一个源 http://www.example2.com 。</p></li>
                        </ul>
                        <p class="step-text">二、示例二</p>
                        <code>
<pre>
&#60;CORSConfiguration&#62; 
    &#60;CORSRule&#62;   
        &#60;AllowedOrigin&#62;http://www.example1.com&#60;/AllowedOrigin&#62;
        &#60;AllowedMethod&#62;PUT&#60;/AllowedMethod&#62;   
        &#60;AllowedMethod&#62;POST&#60;/AllowedMethod&#62;   
        &#60;AllowedMethod&#62;DELETE&#60;/AllowedMethod&#62;
        &#60;AllowedHeader&#62;*&#60;/AllowedHeader&#62; 
        &#60;MaxAgeSeconds&#62;3000&#60;/MaxAgeSeconds&#62; 
        &#60;ExposeHeader&#62;x-test1&#60;/ExposeHeader&#62; 
        &#60;ExposeHeader&#62;x-test2&#60;/ExposeHeader&#62; 
        &#60;ExposeHeader&#62;x-test3&#60;/ExposeHeader&#62; 
    &#60;/CORSRule&#62; 
&#60;/CORSConfiguration&#62;
</pre>
                        </code>
                        <ul class="article-ul">
                            <li><p>MaxAgeSeconds ：指定在OOS对特定资源的预检请求做出响应后，浏览器缓存该响应的时间（以秒为单位，在本示例中为 3000 秒）。通过缓存响应，在需要重复原始请求时，浏览器无需向 OOS 发送预检请求。 </p></li>
                            <li><p>ExposeHeader ： 指定可让用户从应用程序（例如，JavaScript的 XMLHttpRequest 对象）进行访问的响应标头（在本示例中，为 x-test1 、 x-test2 和 x-test3 ）。
                            </p></li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--对象存储—生命周期说明-->
            <article *ngIf="content === 'lifecycleFunctionDes'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">功能说明——对象存储生命周期管理</p>
                        <p class="p-indent">
                            为了使文件高效经济的存储，您可以为容器配置生命周期规则，定期删除符合过期条件的文件以及分片 上传产生的碎片。
                        </p>
                        <p class="p-indent">
                            OOS提供一组API操作用来管理容器中的生命周期配置，同时，您也可以使用控制台页面进行容器的生命周期配置。
                        </p>
                    </div>
                </div>
            </article>
            <!--对象存储—生命周期管理使用说明-->
            <article *ngIf="content === 'lifecycleUsageDes'">
                <p class="p-title">使用说明——对象存储生命周期管理</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、使用说明</p>
                        <p class="p-indent">
                            生命周期配置以 XML 为数据格式，该配置包含一个或者多个生命周期规则。
                        </p>
                        <code>
<pre>
&#60;LifecycleConfiguration&#62; 
    &#60;Rule&#62;
        ...
    &#60;/Rule&#62; 
    &#60;Rule&#62;
        ...
    &#60;/Rule&#62; 
&#60;/LifecycleConfiguration&#62;
</pre>
                        </code>
                        <p class="p-indent">每个规则由以下内容组成：</p>
                        <ul class="article-ul">
                            <li><p>规则元数据，包含规则 ID 以及用于表示规则是已启用还是已禁用的状态。如果规则处于禁用状态，则 OOS 不会执行规则中指定的任何操作。</p></li>
                            <li><p>筛选条件，用于标识规则将应用于的一组对象。您可以通过使用一个对象键前缀，或者一个到多个对象标签，或者对象键前缀和对象标签同时使用来指定筛选条件。 </p></li>
                            <li><p>您希望 OOS 执行的过期操作，支持基于特定日期或者过期天数进行设置。 </p></li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--对象存储—生命周期管理配置示例-->
            <article *ngIf="content === 'lifecycleExp'">
                <p class="p-title">配置示例——对象存储生命周期管理</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、示例一</p>
                        <p class="p-indent">每个生命周期规则都包含一个筛选条件，该筛选条件可用于确定容器中适用生命周期规则的一部分对象。以下生命周期配置显示了如何指定筛选条件的示例</p>
                        <p class="p-indent">在此生命周期配置规则中，筛选条件指定了一个键前缀 ( tax/ )。因此，此规则应用于带键名前缀 tax/ 的对象，例如 tax/doc1.txt 和 tax/doc2.txt ，根据此规则，OOS 将在对象创建365天（一 年）后将其删除</p>
                        <code>
<pre>
&#60;LifecycleConfiguration&#62; 
    &#60;Rule&#62;   
        &#60;ID&#62;Expiration Rule&#60;/ID&#62;
        &#60;Filter&#62;
            &#60;Prefix&#62;tax/&#60;/Prefix&#62;
        &#60;/Filter&#62;   
        &#60;Status&#62;Enabled&#60;/Status&#62;   
        &#60;Expiration&#62;
            &#60;Days&#62;365&#60;/Days&#62;
        &#60;/Expiration&#62;
    &#60;/Rule&#62; 
&#60;/LifecycleConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">二、示例二</p>
                        <p class="p-indent">您可以临时禁用生命周期规则。以下生命周期配置指定了两个规则：</p>
                        <ul class="article-ul">
                            <li><p>规则 1 指示 OOS 在带 logs/ 前缀的对象创建一天后将其过期。</p></li>
                            <li><p>规则 2 指示 OOS 在带 documents/ 前缀的对象创建一天后将其过期。</p></li>
                        </ul>
                        <p class="p-indent">在策略中，启用了规则 1 并禁用了规则 2。OOS 不会对已禁用规则执行任何操作。</p>
                        <code>
<pre>
&#60;LifecycleConfiguration&#62; 
    &#60;Rule&#62;   
        &#60;ID&#62;Rule1&#60;/ID&#62;
        &#60;Filter&#62;
            &#60;Prefix&#62;logs&#60;/Prefix&#62;
        &#60;/Filter&#62;   
        &#60;Status&#62;Enabled&#60;/Status&#62;   
        &#60;Expiration&#62;
            &#60;Days&#62;1&#60;/Days&#62;
        &#60;/Expiration&#62;
    &#60;/Rule&#62; 
    &#60;Rule&#62;   
        &#60;ID&#62;Rule2&#60;/ID&#62;
        &#60;Filter&#62;
            &#60;Prefix&#62;documents&#60;/Prefix&#62;
        &#60;/Filter&#62;   
        &#60;Status&#62;Disabled&#60;/Status&#62;   
        &#60;Expiration&#62;
            &#60;Days&#62;1&#60;/Days&#62;
        &#60;/Expiration&#62;
    &#60;/Rule&#62; 
&#60;/LifecycleConfiguration&#62;
</pre>
                        </code>
                        <p class="step-text">三、示例三</p>
                        <p class="p-indent">您可以使用分片上传 API 来上传大对象。利用生命周期配置，当分片上传初始化后在指定的天数内未完成时，可以指示 OOS 终止未完成的分片上传。当 OOS 终止分片上传时，将删除与分片上传关联的所有分片。</p>
                        <p class="p-indent">下面是使用 AbortIncompleteMultipartUpload 操作的生命周期规则配置。此操作将指示 OOS 在分片上传初始化 7 天后终止未完成的分片上传。</p>
                        <code>
<pre>
&#60;LifecycleConfiguration&#62; 
    &#60;Rule&#62;   
        &#60;ID&#62;sample-rule&#60;/ID&#62;
        &#60;Filter&#62;
            &#60;Prefix&#62;SomeKeyPrefix/&#60;/Prefix&#62;
        &#60;/Filter&#62;   
        &#60;Status&#62;Enabled&#60;/Status&#62;   
        &#60;AbortIncompleteMultipartUpload&#62;
            &#60;DaysAfterInitiation&#62;7&#60;/DaysAfterInitiation&#62;
        &#60;/AbortIncompleteMultipartUpload&#62;
    &#60;/Rule&#62; 
&#60;/LifecycleConfiguration&#62;
</pre>
                        </code>
                    </div>
                </div>
            </article>


            <!--弹性伸缩-->
            <article *ngIf="content === 'elasticScalingProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品介绍——弹性伸缩</p>
                        <p class="p-indent">弹性伸缩（Auto
                            Scaling）是根据用户的业务需求，通过策略自动调整其业务资源的服务。您可以根据业务需求自行定义伸缩策略，从而降低人为反复调整资源以应对业务变化和负载高峰的工作量，帮您节约资源和人力运维成本。弹性伸缩目前尽支持对一个负载均衡下的云服务器进行自动调整，伸缩策略支持的监控指标有CPU和内存在负载均衡下的平均使用率，用户可以设置相应指标的阈值，实现负载均衡下云服务器的自动调整。
                        </p>
                    </div>
                </div>
            </article>
            <!--弹性伸缩—操作实例-->
            <article *ngIf="content === 'elasticScalingOperate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">弹性伸缩操作</p>
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、创建弹性伸缩</p>
                        <p class="margin-text">（1）进入控制台弹性伸缩页面，点击【创建弹性伸缩组】按钮</p>
                        <img src="./assets/manual-images/700001.png" class="step-img">
                        <p class="margin-text">（2）输入伸缩组名称、最小实例数、最大实例数、选择负载均衡和实例移除策略，以及默认冷却时间，然后点击【下一步】</p>
                        <img src="./assets/manual-images/700002.png" class="step-img">
                        <p class="margin-text">
                            （3）配置弹性伸缩虚拟机模板，选择配置、镜像设置存储空间，选择网络，弹性公网IP，设置登陆密码、实例信息，上传用户数据注入脚本，用户数据注入脚本是在虚拟机创建时执行。</p>
                        <img src="./assets/manual-images/700003.png" class="step-img">
                        <img src="./assets/manual-images/700004.png" class="step-img">
                        <p class="step-text">二、配置伸缩策略</p>
                        <p class="margin-text">（1）点击操作栏中的【伸缩策略】按钮进入配置伸缩策略</p>
                        <img src="./assets/manual-images/700005.png" class="step-img">
                        <p class="margin-text">（2）点击【添加伸缩策略】，创建伸缩策略</p>
                        <img src="./assets/manual-images/700006.png" class="step-img">
                        <p class="margin-text">（3）设置策略名称、监控指标、阈值、监控周期、连续出现次数和冷却时间</p>
                        <img src="./assets/manual-images/700007.png" class="step-img">
                        <p class="step-text">三、查看弹性伸缩实例配置</p>
                        <p class="margin-text">（1）点击弹性伸缩组列表中的【查看】按钮查看伸缩实例配置</p>
                        <img src="./assets/manual-images/700008.png" class="step-img">
                        <img src="./assets/manual-images/700009.png" class="step-img">
                        <p class="step-text">四、编辑伸缩组</p>
                        <p class="margin-text">（1）点击弹性伸缩组列表中的【编辑】按钮进入编辑页面</p>
                        <img src="./assets/manual-images/700010.png" class="step-img">
                        <p class="margin-text">（2）配置相关信息并点击【保存】按钮</p>
                        <img src="./assets/manual-images/700011.png" class="step-img">
                        <p class="step-text">五、删除伸缩组</p>
                        <p class="margin-text">（1）点击弹性伸缩组列表中的【删除】按钮并确认删除</p>
                        <img src="./assets/manual-images/700012.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--弹性伸缩—常见问题-->
            <article *ngIf="content === 'elasticScalingProblem'">
                <p class="p-title">常见问题</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="step-text">一、策略执行的时间间隔大于监控周期？</p>
                        <p class="margin-text indent-text">
                            当出现创建虚拟机（删除虚拟机）的情况时，策略执行的时间间隔等于虚拟机创建及负载均衡绑定时间（虚拟机删除及负载均衡解绑时间） + 冷却时间 + 等待进入下一个策略执行时间点的时间。。
                        </p>
                        <p class="step-text">二、策略定时执行规则？</p>
                        <p class="margin-text indent-text">
                            策略是根据设置的监控周期定时执行，当上一个周期策略执行的任务未完成时，这一个时刻的策略任务不会执行，只有处于策略执行时间点，且无上一次策略任务正在执行，才会执行当前时刻的策略任务。
                        </p>
                        <p class="step-text">三、为什么正在运行的策略会突然停止？</p>
                        <p class="margin-text indent-text">
                            当出现需要增加虚拟机的情况时，虚拟机连续创建失败4次，这时候会自动停止创建虚拟机和运行的对应策略。
                        </p>
                        <p class="step-text">四、满足伸缩策略的情况下，没有创建虚拟机？</p>
                        <p class="margin-text indent-text">
                            只有在满足伸缩策略增加虚拟机的条件，且系统当前没有正在创建的虚拟机时，才会创建虚拟机。
                        </p>
                    </div>
                </div>
            </article>

            <!--文件存储-->
            <article *ngIf="content === 'fileStorageProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——文件存储</p>
                        <p class="p-indent">OFS（AIC File Storage，AIC文件存储）是面向云主机的分布式文件系统服务，提供NFS文件访问协议，完全兼容现有传统应用，
                            具备容量弹性扩容、性能线性扩展、独立命名空间、多共享、高可靠和高可用等特性</p>
                        <p class="step-text">产品功能：</p>
                        <ul class="article-ul">
                            <li>
                                <p>标准协议：支持NFS访问协议，提供POSIX标准语义访问接口。</p>
                            </li>
                            <li>
                                <p>共享访问：支持多云主机并发访问同一个共享文件系统，避免拷贝开销。</p>
                            </li>
                            <li>
                                <p>权限设置：支持用户白名单设置，保证用户数据隔离。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>文件共享：协同办公需要频繁地共享文件，多台服务器需要同时访问共享文件存储。OFS文件存储支持多主机并发访问同一个文件系统，节约大量拷贝和同步成本。</p>
                            </li>
                            <li>
                                <p>媒体文件存储：媒体文件一般大小在数十MB到数十GB之间，且一般都是顺序访问。 OFS文件存储对大文件顺序读写的场景进行了优化，保证媒体文件的读写高效稳定地运行</p>
                            </li>
                            <li>
                                <p>海量数据收集：各种数据分析平台需要将云主机产生的业务数据、日志数据进行收集。数据最终需要汇聚到一起进行分析。
                                    OFS文件存储支持共享访问，可以无缝完成数据收集和汇总，节省了额外进行数据汇总的开销。
                                    且可以根据容量需求，进行灵活地扩展，避免了因容量不够而导致的数据迁移。</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--文件存储—创建文件系统-->
            <article *ngIf="content === 'fileStorageCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建文件系统</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台文件存储页面，点击【创建文件系统】按钮</p>
                        <img src="./assets/manual-images/900001.png" class="step-img">
                        <p class="margin-text">（2）输入文件系统名称，名称仅允许包含小写字母、数字、-，需要以小写字母或数字开头和结尾、长度为3-63个字符</p>
                        <img src="./assets/manual-images/900002.png" class="step-img">
                        <p class="margin-text">（3）输入文件系统容量（10-500GB）</p>
                        <p class="margin-text">（4）点击【确定】按钮即可创建文件系统</p>
                    </div>
                </div>
            </article>
            <!--文件存储—操作实例-->
            <article *ngIf="content === 'fileStorageOperate'">
                <p class="p-title">文件存储操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看用量、用户白名单详情</p>
                        <p class="margin-text">（1） 点击文件存储首页【查看】按钮查看文件系统的用量、用户白名单详情</p>
                        <img src="./assets/manual-images/900003.png" class="step-img">
                        <img src="./assets/manual-images/900004.png" class="step-img">
                        <p class="step-text">二、用户白名单操作</p>
                        <p class="margin-text">（1） 点击【用户白名单】按钮可以对文件系统的用户白名单进行更改，最多只能增加16组不同的用户白名单</p>
                        <img src="./assets/manual-images/900005.png" class="step-img">
                        <img src="./assets/manual-images/900006.png" class="step-img">
                        <p class="step-text">三、扩容</p>
                        <p class="margin-text">（1） 点击【扩容】按钮，输入目标容量，点击确定即可扩容，仅支持增加整数GB容量</p>
                        <img src="./assets/manual-images/900007.png" class="step-img">
                        <img src="./assets/manual-images/900008.png" class="step-img">
                        <p class="step-text">四、删除文件系统</p>
                        <p class="margin-text">（1） 点击【删除】按钮并确认删除即可删除文件系统，删除文件系统后内部数据会一起删除并且无法恢复！</p>
                        <img src="./assets/manual-images/900009.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--文件存储—常见问题-->
            <article *ngIf="content === 'fileStorageProblem'">
                <p class="p-title">文件存储常见问题</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">常见问题：</p>
                        <p class="step-text">一、用户白名单说明</p>
                        <p class="margin-text indent-text">如果输入的白名单是错误的用户名不会显示错误，但是该用户的白名单不会起作用。
                            如果输入的错误用户名，后面被注册为正式用户名，需要白名单起作用，还需要重新设置。</p>
                    </div>
                </div>
            </article>

            <!--消息队列-->
            <article *ngIf="content === 'MQProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——消息队列(Kafka)</p>
                        <p class="p-indent">OMQ（AIC Message
                            Queue(Kafka)，AIC消息队列(Kafka)）是一个分布式的、高吞吐量、低延迟、高可扩展性消息队列服务。100%兼容开源 Kafka
                            API（2.0.X及以上），帮助用户快速尚云。作为消息中间件，帮助用户实现消息生产者与消费者之间的解耦，无需彼此等待。</p>
                        <p class="step-text">产品优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>高性价比：无需采购、部署和运维费用，基于自有机房进行大规模部署，成本低廉。</p>
                            </li>
                            <li>
                                <p>开箱即用：只需几分钟时间，即可获得一个高可用Kafka集群。</p>
                            </li>
                            <li>
                                <p>高可用：使用集群形态部署，稳定可靠。</p>
                            </li>
                            <li>
                                <p>业务安全：租户之间网络隔离，实例的网络访问在租户间天然隔离。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    日志分析系统：消息队列在日志分析系统中扮演重要角色。日志产生后异步发送至消息队列，再有消息队列分发至不同的日志分析系统，其高吞吐特新使其保证日志采集分发的稳定、可靠。
                                </p>
                            </li>
                            <li>
                                <p>
                                    大数据分析：消息队列是大数据生态中不可或缺的组件之一。通过agent采集到消息队列中的服务，依托其低延迟特性可将数据推送至实时数据分析平台，完成实时图表展示、异常检测等；也可以依托其高吞吐特性将数据推送至离线数据仓库，作为离线数据分析的数据入口。
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--消息队列—创建实例-->
            <article *ngIf="content === 'MQCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建Kafka实例</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台消息队列-Kafka页面，点击【创建实例】按钮进入实例创建页面</p>
                        <img src="./assets/manual-images/10-1.png" class="step-img">
                        <p class="margin-text">（2）选择计费方式</p>
                        <p class="margin-text">（3）选择实例类型、磁盘容量、消息保留时长</p>
                        <p class="margin-text">（4）选择专有网络及子网</p>
                        <p class="margin-text">（5）输入实例名称，实例名称必须以字母开头，只能输入英文、数字、中划线</p>
                        <p class="margin-text">（6）点击【创建】按钮即可创建Kafka实例</p>
                        <img src="./assets/manual-images/10-2.png" class="step-img">

                    </div>
                </div>
            </article>
            <!--消息队列—操作实例-->
            <article *ngIf="content === 'MQOperate'">
                <p class="p-title">消息队列Kafka操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看详情</p>
                        <p class="margin-text">（1） 点击Kafka首页【查看】按钮进入详情页面</p>
                        <img src="./assets/manual-images/10-3.png" class="step-img">
                        <p class="margin-text">（2） Kafka详情页第一页为Kafka基本信息</p>
                        <img src="./assets/manual-images/10-4.png" class="step-img">
                        <p class="margin-text">（3） Kafka详情页第二页为Topic管理页</p>
                        <img src="./assets/manual-images/10-5.png" class="step-img">
                        <p class="margin-text">1. 点击【+ 创建Topic】按钮，输入Topic名称，点击确定即可新建Topic</p>
                        <img src="./assets/manual-images/10-6.png" class="step-img">
                        <img src="./assets/manual-images/10-7.png" class="step-img">
                        <p class="margin-text">2. 点击Topic列表更多操作中的【查看分区状态】按钮可查看当前Topic的消息总量等信息</p>
                        <img src="./assets/manual-images/10-8.png" class="step-img">
                        <img src="./assets/manual-images/10-9.png" class="step-img">
                        <p class="margin-text">3. 点击Topic列表更多操作中的【删除】按钮可删除当前Topic</p>
                        <img src="./assets/manual-images/10-10.png" class="step-img">
                        <p class="margin-text">（4） Kafka详情页第三页为消费者组管理页</p>
                        <img src="./assets/manual-images/10-11.png" class="step-img">
                        <p class="margin-text">1. 点击【+ 创建消费者组】按钮，输入消费者组名称、备注，点击确定即可新建消费者组</p>
                        <img src="./assets/manual-images/10-12.png" class="step-img">
                        <img src="./assets/manual-images/10-13.png" class="step-img">
                        <p class="margin-text">2. 点击消费者组列表更多操作中的【查看消息堆积】按钮可查看当前消费者组的消息堆积量等信息</p>
                        <img src="./assets/manual-images/10-14.png" class="step-img">
                        <img src="./assets/manual-images/10-15.png" class="step-img">
                        <p class="margin-text">3. 点击消费者组列表更多操作中的【删除】按钮可删除当前消费者组</p>
                        <img src="./assets/manual-images/10-16.png" class="step-img">

                        <p class="margin-text">（5） Kafka详情页第四页为告警监控管理页</p>
                        <img src="./assets/manual-images/10-17.png" class="step-img">
                        <p class="margin-text">1. 点击【+ 新增监控】按钮，输入告警监控名称，选择告警监控类型、对象，输入阈值，按需选择通知方式，点击确定即可新建告警监控</p>
                        <img src="./assets/manual-images/10-18.png" class="step-img">
                        <img src="./assets/manual-images/10-19.png" class="step-img">
                        <p class="margin-text">2. 点击监控列表更多操作中的【静默】按钮可静默当前监控告警</p>
                        <img src="./assets/manual-images/10-20.png" class="step-img">
                        <p class="margin-text">3. 点击监控列表更多操作中的【恢复】按钮可恢复当前监控告警</p>
                        <img src="./assets/manual-images/10-21.png" class="step-img">
                        <p class="margin-text">4. 点击监控列表更多操作中的【删除】按钮可删除当前监控</p>
                        <img src="./assets/manual-images/10-22.png" class="step-img">
                        <p class="step-text">二、启动Kafka实例</p>
                        <p class="margin-text">（1） 在非欠费情况下，点击Kafka列表的【启动】按钮即可启动因欠费导致停止的Kafka实例</p>
                        <img src="./assets/manual-images/10-23.png" class="step-img">
                        <p class="step-text">三、删除Kafka实例</p>
                        <p class="margin-text">（1） 点击【删除】按钮并确认删除即可删除Kafka实例</p>
                        <img src="./assets/manual-images/10-24.png" class="step-img">
                    </div>
                </div>
            </article>

            <!--k8s—产品介绍-->
            <article *ngIf="content === 'k8sProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——Kubernetes</p>
                        <p>AIC应用托管平台提供基于原生Kubernetes的容器管理服务（AIC
                            Kubernetes），提供高性能的容器化管理能力，能够实现对用户容器化应用的全生命周期管理。服务部署简单，免去运维成本。可根据业务一键增删节点，满足不同场景下的使用需求。</p>
                        <p class="step-text">产品优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>一键部署：省去集群搭建的烦恼，用户无需手动运维，全程自动化部署，开箱即用。</p>
                            </li>
                            <li>
                                <p>按需使用：页面一键新增、减少节点数量，根据业务情况使用，降低成本。</p>
                            </li>
                            <li>
                                <p>秒级启动：结合私有镜像仓库，可实现用户容器化应用快速部署。</p>
                            </li>
                            <li>
                                <p>监控全面：内置监控组件，实现对节点、应用的多维度监控，并及时告警。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    应用托管：利用Kubernetes对于容器的全生命周期管理特性，在用户应用故障的情况下自动重启应用，无需人为干预。同时支持用户无状态应用多副本部署，保证应用高可用。
                                </p>
                            </li>
                            <li>
                                <p>
                                    微服务架构：微服务架构将功能分解到各个离散的容器服务中以实现对解决方案的解耦，大幅提高系统功能扩展性，Kubernetes平台对每个容器服务进行全生命周期管理，保证服务可用。
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--k8s—创建实例-->
            <article *ngIf="content === 'k8sCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建Kubernetes实例</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台容器服务-Kubernetes页面，点击【创建Kubernetes】按钮进入实例创建页面</p>
                        <img src="./assets/manual-images/11-1.png" class="step-img">
                        <p class="margin-text">（2）选择计费方式</p>
                        <p class="margin-text">（3）输入实例名称，名称可包含数字、大小写字母、中文、“.”、“_”或“-”，长度为4-16个字符</p>
                        <p class="margin-text">（4）输入描述(可选，200字符以内)</p>
                        <p class="margin-text">（5）选择集群版本</p>
                        <p class="margin-text">（6）配置主节点、计算节点的CPU、内存规格、存储空间等</p>
                        <p class="margin-text">（7）选择专有网络及子网</p>
                        <p class="margin-text">（8）点击【创建】按钮即可创建Kubernetes实例</p>
                        <img src="./assets/manual-images/11-2.png" class="step-img">
                        <img src="./assets/manual-images/11-3.png" class="step-img">

                    </div>
                </div>
            </article>
            <!--k8s—操作实例-->
            <article *ngIf="content === 'k8sOperate'">
                <p class="p-title">Kubernetes操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看详情</p>
                        <p class="margin-text">（1） 点击Kubernetes首页【查看】按钮进入详情页面</p>
                        <img src="./assets/manual-images/11-4.png" class="step-img">
                        <p class="margin-text">（2） Kubernetes详情页第一页为Kubernetes基本信息</p>
                        <img src="./assets/manual-images/11-5.png" class="step-img">
                        <p class="margin-text">（3） 点击绑定/修改镜像仓库，选择镜像仓库并输入正确的用户名、密码，点击确定即可绑定/修改，绑定/修改后可点击镜像名后的删除按钮删除镜像</p>
                        <img src="./assets/manual-images/11-6.png" class="step-img">
                        <img src="./assets/manual-images/11-7.png" class="step-img">
                        <p class="margin-text">（4） Kubernetes详情页第二页为Kubernetes节点管理页</p>
                        <p class="margin-text">1. 点击【+ 创建计算节点】按钮，选择CPU、内存、磁盘大小、数量，点击确定即可新建计算节点（同一集群计算节点最多创建10个）</p>
                        <img src="./assets/manual-images/11-8.png" class="step-img">
                        <img src="./assets/manual-images/11-9.png" class="step-img">
                        <p class="margin-text">2. 点击节点列表更多操作中的【访问】按钮可进入节点虚拟机控制台</p>
                        <img src="./assets/manual-images/11-10.png" class="step-img">
                        <p class="margin-text">3. 点击节点列表更多操作中的【删除】按钮可删除当前计算节点</p>
                        <img src="./assets/manual-images/11-11.png" class="step-img">
                        <p class="margin-text">（5） Kubernetes详情页第三页为命名空间管理页</p>
                        <img src="./assets/manual-images/11-12.png" class="step-img">
                        <p class="margin-text">1. 点击【+
                            新增命名空间】按钮，输入命名空间名称、备注，名称长度为2-64个字符之间(含),可包含小写字母、数字及分隔符("-"),不能以分隔符开头或结尾，点击确定即可新建命名空间</p>
                        <img src="./assets/manual-images/11-13.png" class="step-img">
                        <img src="./assets/manual-images/11-14.png" class="step-img">
                        <p class="margin-text">2. 点击命名空间列表更多操作中的【修改】按钮可修改当前命名空间的描述</p>
                        <img src="./assets/manual-images/11-15.png" class="step-img">
                        <img src="./assets/manual-images/11-16.png" class="step-img">
                        <p class="margin-text">3. 点击命名空间列表更多操作中的【删除】按钮可删除当前命名空间</p>
                        <img src="./assets/manual-images/11-17.png" class="step-img">
                        <p class="margin-text">（6） Kubernetes详情页第四页为服务管理页</p>
                        <p class="margin-text">1. 选择左侧下拉框，查看不同节点上部署的服务</p>
                        <img src="./assets/manual-images/11-18.png" class="step-img">
                        <p class="margin-text">2. 点击【+ 新增服务部署】按钮，进入创建服务部署页面</p>
                        <img src="./assets/manual-images/11-19.png" class="step-img">
                        <p class="margin-text">3. 输入服务名称、选择命名空间，按需填写数据卷、运行容器端口映射等后，点击【确认创建】按钮即可增加服务</p>
                        <img src="./assets/manual-images/11-20.png" class="step-img">
                        <img src="./assets/manual-images/11-21.png" class="step-img">
                        <p class="margin-text">4. 点击服务列表更多操作中的【查看容器】按钮可删除当前服务中的容器</p>
                        <img src="./assets/manual-images/11-22.png" class="step-img">
                        <img src="./assets/manual-images/11-23.png" class="step-img">
                        <p class="margin-text">5. 点击pod列表日常日志中的图标按钮可查看当前pod的异常日志</p>
                        <img src="./assets/manual-images/11-24.png" class="step-img">
                        <p class="margin-text">6. 点击pod列表中名称前的【+】按钮查看当前pod下的容器，点击日志/异常中的【查看日志】按钮可查看当前容器的日志</p>
                        <img src="./assets/manual-images/11-25.png" class="step-img">
                        <img src="./assets/manual-images/11-26.png" class="step-img">
                        <p class="margin-text">7. 点击【手动刷新】按钮可刷新当前pod列表信息</p>
                        <img src="./assets/manual-images/11-27.png" class="step-img">
                        <p class="margin-text">8. 点击服务列表更多操作中的【端口】按钮可查看当前服务的端口信息</p>
                        <img src="./assets/manual-images/11-28.png" class="step-img">
                        <img src="./assets/manual-images/11-29.png" class="step-img">
                        <p class="margin-text">9. 点击服务列表更多操作中的【删除】按钮可删除当前服务</p>
                        <img src="./assets/manual-images/11-30.png" class="step-img">
                        <p class="margin-text">（7） Kubernetes详情页第六页为告警规则管理页</p>
                        <img src="./assets/manual-images/11-33.png" class="step-img">
                        <p class="margin-text">1. 点击【+ 新增告警规则】按钮，选择告警规则类型、告警条件、延时告警时间，点击【确定】按钮即可新增告警规则</p>
                        <img src="./assets/manual-images/11-34.png" class="step-img">
                        <img src="./assets/manual-images/11-35.png" class="step-img">
                        <p class="margin-text">2. 点击告警规则列表的更多操作中的【修改】按钮可修改当前规则的告警条件、延时告警时间</p>
                        <img src="./assets/manual-images/11-36.png" class="step-img">
                        <img src="./assets/manual-images/11-37.png" class="step-img">
                        <p class="margin-text">3. 点击告警规则列表的更多操作中的【禁用】（或【启用】）按钮可禁用（或启用）当前规则</p>
                        <img src="./assets/manual-images/11-38.png" class="step-img">
                        <img src="./assets/manual-images/11-39.png" class="step-img">
                        <p class="margin-text">4. 点击告警规则列表的更多操作中的【删除】按钮可删除当前规则</p>
                        <img src="./assets/manual-images/11-40.png" class="step-img">
                        <p class="margin-text">（8） Kubernetes详情页第七页为告警用户管理页</p>
                        <img src="./assets/manual-images/11-41.png" class="step-img">
                        <p class="margin-text">1. 点击【+ 新增告警用户】按钮，输入用户名、邮箱，点击【确定】按钮即可新增告警用户</p>
                        <img src="./assets/manual-images/11-42.png" class="step-img">
                        <img src="./assets/manual-images/11-43.png" class="step-img">
                        <p class="margin-text">2. 点击告警用户列表的更多操作中的【修改】按钮可修改当前告警用户的激活状态</p>
                        <img src="./assets/manual-images/11-44.png" class="step-img">
                        <img src="./assets/manual-images/11-45.png" class="step-img">
                        <p class="margin-text">3. 点击告警用户列表的更多操作中的【删除】按钮可删除当前用户</p>
                        <img src="./assets/manual-images/11-46.png" class="step-img">
                        <p class="margin-text">（9） Kubernetes详情页第八页为告警实例管理页</p>
                        <img src="./assets/manual-images/11-47.png" class="step-img">
                        <p class="margin-text">1. 点击左侧下拉菜单，可选择不同的告警类型查询；点击右侧【已激活告警实例】和【静默告警实例】可查看不同状态的告警实例</p>
                        <img src="./assets/manual-images/11-48.png" class="step-img">
                        <p class="margin-text">2. 在【已激活告警实例】列表中，点击表格右侧操作栏中的【静默告警】按钮，选择静默时间段，点击确定可在指定时间段中静默该告警</p>
                        <img src="./assets/manual-images/11-49.png" class="step-img">
                        <img src="./assets/manual-images/11-50.png" class="step-img">
                        <p class="margin-text">3. 在【静默告警实例】列表中，点击表格右侧操作栏中的【恢复告警】按钮，点击确定即可恢复该告警</p>
                        <img src="./assets/manual-images/11-51.png" class="step-img">
                        <p class="step-text">二、启动Kubernetes实例</p>
                        <p class="margin-text">（1） 在非欠费情况下，点击Kubernetes列表的【启动】按钮即可启动因欠费导致停止的Kubernetes实例</p>
                        <img src="./assets/manual-images/11-52.png" class="step-img">
                        <p class="step-text">三、删除Kubernetes实例</p>
                        <p class="margin-text">（1） 点击【删除】按钮并确认删除即可删除Kubernetes实例</p>
                        <img src="./assets/manual-images/11-53.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--harbor—产品介绍-->
            <article *ngIf="content === 'harborProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——Harbor</p>
                        <p>AIC应用托管平台提供私有镜像仓库服务（AIC Harbor），用于存储、分发用户Docker镜像，一键部署，开箱即用，免去用户手动运维及管理的烦恼，后端可采用AWS
                            S3对象存储方案，以多副本形式保存用户镜像数据，保证用户数据安全性</p>
                        <p class="step-text">产品优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>高性价比：无需采购、部署和运维费用，基于自有机房进行大规模部署，成本低廉。</p>
                            </li>
                            <li>
                                <p>私有化：独享集群，拥有该镜像仓库最高管理权限。</p>
                            </li>
                            <li>
                                <p>可视化：提供友好的可视化界面，用户、镜像操作轻松管理。</p>
                            </li>
                            <li>
                                <p>高可靠：基于对象存储的镜像数据，多副本保存，服务可用性高达99.9%。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    企业镜像存储：企业用户可根据部门或团队情况划分合适的镜像仓库，并根据业务或者人员分配相应权限，存取企业业务相关镜像，并无需关注集群运维情况，降低维护成本。
                                </p>
                            </li>
                            <li>
                                <p>
                                    容器化部署：结合AIC
                                    Kubernetes服务，将私有仓库中的用户镜像分发至Kubernetes集群，无缝衔接，无需用户额外手动配置，提供用户服务的全生命周期管理。
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--harbor—创建实例-->
            <article *ngIf="content === 'harborCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">创建Harbor实例</p>
                        <p class="title-l2">步骤：</p>
                        <p class="margin-text">（1）进入控制台容器服务-Harbor页面，点击【创建Harbor】按钮进入实例创建页面</p>
                        <img src="./assets/manual-images/12-1.png" class="step-img">
                        <p class="margin-text">（2）选择计费方式</p>
                        <p class="margin-text">（3）输入实例名称，名称可包含数字、大小写字母、中文、“.”、“_”或“-”，长度为4-16个字符</p>
                        <p class="margin-text">（4）输入描述(可选，200字符以内)</p>
                        <p class="margin-text">（5）选择集群版本</p>
                        <p class="margin-text">（6）选择配置方式、存储类型等</p>
                        <p class="margin-text">（7）配置主节点</p>
                        <p class="margin-text">（8）选择专有网络及子网</p>
                        <p class="margin-text">（9）选择弹性公网IP</p>
                        <p class="margin-text">（10）点击【创建】按钮即可创建Harbor实例</p>
                        <img src="./assets/manual-images/12-2.png" class="step-img">
                        <img src="./assets/manual-images/12-3.png" class="step-img">

                    </div>
                </div>
            </article>
            <!--harbor—操作实例-->
            <article *ngIf="content === 'harborOperate'">
                <p class="p-title">Harbor操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、查看详情</p>
                        <p class="margin-text">（1） 点击Harbor首页【查看】按钮进入详情页面</p>
                        <img src="./assets/manual-images/12-4.png" class="step-img">
                        <p class="margin-text">（2） Harbor详情页第一页为Harbor基本信息</p>
                        <img src="./assets/manual-images/12-5.png" class="step-img">
                        <p class="margin-text">（3） 点击弹性公网IP超链接可直接访问Harbor可视化界面</p>
                        <img src="./assets/manual-images/12-6.png" class="step-img">
                        <p class="margin-text">（4） Harbor详情页第二页为Harbor节点管理页</p>
                        <img src="./assets/manual-images/12-7.png" class="step-img">
                        <p class="step-text">二、启动Harbor实例</p>
                        <p class="margin-text">（1） 在非欠费情况下，点击Harbor列表的【启动】按钮即可启动因欠费导致停止的Harbor实例</p>
                        <img src="./assets/manual-images/12-8.png" class="step-img">
                        <p class="step-text">三、删除Harbor实例</p>
                        <p class="margin-text">（1） 点击【删除】按钮并确认删除即可删除Harbor实例</p>
                        <img src="./assets/manual-images/12-9.png" class="step-img">
                    </div>
                </div>
            </article>

            <!--云效平台-->
            <article *ngIf="content === 'devopsProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——云效平台(AIC DevOps)</p>
                        <p class="p-indent">云效平台（AIC DevOps）是一站式企业研发平台，为企业用户提供从“需求->开发->测试->发布->运维”端到端的协同服务和研发工具支撑。
                        </p>
                        <p class="step-text">产品优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>一站式：提供代码编译、镜像构建、服务发布、容器化部署等流程化体系。</p>
                            </li>
                            <li>
                                <p>高性能：接入AIC Kubernetes服务，助力用户一键上云。</p>
                            </li>
                            <li>
                                <p>灵活：提供丰富的接口，方便用户自有的CI/CD系统能够无缝接入。</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    企业服务：集合编译、构建、发布、部署为流水线，满足产品快速交付的需求
                                </p>
                            </li>
                            <li>
                                <p>
                                    容器部署：结合AIC Kubernetes服务，助力用户应用秒级启动，保证服务高可用
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--云效平台—名词说明-->
            <article *ngIf="content === 'devopsCreate'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">名词说明</p>
                        <nz-table nzTemplateMode class="help-menu-table" [nzBordered]=true>
                            <thead>
                                <tr>
                                    <th>名词</th>
                                    <th width=80%>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>项目</td>
                                    <td>
                                        <span>项目是效能平台最高一层管理纬度，每个用户可创建自己的项目：默认10个为上限，再多需要提供单由OnetNet管理员创建</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>设置-成员管理</td>
                                    <td>
                                        <span>项目成员管理：是管理什么用户能访问到该项目的配置管理，可从整个平台添加用户到项目中，每个用户可以配置多个用户组，多个用户组的权限是相加。</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>设置-用户组</td>
                                    <td>
                                        <span>项目用户组：配置该项目中权限分配情况 – 每个用户组可自由添加删除权限节点和用户。</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>设置-凭证管理</td>
                                    <td nzAlign="left">
                                        <span>
                                            项目凭证管理：配置在项目中需要使用的凭证，git+docker凭证2种类型，<br>
                                            1. Git 凭证只配置在应用参数中，用来鉴权并下载代码 <br>
                                            2. Docker凭证是用来上传镜像到harbor，并下载的配置；存在项目/应用/环境中，按照项目 &lt; 应用 &lt; 环境
                                            的优先级查找docker凭证，如果均没有配置流水线会报错。<br>
                                            PS：项目已预置了一个默认的harbor配置
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>设置-镜像仓库</td>
                                    <td>
                                        <span>给该项目配置一个默认的镜像仓库</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>应用</td>
                                    <td>
                                        <span>应用是项目下的一层管理层次，可以按照每个代码仓库来区分不同的应用</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>环境</td>
                                    <td>
                                        <span>环境是对接k8s集群的配置，每个环境对应着一个集群的命名空间</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>流水线</td>
                                    <td>
                                        <span>流水线是应用中的执行计划，每个流水线按照配置执行相应的流程。</span>
                                    </td>
                                </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </div>
            </article>
            <!--云效平台—操作实例-->
            <article *ngIf="content === 'devopsOperate'">
                <p class="p-title">云效平台操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、项目</p>
                        <p class="margin-text">（1） 项目列表</p>
                        <p class="margin-text">展示所有该用户有权限的项目 – 每个项目的全新是独立的，都对应的查看，编辑，删除</p>
                        <img src="./assets/manual-images/13-1.png" class="step-img">
                        <p class="margin-text">（2） 创建项目</p>
                        <p class="margin-text">每个用户可以创建项目(10个)，每个字段均为必选，其中项目标示为全局唯一。</p>
                        <img src="./assets/manual-images/13-2.png" class="step-img">
                        <p class="margin-text">（3） 编辑项目</p>
                        <p class="margin-text">修改项目中的某些字段，需要编辑项目。其中的项目标示字段是不可编辑的。</p>
                        <img src="./assets/manual-images/13-3.png" class="step-img">
                        <p class="margin-text">（4） 删除项目</p>
                        <p class="margin-text">只有项目管理员能删除项目 – 注意 必须先将项目下的所有应用删除之后才能删除项目。</p>
                        <p class="margin-text">（5） 项目概览</p>
                        <p class="margin-text">点击项目 进入第一个项目概览页面，该页面统计了该项目的一些数据，和在时间段之内应用的的构建分析情况。</p>
                        <img src="./assets/manual-images/13-4.png" class="step-img">
                        <p class="margin-text">（6） 应用列表</p>
                        <p class="margin-text">列表中展示该项目中所有应用，每个应用有信息展示和一些应用级别的操作按钮：查看，编辑 ，删除，扩缩容，资源变更等操作。</p>
                        <img src="./assets/manual-images/13-5.png" class="step-img">

                        <p class="step-text">二、应用</p>
                        <p class="margin-text">（1） 新建应用</p>
                        <p class="margin-text">应用名称为全项目唯一；代码仓库为github/gitlab/gitee等git协议的代码仓库地址</p>
                        <p class="margin-text">PS：要以“.git”结尾；git凭证为请求该代码仓库使用的鉴权信息，为整个项目通用。</p>
                        <p class="margin-text">（2） 基础信息</p>
                        <img src="./assets/manual-images/13-6.png" class="step-img">
                        <p class="margin-text">（3） 构建配置</p>
                        <p class="margin-text">构建配置中，需要选择该应用的构建类型：默认go/java/nodejs 3中模版，如果是其他类型构建，选择不使用模版自定义构建；
                            <br>构建路径：为代码编译构建需要的目录，有特殊要求的需要单独配置，
                        </p>
                        <img src="./assets/manual-images/13-7.png" class="step-img">
                        <p class="margin-text">高级选项：配置属于该应用的harbor地址鉴权</p>
                        <img src="./assets/manual-images/13-8.png" class="step-img">
                        <p class="margin-text">（4） 编辑应用</p>
                        <p class="margin-text">编辑应用：应用名称不可修改 – 其他字段与新建应用类对。</p>
                        <img src="./assets/manual-images/13-9.png" class="step-img">
                        <p class="margin-text">（5） 扩、缩容</p>
                        <p class="margin-text">选择环境后，再修改实例数量参数</p>
                        <img src="./assets/manual-images/13-10.png" class="step-img">
                        <p class="margin-text">（6） 资源变更</p>
                        <p class="margin-text">类比扩缩容，选择环境之后 修改资源使用配置</p>
                        <img src="./assets/manual-images/13-11.png" class="step-img">
                        <p class="margin-text">（7） 应用实例</p>
                        <p class="margin-text">应用实例页面显示的是该应用在各个环境上运行的实例情况展示</p>
                        <img src="./assets/manual-images/13-12.png" class="step-img">

                        <p class="step-text">三、环境</p>
                        <p class="margin-text">环境有2个入口，在应用页面可以进入，在左边侧栏也可以进入</p>
                        <img src="./assets/manual-images/13-13.png" class="step-img">
                        <p class="margin-text">（1） 创建环境</p>
                        <p class="margin-text">
                            环境创建页面，需要按提示输入相应的值。环境类型只有开发/测试/予发布/生产，环境类型可以与环境名称没有强关联；可以单独配置环境的凭证。Kubernetes Ctx
                            是从该用户在系统中的容器集群中查询，如果没有需要先创建集群。
                            <br>PS：部署方式 只支持kubernetes部署
                            <br>PS：预发布和生产环境 部署步骤不会执行，只会默认成功
                        </p>
                        <img src="./assets/manual-images/13-14.png" class="step-img">
                        <p class="margin-text">（2） 编辑环境</p>
                        <p class="margin-text">编辑环境和新建环境参数类对
                            <br>PS：当环境创建好之后，强烈建议 不要修改Kubernetes Ctx/namespace 等字段
                        </p>
                        <img src="./assets/manual-images/13-15.png" class="step-img">
                        <p class="margin-text">（3） 环境锁定</p>
                        <p class="margin-text">项目管理员可以锁定和解锁定环境，当环境处于被锁定状态时，该环境不允许被修改，不允许有该环境的流水线运行。</p>
                        <img src="./assets/manual-images/13-16.png" class="step-img">
                        <img src="./assets/manual-images/13-17.png" class="step-img">
                        <p class="margin-text">（4） 环境配置</p>
                        <p class="margin-text">环境配置，是配置实例在集群中的所有参数与行为</p>
                        <img src="./assets/manual-images/13-18.png" class="step-img">
                        <p class="margin-text">（5） 基本配置</p>
                        <p class="margin-text">基本配置，包含实例启动的最少配置，实例数量，资源限制等</p>
                        <img src="./assets/manual-images/13-19.png" class="step-img">
                        <p class="margin-text">（6） 环境变量，自定义dns解析</p>
                        <p class="margin-text">当用户有这类需求时，有用户自定使用
                            <br>PS： 环境变量中的ENV 被系统所使用，如果配置会被覆盖，请注意
                        </p>
                        <img src="./assets/manual-images/13-20.png" class="step-img">
                        <p class="margin-text">（7） 配置文件</p>
                        <p class="margin-text">配置文件可以将文件，直接挂载到容器里面去，也可以使用配置文件来配置环境变量。</p>
                        <img src="./assets/manual-images/13-21.png" class="step-img">
                        <p class="margin-text">（8） 服务暴露</p>
                        <p class="margin-text">服务暴露氛围service+ingress</p>
                        <p class="margin-text">1. Service</p>
                        <p class="margin-text">Service
                            是内部服务暴露，一个集群namespace中的不同微服务可以开放service相互调用；service分为内部和外部，外部service可以通过ip+端口方式来访问应用</p>
                        <img src="./assets/manual-images/13-22.png" class="step-img">
                        <img src="./assets/manual-images/13-23.png" class="step-img">
                        <p class="margin-text">2. Ingress</p>
                        <p class="margin-text">Ingress配置了一个可以访问应用的域名地址；选择一个内部访问的service，配置一些ingress参数（参考nginx参数）
                            <br>注意：配置ingress 需要先配置一个内部访问的service作为前提
                        </p>
                        <img src="./assets/manual-images/13-24.png" class="step-img">

                        <p class="step-text">四、流水线</p>
                        <p class="margin-text">（1） 流水线列表</p>
                        <p class="margin-text">显示该应用的所有流水线信息；每条流水线有发布记录/查看/编辑/删除/复制等功能</p>
                        <img src="./assets/manual-images/13-25.png" class="step-img">
                        <p class="margin-text">（2） 新建流水线</p>
                        <p class="margin-text">新建一个该应用的流水线，可以选择不同的步骤，每个步骤需要配置参数，从而形成一条可执行的流水线</p>
                        <img src="./assets/manual-images/13-26.png" class="step-img">
                        <p class="margin-text">（3） 查看流水线</p>
                        <p class="margin-text">会显示流水线详情信息和最后一次执行结果</p>
                        <img src="./assets/manual-images/13-27.png" class="step-img">
                        <p class="margin-text">（4） 运行流水线</p>
                        <p class="margin-text">点击“运行流水线”会出现一个弹窗 选择需要运行的代码分支/tag 来执行流水线</p>
                        <img src="./assets/manual-images/13-28.png" class="step-img">
                        <p class="margin-text">（5） 停止流水线</p>
                        <p class="margin-text">当一个流水线正在运行时，该按钮会生效，可以停止当前正在运行的流水线。</p>
                        <img src="./assets/manual-images/13-29.png" class="step-img">
                        <p class="margin-text">（5） 编辑流水线</p>
                        <p class="margin-text">编辑流水线会显示该流水线的所有步骤，以及各个步骤的配置细节</p>
                        <img src="./assets/manual-images/13-30.png" class="step-img">
                        <p class="margin-text">（6） 删除流水线</p>
                        <p class="margin-text">会删除该流水线，但是不会删除该流水线运行创建的实例</p>
                        <p class="margin-text">（7） 发布记录</p>
                        <p class="margin-text">点击流水线的“发布记录”可以查看到该流水线运行的记录，成功/失败 日志和回滚等信息</p>
                        <img src="./assets/manual-images/13-31.png" class="step-img">

                        <p class="step-text">五、使用样例</p>
                        <p class="margin-text">（1） 创建项目</p>
                        <img src="./assets/manual-images/13-32.png" class="step-img">
                        <p class="margin-text">（2） 创建应用</p>
                        <p class="margin-text">进入新建的项目，点击应用菜单，然后“新建”</p>
                        <img src="./assets/manual-images/13-33.png" class="step-img">
                        <p class="margin-text">（3） 填写应用参数和配置</p>
                        <img src="./assets/manual-images/13-34.png" class="step-img">
                        <p class="margin-text">（4） 填写构建配置</p>
                        <p class="margin-text">选择对应语言的模版，如果非 java/go/nodejs
                            则选择不使用模版，构建路径由代码觉得是否配置，为代码编译目录，高级选项中可以配置属于应用的harbor地址和鉴权信息</p>
                        <img src="./assets/manual-images/13-35.png" class="step-img">
                        <p class="margin-text">（5） 部署类型</p>
                        <p class="margin-text">PS： 现在只支持 标准kubernetes部署方式</p>
                        <p class="margin-text">（6） 完成页面</p>
                        <p class="margin-text">可以查看刚刚所有的配置情况，再次确实是否需要更改；最后点击提交</p>
                        <p class="margin-text">PS： 应用名称一旦创建无法更改，其他参数可以随时修改</p>
                        <p class="margin-text">（7） 查看新建的应用</p>
                        <p class="margin-text">创建应用后自动跳到改页</p>
                        <img src="./assets/manual-images/13-36.png" class="step-img">
                        <p class="margin-text">（8） 进入应用详情</p>
                        <p class="margin-text">单击应用条目 – 进入应用详情页面</p>
                        <img src="./assets/manual-images/13-37.png" class="step-img">
                        <p class="margin-text">（9）创建环境</p>
                        <p class="margin-text">进入环境页面,两个方式都可以进入环境，然后点击新建环境</p>
                        <img src="./assets/manual-images/13-38.png" class="step-img">
                        <p class="margin-text">（10）填写环境基本信息</p>
                        <p class="margin-text">PS： 部署方式只能选择kubernetes部署，暂不支持其他类型部署，然后点击保存完成</p>
                        <img src="./assets/manual-images/13-39.png" class="step-img">
                        <p class="margin-text">（11）查看环境列表，然后配置环境</p>
                        <img src="./assets/manual-images/13-40.png" class="step-img">
                        <p class="margin-text">（12）填写容器基本配置</p>
                        <p class="margin-text">PS： 命名空间可为空，当为空时直接使用环境的中的namespace，如果不为空则使用改配置的namespace；点击保存</p>
                        <img src="./assets/manual-images/13-41.png" class="step-img">
                        <p class="margin-text">（13）创建流水线</p>
                        <p class="margin-text">进入流水线列表页面，然后点击“新建”</p>
                        <img src="./assets/manual-images/13-42.png" class="step-img">
                        <p class="margin-text">（14）进入流水线配置页面</p>
                        <p class="margin-text">点击新建后，进入流水线配置页面，在该页面配置完成流水线的所有配置；填写流水线名称</p>
                        <p class="margin-text">PS： 流水线名称一旦创建，不能修改</p>
                        <img src="./assets/manual-images/13-43.png" class="step-img">

                        <p class="margin-text">1. 配置代码编辑构建</p>
                        <p class="margin-text">选择代码构建，然后填写代码构建捕捉需要的参数</p>
                        <img src="./assets/manual-images/13-44.png" class="step-img">
                        <p class="margin-text">2. 配置镜像构建</p>
                        <p class="margin-text">Dockerfile为Dockerfile文件在代码中的相对位置；可以直接写目录名称，也可以写“./Dockerfile”地址</p>
                        <p class="margin-text">“流水线运行时重新指定tag“ 勾选后运行流水线时可以自由自定镜像tag</p>
                        <img src="./assets/manual-images/13-45.png" class="step-img">
                        <p class="margin-text">3. 配置部署</p>
                        <p class="margin-text">在部署中直接选择需要部署的环境</p>
                        <p class="margin-text">PS：环境时从环境列表中获取</p>
                        <img src="./assets/manual-images/13-46.png" class="step-img">
                        <p class="margin-text">4. 完成流水线配置，保存</p>
                        <p class="margin-text">（15）运行流水线</p>
                        <p class="margin-text">1. 重新进入流水线列表，单击进入该流水线</p>
                        <img src="./assets/manual-images/13-47.png" class="step-img">
                        <p class="margin-text">2. 点击运行流水线，选择运行分支，确认运行</p>
                        <img src="./assets/manual-images/13-48.png" class="step-img">
                        <p class="margin-text">3. 在该页面等待，查看流水线运行情况</p>
                        <img src="./assets/manual-images/13-49.png" class="step-img">
                        <p class="margin-text">4. 执行完成</p>
                        <img src="./assets/manual-images/13-50.png" class="step-img">
                        <p class="margin-text">（16）查看应用实例情况</p>
                        <img src="./assets/manual-images/13-51.png" class="step-img">

                    </div>
                </div>
            </article>

            <!--云监控-->
            <article *ngIf="content === 'monitorProduction'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">产品概述——云监控</p>
                        <p class="p-indent">获取云主机的监控指标，探测服务可用性，以及针对指标设置警报。实时掌握资源及应用的运行状态，保证服务及应用稳定运行。
                        </p>
                        <p class="step-text">产品优势：</p>
                        <ul class="article-ul">
                            <li>
                                <p>多维监控：丰富的监控指标，多维云主机监控</p>
                            </li>
                            <li>
                                <p>灵活易用：即开即删，只需简单几步操作即可监控云主机</p>
                            </li>
                            <li>
                                <p>告警能力：强大灵活的告警功能，能全方位监控，及时告警自己的云主机健康状况</p>
                            </li>
                        </ul>
                        <p class="step-text">应用场景：</p>
                        <ul class="article-ul">
                            <li>
                                <p>
                                    AIC应用托管平台建立的主机
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </article>
            <!--云监控—操作实例-->
            <article *ngIf="content === 'monitorOperate'">
                <p class="p-title">云效平台操作</p>
                <div class="article-content">
                    <div class="original-content">
                        <p class="title-l2">相关操作：</p>
                        <p class="step-text">一、监控大盘</p>
                        <p class="margin-text">（1） 创建监控大盘</p>
                        <p class="margin-text">
                            点击右上角【+】号，输入监控大盘名称，点击确定（注意名称有相应的输入规范检查:名称仅允许包含数字、大小写字母、中文、“.”、“_”或“-”长度不超过64个字符）即可创建监控大盘</p>
                        <img src="./assets/manual-images/14-1.png" class="step-img">
                        <img src="./assets/manual-images/14-2.png" class="step-img">
                        <p class="margin-text">（2） 删除监控大盘</p>
                        <p class="margin-text">点击左上角【X】号能删除你创建的监控大盘</p>
                        <img src="./assets/manual-images/14-3.png" class="step-img">
                        <p class="margin-text">（3） 修改监控大盘信息</p>
                        <p class="margin-text">点击【更多操作】下拉按钮弹出2个操作选择项</p>
                        <img src="./assets/manual-images/14-4.png" class="step-img">
                        <p class="margin-text">点击【修改名称】可以修改监控大盘名称</p>
                        <img src="./assets/manual-images/14-5.png" class="step-img">
                        <p class="margin-text">
                            点击【添加图表】，弹出新建大盘的弹出框即可在监控大盘中新建图表，“图表名称”输入框输入你新建的图表名称。云资源下拉框选择你新建的云主机，
                            注意云注意一定是开机的云主机才会出现在下拉选项种。监控指标下拉选择框选择你上面选择的资源类型所属的监控指标。具体指标说明请查看“指标说明”，
                            里面会对所有云监控支持的监控指标详细说明。阈值分为3种：警告、严重、紧急，这3个严重程度的输入值由小到大，这3个阈值会出现在图表上，
                            方便用户查看自己的指标有没有超过自己的监控指标。资源类型目前有4种：CPU，网络，磁盘，内存，云监控主要从这4个方面监控。
                        </p>
                        <img src="./assets/manual-images/14-6.png" class="step-img">
                        <p class="margin-text">（4） 图表元素说明</p>
                        <p class="margin-text">
                            将指针放到图表上则可以查看在横坐标对应时间的监控指标的值。点击“1小时”、“1小时”、“2小时”、“1天”、“7天”、“30天”，
                            则可以从现在到过去的相应时间段的监控指标数据。
                            可以选择勾选“环比1天”、“环比7天”，则可以查看该云主机在当前时间的1天或者7天前的监控指标数据。
                        </p>
                        <img src="./assets/manual-images/14-7.png" class="step-img">
                        <p class="margin-text">点击红色指针所示处查看图表详情：弹框最上面显示的是你监控的虚拟机名称，显示该图表的监控指标，采集周期是5分钟采集一次数据</p>
                        <img src="./assets/manual-images/14-8.png" class="step-img">
                        <p class="margin-text">点击红色指针所示处查看大图</p>
                        <img src="./assets/manual-images/14-9.png" class="step-img">
                        <p class="margin-text">弹出功能更多的图表弹框。注意环比和聚合操作互互斥，即不能同时选择。</p>
                        <img src="./assets/manual-images/14-10.png" class="step-img">
                        <p class="margin-text">点击红色【修改】按钮，则可以修改创建的图表</p>
                        <img src="./assets/manual-images/14-11.png" class="step-img">
                        <img src="./assets/manual-images/14-12.png" class="step-img">
                        <p class="margin-text">点击【删除】按钮则可以快速删除你创建的图表</p>
                        <img src="./assets/manual-images/14-13.png" class="step-img">
                        <p class="step-text">二、告警管理</p>
                        <p class="margin-text">点击左侧二级目录栏的“告警管理”，则显示以下5个标签：“告警症状”“告警定义”“告警规则”“联系组”“消息接收”。</p>
                        <img src="./assets/manual-images/14-14.png" class="step-img">
                        <p class="step-text">1. 告警症狀</p>
                        <p class="margin-text">（1） 告警症状是给监控指标设置某个阈值，超过这个阈值，则会触发一些告警方面的操作。</p>
                        <p class="margin-text">首先点击“新增”告警症状新建一个告警症状</p>
                        <img src="./assets/manual-images/14-15.png" class="step-img">
                        <p class="margin-text">给该告警症状设置某个名称，监控指标首先选择资源类型，然后选择该资源类型下的监控指标，然后条件可以选择=、!=、&lt;、>、&lt;=、>=,
                            让后选择一个阈值。监控等级有“告警”“紧急”“严重”，此条件对系统没有意义，给人直观查看该阈值对你的业务影响程度确定。</p>
                        <img src="./assets/manual-images/14-16.png" class="step-img">
                        <p class="margin-text">2. 告警定义</p>
                        <p class="margin-text">作用是把告警症状和要监控的主机资源管理起来</p>
                        <p class="margin-text">输入名称，选择告警症状，选择监控对象，监控等级，以及等待周期和取消周期。
                            “等待周期”包含在警示定义中的症状保持触发状态达到此收集周期数（收集周期为五分钟）之后，便会生成警示。值必须大于等于 1。
                            此设置可帮助您调整环境中的敏感度。警示定义的等待周期将添加到症状定义的等待周期。在大多数定义中，您可以配置症状级别的敏感度，
                            并将警示定义的等待周期配置为 1。此配置可确保在所需的症状敏感度级别触发所有症状后，立即触发警示。
                        </p>
                        <p class="margin-text">“取消周期”如果在达到此收集周期（收集周期为五分钟）数之后取消警示，症状将取消。值必须大于等于 1。
                            此设置可帮助您调整环境中的敏感度。警示定义的取消周期将添加到症状定义的取消周期。在大多数定义中，您可以配置症状级别的敏感度，并将警示定义的等待周期配置为 1。
                            此配置可确保在经过所需的症状取消周期后所有的症状条件均消失之后，立即取消警示。
                        </p>
                        <img src="./assets/manual-images/14-17.png" class="step-img">
                        <p class="margin-text">3. 消息接收</p>
                        <p class="margin-text">
                            消息接收的功能是定义接收联系人的信息。输入用户名，邮箱，手机号，备注，点击确定即可保存。注意一定要保证邮箱的正确，否则会导致收不到告警邮件。点击确定。</p>
                        <img src="./assets/manual-images/14-18.png" class="step-img">
                        <p class="margin-text">4. 联系组</p>
                        <p class="margin-text">在添加完联系人之后，则要把联系人添加到联系组，方便统一告警。
                            输入名称，选择你的成员，点击确定。
                        </p>
                        <img src="./assets/manual-images/14-19.png" class="step-img">
                        <p class="margin-text">5. 告警规则</p>
                        <p class="margin-text">在这里把告警定义和联系组管理起来，会指定把告警通知邮件告知给联系组的联系人。</p>
                        <img src="./assets/manual-images/14-20.png" class="step-img">
                        <p class="margin-text">三、告警通知</p>
                        <p class="margin-text">此处就会显示根据你的告警定义产生的一些告警，一个新的告警是处在激活状态，你可以点击铃铛按钮，取消告警。当告警处在取消状态的时候，可以删除该告警。
                        </p>
                        <img src="./assets/manual-images/14-21.png" class="step-img">
                    </div>
                </div>
            </article>
            <!--云监控—指标说明-->
            <article *ngIf="content === 'monitorExplain'">
                <div class="article-content">
                    <div class="original-content">
                        <p class="p-title">指标说明</p>
                        <nz-table nzTemplateMode class="help-menu-table" [nzBordered]=true>
                            <thead>
                                <tr>
                                    <th>名词</th>
                                    <th width=20%>监控指标</th>
                                    <th width=20%>名称</th>
                                    <th width=50%>描述</th>
                                    <th>单位</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td rowspan=2>CPU</td>
                                    <td>
                                        <span>cpu|iowait</span>
                                    </td>
                                    <td>
                                        <span>IO 等待</span>
                                    </td>
                                    <td>
                                        <span>虚拟机 CPU 等待 IO 的时间百分比。公式为“等待 - 闲置 - 交换等待”。高值表示低存储子系统</span>
                                    </td>
                                    <td>
                                        %
                                    </td>
                                </tr>
                                <tr>
                                    <td>cpu|usage_average</td>
                                    <td>
                                        <span>CPU使用情况</span>
                                    </td>
                                    <td>
                                        <span>按虚拟机 CPU 配置划分的 CPU 使用情况 (MHz)</span>
                                    </td>
                                    <td>%</td>
                                </tr>
                                <tr>
                                    <td rowspan=11>磁盘</td>
                                    <td>
                                        <span>virtualDisk|commandsAveraged_average</span>
                                    </td>
                                    <td>
                                        <span>
                                            磁盘|IOPS 合计
                                        </span>
                                    </td>
                                    <td><span>每秒读取/写入操作数。这取报告时间段内的平均值</span></td>
                                    <td>次</td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|totalLatency</td>
                                    <td>
                                        <span>
                                            磁盘|总延迟
                                        </span>
                                    </td>
                                    <td>
                                        <span>磁盘 IO 总延迟</span>
                                    </td>
                                    <td>
                                        <span>毫秒</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|totalReadLatency_average</td>
                                    <td>
                                        <span>磁盘|读取延迟</span>
                                    </td>
                                    <td>
                                        <span>磁盘读取延迟</span>
                                    </td>
                                    <td>
                                        <span>毫秒</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|totalWriteLatency_average</td>
                                    <td>
                                        <span>磁盘|写入延迟</span>
                                    </td>
                                    <td>
                                        <span>磁盘写入延迟</span>
                                    </td>
                                    <td>
                                        <span>毫秒</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|numberWriteAveraged_average</td>
                                    <td>
                                        <span>磁盘|写入 IOPS</span>
                                    </td>
                                    <td>
                                        <span>磁盘写入 IOPS</span>
                                    </td>
                                    <td>
                                        <span>-</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|usage</td>
                                    <td>
                                        <span>磁盘|总吞吐量</span>
                                    </td>
                                    <td>
                                        <span>总吞吐量</span>
                                    </td>
                                    <td>
                                        <span>%</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|vDiskOIO</td>
                                    <td>
                                        <span>磁盘|未完成 IO 请求数</span>
                                    </td>
                                    <td>
                                        <span>未完成 IO 请求数</span>
                                    </td>
                                    <td>
                                        <span>-</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|numberReadAveraged_average</td>
                                    <td>
                                        <span>磁盘|读取 IOPS</span>
                                    </td>
                                    <td>
                                        <span>读取 IOPS</span>
                                    </td>
                                    <td>
                                        <span>-</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|write_average</td>
                                    <td>
                                        <span>磁盘|写入吞吐量</span>
                                    </td>
                                    <td>
                                        <span>写入吞吐量</span>
                                    </td>
                                    <td>
                                        <span>KBps</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|actualUsage</td>
                                    <td>
                                        <span>磁盘|已用磁盘空间</span>
                                    </td>
                                    <td>
                                        <span>已用磁盘空间</span>
                                    </td>
                                    <td>
                                        <span>GB</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>virtualDisk|read_average</td>
                                    <td>
                                        <span>磁盘|读取吞吐量</span>
                                    </td>
                                    <td>
                                        <span>读取吞吐量</span>
                                    </td>
                                    <td>
                                        <span>KBps</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td rowspan=3>网络</td>
                                    <td>
                                        <span>net|received_average</span>
                                    </td>
                                    <td>
                                        <span>网络|数据接收速度</span>
                                    </td>
                                    <td>
                                        <span>每秒收到的平均数据量</span>
                                    </td>
                                    <td>
                                        KBps
                                    </td>
                                </tr>
                                <tr>
                                    <td>net|usage_average</td>
                                    <td>
                                        <span>网络|使用速率</span>
                                    </td>
                                    <td>
                                        <span>虚拟机的所有 NIC 实例所传输和接收的数据总计</span>
                                    </td>
                                    <td>
                                        KBps
                                    </td>
                                </tr>
                                <tr>
                                    <td>net|transmitted_average</td>
                                    <td>
                                        <span>网络|数据传输速度</span>
                                    </td>
                                    <td>
                                        <span>每秒传输的平均数据量</span>
                                    </td>
                                    <td>
                                        KBps
                                    </td>
                                </tr>
                                <tr>
                                    <td rowspan=8>内存</td>
                                    <td>
                                        <span>mem|swapped_average</span>
                                    </td>
                                    <td>
                                        <span>内存|已交换</span>
                                    </td>
                                    <td>
                                        <span>虚拟机显示正在交换的</span>
                                    </td>
                                    <td>
                                        KB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|swapinRate_average</span>
                                    </td>
                                    <td>
                                        <span>内存|换入速率</span>
                                    </td>
                                    <td>
                                        <span>虚拟机在该收集时间间隔内将内存从磁盘换入活动内存的速率</span>
                                    </td>
                                    <td>
                                        KBps
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|guest_usage</span>
                                    </td>
                                    <td>
                                        <span>内存|客户机使用情况</span>
                                    </td>
                                    <td>
                                        <span>客户机内存可用量</span>
                                    </td>
                                    <td>
                                        KB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|usage_average</span>
                                    </td>
                                    <td>
                                        <span>内存|使用情况</span>
                                    </td>
                                    <td>
                                        <span>当前正在使用的内存占可用总内存的百分比</span>
                                    </td>
                                    <td>
                                        %
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|workload</span>
                                    </td>
                                    <td>
                                        <span>内存|工作负载</span>
                                    </td>
                                    <td>
                                        <span>工作负载</span>
                                    </td>
                                    <td>
                                        %
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|swapoutRate_average</span>
                                    </td>
                                    <td>
                                        <span>内存|换出速率</span>
                                    </td>
                                    <td>
                                        <span>在当前时间间隔内将内存从活动内存换到磁盘的速率</span>
                                    </td>
                                    <td>
                                        KBps
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|overheadMax_average</span>
                                    </td>
                                    <td>
                                        <span>内存|最大开销</span>
                                    </td>
                                    <td>
                                        <span>最大开销</span>
                                    </td>
                                    <td>
                                        KB
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span>mem|consumed_average_mtd</span>
                                    </td>
                                    <td>
                                        <span>内存|平均消耗情况 MTD</span>
                                    </td>
                                    <td>
                                        <span>最大开销</span>
                                    </td>
                                    <td>
                                        KB
                                    </td>
                                </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </div>
            </article>
        </section>
    </main>
</div>

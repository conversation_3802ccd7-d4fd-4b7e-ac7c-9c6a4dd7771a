<div class="vdi-config config-content">
    <div class="panel panel-left">
        <div class="panel-header">
            <a routerLink="../index" class="back title">
                <i class="icon" nz-icon nzType="left" nzTheme="outline"></i>创建桌面虚拟机
            </a>
        </div>
        <div class="panel-body">
            <form [formGroup]="vdiForm" (submit)="createVdi()">
                <section class="field-section">
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <div class="label-text">名称</div>
                                <input required maxlength="50" type="text" formControlName="name" placeholder="请输入桌面虚拟机名称" />
                                <span class="small tip">
                                    名称可包含字母、数字和连字符，且必须以字母开头，长度为1-50个字符
                                </span>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vdiForm.get('name'))">
                                <div *ngIf="vdiForm.get('name').hasError('required')">
                                    名称不能为空
                                </div>
                                <div *ngIf="vdiForm.get('name').hasError('maxlength')">
                                    名称长度不能超过{{ vdiForm.get('name').errors.maxlength.requiredLength }}个字符
                                </div>
                                <div *ngIf="vdiForm.get('name').hasError('pattern')">
                                    名称不符合规范
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="field-section">
                    <div class="field-group">
                        <div class="field-item required">
                            <label>
                                <div class="label-text">镜像</div>
                                <nz-select formControlName="imageId" nzPlaceHolder="请选择镜像">
                                    <nz-option *ngFor="let image of imageList" [nzValue]="image.id"
                                               [nzLabel]="image.name">
                                    </nz-option>
                                </nz-select>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vdiForm.get('imageId'))">
                                <div *ngIf="vdiForm.get('imageId').hasError('required')">
                                    请选择镜像
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="field-section">
                    <div class="field-group">
                        <div class="field-item required">
                            <label style="width: 100%">
                                <span class="label-text">配置列表</span>
                                <div style="max-height: 320px;overflow: auto;">
                                    <nz-empty *ngIf="!servicePlanList || servicePlanList.length === 0" style="border: 1px solid rgba(0, 0, 0, 0.06)"></nz-empty>
                                    <ul class="server-config-list" *ngIf="servicePlanList && servicePlanList.length > 0">
                                        <li *ngFor="let item of servicePlanList"
                                            [ngClass]="{
                                                'active': vdiForm.value.servicePlanId === item.id,
                                                'disabled': !isServicePlanAvailable(item.id)
                                            }"
                                            (click)="selectServicePlan(item.id)">
                                            <p class="cpu">{{ item.cpu }}核</p>
                                            <p class="memory">{{ item.memory }}G</p>
                                        </li>
                                    </ul>
                                </div>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vdiForm.get('servicePlanId'))">
                                <div *ngIf="vdiForm.get('servicePlanId').hasError('required')">
                                    请选择配置
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="field-section">
                    <div class="field-group">
                        <div class="field-item">
                            <label>
                                <div class="label-text">数据盘 (GB)</div>
                                <input type="number" formControlName="disk" placeholder="请输入数据盘大小" min="1" max="1000" />
                                <span class="small tip">可选，不填写则不创建数据盘</span>
                            </label>
                            <div class="form-hint error" *ngIf="isInvalid(vdiForm.get('disk'))">
                                <div *ngIf="vdiForm.get('disk').hasError('min')">
                                    数据盘大小不能小于1GB
                                </div>
                                <div *ngIf="vdiForm.get('disk').hasError('max')">
                                    数据盘大小不能超过1000GB
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </form>
        </div>
    </div>
    <div class="panel panel-right">
        <div class="panel-aside pined">
            <section class="field-section">
                <div class="field-title">
                    配置概要
                </div>
                <table class="form-info" *ngIf="vdiForm">
                    <tbody>
                        <tr>
                            <td style="width: 35%;">名称</td>
                            <td>{{ vdiForm.value.name || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>镜像</td>
                            <td>{{ getSelectedImageName() || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>配置</td>
                            <td>{{ getSelectedServicePlanName() || '-'}}
                            </td>
                        </tr>
                        <tr>
                            <td>数据盘</td>
                            <td>{{ vdiForm.value.disk ? (vdiForm.value.disk + 'GB') : '-'}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>
            <section class="field-section action">
                <button [nzLoading]="isCreating"
                        (click)="createVdi()"
                        type="button" nz-button
                        nzSize="large"
                        nzType="primary">创建</button>
            </section>
        </div>
    </div>
</div>

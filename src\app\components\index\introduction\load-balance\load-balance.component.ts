import { Component, OnInit } from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-load-balance',
    templateUrl: './load-balance.component.html',
    styleUrls: ['./load-balance.component.less']
})
export class LoadBalanceComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '负载均衡',
        enName: 'Load Balancing',
        desc: '负载均衡会将互联网或内网的流量分发至多台后端云服务器，消除服务的单点故障，达到提高应用系统可用性和可靠性的要求。',
        bgColor: '#1a284d',
        orderLink: '/console/load-balance/load-balance-config',
        type: 'loadBalanceProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

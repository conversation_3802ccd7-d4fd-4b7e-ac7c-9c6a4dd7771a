<!--banner图-->
<!-- <div class="banner">
    <img src="./assets/images/index-banner.jpg" alt="">
</div> -->
<!--低价格、低时延、低门槛-->
<!-- <div class="main-f1 clearfix">
    <div class="main-margin">
        <div nz-row>
            <div nz-col nzSpan="8">
                <div class="main-f1-main">
                    <div class="main-f1-left">
                        <i class="main-f-icon1"></i>
                    </div>
                    <div class="main-f1-right">
                        <h6>高可靠</h6>
                        <div class="line"></div>
                        <div class="main-f1-text">
                            采用业界最先进的虚拟化技术，保障服务的可靠性
                        </div>
                    </div>
                </div>
            </div>
            <div nz-col nzSpan="8">
                <div class="main-f1-main">
                    <div class="main-f1-left">
                        <i class="main-f-icon2"></i>
                    </div>
                    <div class="main-f1-right">
                        <h6>低时延</h6>
                        <div class="line"></div>
                        <div class="main-f1-text">
                            与AIC PaaS服务共享集群，相比使用第三方服务器可有效降低时延。
                        </div>
                    </div>
                </div>
            </div>
            <div nz-col nzSpan="8">
                <div class="main-f1-main">
                    <div class="main-f1-left">
                        <i class="main-f-icon3"></i>
                    </div>
                    <div class="main-f1-right">
                        <h6>低门槛</h6>
                        <div class="line"></div>
                        <div class="main-f1-text">
                            支持先使用后付费的模式，让你以更低的成本进行应用部署、测试和商用。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->
<!--云计算产品-->
<!-- <div class="main-f2 clearfix">
    <div class="main-margin">
        <div class="main-f2-title">
            <h4>安全、稳定的云计算产品</h4>
            <span>为开发者的需求而设计，透明实惠的定价</span>
        </div>
        <div class="main-f2-box">
            <div class="main-f2-col yfwq-box">
                <a href="javascript:void(0)" routerLink="/introduction/cloud-server">
                    <div class="main-f2-tit">
                        <i class="icon yfwq-bg"></i>
                        <p>云服务器</p>
                    </div>
                    <div class="main-f2-txt">
                        安全可靠，性价比高，帮助用户打造灵活高效的、适用各类应用场景的弹性云服务器
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col yp-box">
                <a href="javascript:void(0)" routerLink="/introduction/cloud-disk">
                    <div class="main-f2-tit">
                        <i class="icon yp-bg"></i>
                        <p>云盘</p>
                    </div>
                    <div class="main-f2-txt">
                        持久化、大容量、高可靠的块级数据存储服务，支持动态挂载、扩容、快照、回滚等功能，满足云服务器的数据存储需求
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col txgw-box">
                <a href="javascript:void(0)" routerLink="/introduction/elastic-public-network-ip">
                    <div class="main-f2-tit">
                        <i class="icon txgw-bg"></i>
                        <p>弹性公网IP</p>
                    </div>
                    <div class="main-f2-txt">
                        独立的公网IP，可动态绑定和解绑，实现服务对外部网络可用，满足客户向公网客户提供服务的需求
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col zywl-box">
                <a href="javascript:void(0)" routerLink="/introduction/proprietary-network">
                    <div class="main-f2-tit">
                        <i class="icon zywl-bg"></i>
                        <p>专有网络</p>
                    </div>
                    <div class="main-f2-txt">
                        安全隔离、灵活、易扩展的网络环境，满足服务之间安全风险隔离的需求
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col fzjh-box">
                <a href="javascript:void(0)" routerLink="/introduction/load-balance">
                    <div class="main-f2-tit">
                        <i class="icon fzjh-bg"></i>
                        <p>负载均衡</p>
                    </div>
                    <div class="main-f2-txt">
                        对多台云服务器进行流量分发，扩展对外服务能力，消除单点故障，提升应用系统的可用性
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col rds-box">
                <a href="javascript:void(0)" routerLink="/introduction/rds">
                    <div class="main-f2-tit">
                        <i class="icon rds-bg"></i>
                        <p>RDS</p>
                    </div>
                    <div class="main-f2-txt">
                        高可靠、高可用的MySQL、MariaDB关系型数据库服务，提供了双机热备、故障恢复、业务监控，安全隔离等特性
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col dxcc-box">
                <a href="javascript:void(0)" routerLink="/introduction/object-storage">
                    <div class="main-f2-tit">
                        <i class="icon dxcc-bg"></i>
                        <p>对象存储</p>
                    </div>
                    <div class="main-f2-txt">
                        高安全、高可靠、大容量、低成本等特点的对象存储产品，用于存储图片、音视频、文档等非结构化数据，并实现在线管理数据
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col txss-box">
                <a href="javascript:void(0)" routerLink="/introduction/elastic-scaling">
                    <div class="main-f2-tit">
                        <i class="icon txss-bg"></i>
                        <p>弹性伸缩</p>
                    </div>
                    <div class="main-f2-txt">
                        基于伸缩策略的弹性伸缩功能，帮助用户按需使用，降低成本，实现自动化运维。
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col wjcc-box">
                <a href="javascript:void(0)" routerLink="/introduction/file-storage">
                    <div class="main-f2-tit">
                        <i class="icon wjcc-bg"></i>
                        <p>文件存储</p>
                    </div>
                    <div class="main-f2-txt">
                        通用可靠、灵活扩展、性价比高，满足客户实现文件共享、大文件存储、数据汇总的业务需求。
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col xxdl-box">
                <a href="javascript:void(0)" routerLink="/introduction/message-queues">
                    <div class="main-f2-tit">
                        <i class="icon xxdl-bg"></i>
                        <p>消息队列</p>
                    </div>
                    <div class="main-f2-txt">
                        100%兼容开源 Kafka API（2.0.X及以上），帮助用户快速尚云。作为消息中间件，帮助用户实现消息生产者与消费者之间的解耦，无需彼此等待
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col rqfw-box">
                <a href="javascript:void(0)" routerLink="/introduction/container">
                    <div class="main-f2-tit">
                        <i class="icon rqfw-bg"></i>
                        <p>容器服务</p>
                    </div>
                    <div class="main-f2-txt">
                        提供高性能容器化管理能力的基于原生Kubernetes的容器管理服务及存储、分发Docker镜像，一键部署，开箱即用的私有镜像仓库服务(Harbor)
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
            <div class="main-f2-col xnpt-box">
                <a href="javascript:void(0)" routerLink="/introduction/devops">
                    <div class="main-f2-tit">
                        <i class="icon xnpt-bg"></i>
                        <p>云效平台</p>
                    </div>
                    <div class="main-f2-txt">
                        一站式企业研发平台，为企业用户提供从“需求->开发->测试->发布->运维”端到端的协同服务和研发工具支撑。
                    </div>
                    <button>了解详情</button>
                </a>
            </div>
        </div>
    </div>
</div> -->

<div class="home-page">
    <div id="banner" class="banner">
        <div class="banner-item"
            [ngStyle]="{backgroundColor: banner.bgColor || 'transparent'}">
            <div class="on-container">
                <div class="banner-text">
                    <h3 class="banner-title">
                        {{ banner.title }}</h3>
                    <div class="banner-desc">{{ banner.desc }}
                    </div>
                </div>
                <app-sub-img></app-sub-img>
            </div>
        </div>
    </div>
    <div class="feature">
        <div class="on-container" nz-row>
            <dl class="feature-item reliable" nz-col nzSpan="8">
                <span class="feature-icon"></span>
                <div class="feature-content">
                    <dt class="feature-name">高可靠</dt>
                    <dd class="feature-desc">采用业界最先进的虚拟化技术，保障服务的可靠性。</dd>
                </div>
            </dl>
            <dl class="feature-item low-latency" nz-col nzSpan="8">
                <span class="feature-icon"></span>
                <div class="feature-content">
                    <dt class="feature-name">低时延</dt>
                    <dd class="feature-desc">与AIC PaaS服务共享集群，相比使用第三方服务器可有效降低时延。</dd>
                </div>
            </dl>
            <dl class="feature-item low-threshold" nz-col nzSpan="8">
                <span class="feature-icon"></span>
                <div class="feature-content">
                    <dt class="feature-name">低门槛</dt>
                    <dd class="feature-desc">支持先使用后付费的模式，让你以更低的成本进行应用部署、测试和商用。</dd>
                </div>
            </dl>
        </div>
    </div>
    <div class="products">
        <div class="on-container">
            <h3>安全、稳定的云计算产品</h3>
            <div class="sub-title">
                为开发者的需求而设计，透明实惠的定价
            </div>

            <div class="product-list-container">
                <ul class="product-list">
                    <li class="product-item"
                        *ngFor="let item of productList; index as i"
                        [ngClass]="{'last-row': isLastRow(i), 'last-one': isLastOne(i) }">
                        <a [routerLink]="item.routerLink">
                            <div class="icon {{ item.icon }}"></div>
                            <div class="product-name">{{ item.name }}</div>
                            <p class="product-desc">{{ item.desc }}</p>
                            <span class="check-detail">了解详情</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

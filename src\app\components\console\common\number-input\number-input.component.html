<div class="on-number-input"
    nz-popover
    [nzPopoverContent]="showRange ? getRangeText() : null">
    <button
        type="button"
        nz-button
        [disabled]="!canDecrease()"
        (click)="decrease()"
        nzType="default"><i nz-icon nzType="minus" nzTheme="outline"></i></button>
    <div class="input-area">
        <input [ngModel]="num"
            (ngModelChange)="validateInput($event)"
            (keydown)="keyControl($event)"
            [readonly]="!allowInput"
            type="number"
            appDigitalOnly
            maxlength="20">
    </div>
    <button
        type="button"
        nz-button
        [disabled]="!canIncrease()"
        (click)="increase()"
        nzType="default"><i nz-icon nzType="plus" nzTheme="outline"></i></button>
</div>
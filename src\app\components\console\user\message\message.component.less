@import '../../../../style/common/_variable.less';

.message-container {
    .message-list {
        &.data-list {
            min-height: 130px;
        }
        
        .message-item {
            position: relative;
            border-top: 1px solid @light-gray;
            padding: 15px 10px 15px 15px;
            // transform: translateX(0);
            // transition: transform 0.3s ease 0s;
            &:hover {
                // background-color: #e6f7ff;
                // transform: translateX(10px);
            }
            
            a {
                display: block;
            }

            &.unread {
                &:before {
                    content: '';
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    left: 0;
                    top: 33px;
                    border-radius: 50%;
                    background-color: lighten(@blue, 25%);
                }
            }
        }

        .avatar {
            position: absolute;
            width: 40px;
            height: 40px;
            left: 15px;
            top: 15px;
        }

        .message-body {
            margin-left: 40px + 16px;

            .message-title {
                font-weight: bold;
                margin-bottom: 5px;
            }
            
            .message-summary {
                white-space: pre-line;
                height: 20px;
                line-height: 20px;
                overflow: hidden;
                color: #666;
            }

            .message-info {
                margin-top: 5px;
                .create-time {
                    font-size: 12px;
                    color: #999;
                }
            }
        }
    }

    .load-more {
        border-top: 1px solid @light-gray;
        padding: 20px 0 0;
        text-align: center;
    }
}
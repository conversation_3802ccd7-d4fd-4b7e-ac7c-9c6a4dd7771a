@import '../../../../style/common/_variable.less';
.choose-module .choose-module-inp .inp>input {
    margin: 0 10px 10px 0;
}
.choose-module .choose-module-inp .inp input {
    width: 20%;
    margin: 0;
}
.margin-input {
    margin: 0 5px 0 10px !important;
    padding-left: 5px;
    height: 30px;
}
.back-box {
    width: 200px;
    padding: 20px 0;
    height: 20px;
    line-height: 14px;
    color: #0086df;
    cursor: pointer;
    .line-back {
        vertical-align: top;
    }
    .txt-back {
        display: inline-block;
        margin-left: 10px;
        font-size: 14px;
        // line-height: 22px;
    }
}
.inp, .inp-with-number-input {
    label {
        display: inline-block;
        width: 100px;
        color: #232323;
        font-size: 14px;
        height: 30px;
        line-height: 30px;
    }
}
// 可选框
.choosable-box {
    width: 80%;
    display: inline-block;
    margin-left: 10px;
    // background: red;
    vertical-align: top;
    &>input {
        width: 200px;
    }
    // 删除内容样式
    .choosable-box-item {
        margin-left: 10px;
        .fa {
            cursor: pointer;
        }
    }
    .choosable-box-item-border {
        border: 1px solid #e8e8e8;
    }
    // 增加内容样式
    .choosable-add-text {
        cursor: pointer;
        width: 120px;
        line-height: 30px;
        margin-left: 10px;
        i {
            display: inline-block;
            margin-right: 8px
        }
    }
}
// 带背景颜色模块
.choosable-box-bg {
    // background: @light-gray;
    padding-top: 20px;
    .choosable-box-item-part {
        margin: 15px 0;
        label {
            width: 100px;
        }
        textarea {
            width: 40%;
            height: 120px;
            margin-left: 10px;
            padding: 0 5px 10px 10px;
            vertical-align: top;
            resize: none;
        }
        .input-tips {
            margin-left: 110px;
            margin-top: 10px;
            color: #999;
        }
        .choosable-box-item-part-separate-area {
            display: inline-block;
            width: 350px;
            vertical-align: top;
            margin-left: 15px;
            // background: red;
            .separate-area-title {
                height: 30px;
                line-height: 30px;
            }
            input {
                margin: 0;
                width: 20%;
            }
            .input-before {
                height: 32px;
                line-height: 32px;
            }
            .separate-icon {
                display: inline-block;
                width: 10px;
                height: 0;
                margin: 15px 10px;
                border: 1px solid @black;
            }
            .separete-until {
                display: inline-block;
                margin: 0 5px;
                font-size: 14px;
                line-height: 30px;
            }
        }
        // 第二层可扩展模块
        .choosable-box-item-part-item {
            display: inline-block;
            vertical-align: top;
            width: 550px;
        }
    }
}
/*提示文字*/
.tips-text {
    display: inline-block;
    font-size: 14px;
    color: #666;
    padding: 5px 10px;
    margin: 10px 0 0 8px;
}
.nameInput {
    display: inline-block;
    width: 40%;
}
/*输入提示字体*/
.form-label-tips {
    font-size: 12px;
    color: #999;
    margin-bottom: 20px;
    margin-left: 10px;
}
.number-input {
    width:20%;
    padding: 0 !important;
    margin: 0 !important;
}
.tips-text {
    margin: 0;
    padding: 0;
    color: @gray;
    font-size: 12px;
}
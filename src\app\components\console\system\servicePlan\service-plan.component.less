/*自有样式*/
.operation-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin: 0 5px;
}

.operation-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: #808080;
  background-size: 100% 100%;
}

.able-icon:hover {
  color: #6EB8FF;
}

.disabled-btn:hover {
  color: #aaa;
}

.disabled-btn a{
  color: #aaa;
  cursor: not-allowed;
}

/*状态样式*/
.status-icon {
  display: inline-block;
  margin-right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
}

.fa-spin {
  margin-right: 5px;
}

.loading-status {
  color: #97a4b6;
}

.run-status {
  color: #45bb79;
}

.run-icon {
  background-color: #45bb79;
  border: 1px solid #45bb79;
}

.error-status {
  color: #ec5960;
}

.error-icon {
  background-color: #ec5960;
  border: 1px solid #ec5960;
}

.stop-status {
  color: #97a4b6;
}

.stop-icon {
  background-color: #97a4b6;
  border: 1px solid #97a4b6;
}

.container {
  margin-top: 20px;
}

.on-panel-tips {
  width: 50px !important;
  display: inline-block;
}

.dot-title {
  width: 70px;
  display: inline-block;
  text-align: left;
}

.select-tips {
  font-weight: bold;
}

.view-tips {
  font-weight: bold;
  width: 100px !important;
  display: inline-block;
}

.view-p {
  margin: 0px;
  padding: 10px 0px;
}

.ant-row div:nth-child(odd) {
  font-weight: bold;
}

.height30 {
  height: 30px;
  line-height: 30px;
  text-align: right;
}

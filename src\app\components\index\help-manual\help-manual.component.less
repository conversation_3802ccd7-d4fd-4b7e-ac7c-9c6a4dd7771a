@import '../../../style/common/_variable.less';

a {
    text-decoration: none;
}

.on-container {
    padding-top: 25px;
    padding-bottom: 25px;
}

aside {
    width: 17%;
    display: inline-block;
    vertical-align: top;
    margin-right: 3%;
}

main {
    width: 80%;
    display: inline-block;
    vertical-align: top;
}

/*帮助文档css样式*/
.help-wrap {
    height:100px;
    background-color:#1a284d;
    color:#fff;
    margin-top: 58px;
    line-height: 100px;
}
.help-wrap h1{
    text-align: left;
    padding-top: 25px;
}
.help-content .help-section-wrap{
    width: 1080px;
    margin: 0 auto;
    zoom: 1;
}
.help-content .help-main-content{
    width: auto;
    margin-left: 280px;
    margin-right: 100px;
    float: none !important;
}
.help-content img{
    max-width: 100%;
}
.help-main-right{
    padding-left: 20px;
}


.help-section-list{
    background-color: #fff;
    width: 200px;
    border-right: none!important;

    >ul>li {
        border-bottom: 1px solid #e5e5e5;
    }
}
.help-section-list a{
    color: #343a409e;
}

.help-section-list h3{
    height: 50px;
    padding: 0px 20px;
    margin: 0;
    line-height: 50px;
    border: none;
    border-top: 1px solid #e5e5e5;
    font-size: 24px;
    text-align: center;
}
.help-section-list ul{
    border-top: 1px solid #e5e5e5;
    padding: 0;
    text-align: center;
    background-color: #fff;
    li, div{
        text-align: left;
    }
}
.help-section-list li:last-child{
   margin-bottom: 0;
}

.help-section-list li h3{
    border-top: 1px solid #e5e5e5;
    position: relative;
    font-size: 16px;
    line-height: 40px;
    text-align: center
}
.help-popup-nav{
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 0;
    visibility: hidden;
}
.help-popup-nav.is-visible-nav {
    opacity: 1;
    visibility: visible;
    position: relative;
}
.help-popup-container-nav {
    margin-left: 25px;
    width: 125px;
    /*top: 50px;*/
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    -webkit-backface-visibility: hidden;
    -webkit-transition-property: -webkit-transform;
    -moz-transition-property: -moz-transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -ms-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
}
.help-popup-container-nav li{
    list-style: none;
}

.help-popup-container-nav a{
    display: block;
    text-align: center;
    color: #232323;
    line-height: 40px;
    border-bottom: 1px solid #cccccc;
    background: #e8e8e8ed;
    font-size: 14px;
}

.help-popup-container-nav a:hover{
    color: #0083FF;
}
/**
 詹林翰  2019/06/28
**/
.help-menu-title {
    font-size: 20px;
    text-align: center;
    margin: 5px 0;
}
p {
    margin: 0;
}
.article-ul {
    li {
        list-style-type: initial;
        margin: 5px 0;
    }
    .article-ol {
        li {
            list-style-type: decimal;
            margin-left: 40px;
        }
    }
}
.article-ol {
    li {
        list-style-type: decimal;
    }
}
.title-l2 {
    font-size: 20px;
    margin: 10px 0;
}
.p-title {
    font-weight: bold;
    font-size: 24px;
}
.p-indent {
    margin: 10px;
    text-indent: 2em;
}
.step-text {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
}
.margin-text {
    margin: 15px 0;
}
.step-img {
    width: 900px;
}
.download-link {
    color: @primary;
    text-decoration: none;
}
.indent-text {
    text-indent: 2em;
}
.help-menu-table td{
    padding: 10px 5px;
    span {
        word-break: break-all;
    }
}
.red-text {
    color: @red;
}
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-help',
    templateUrl: './help.component.html',
    styleUrls: ['./help.component.less']
})
export class HelpComponent implements OnInit {
    constructor() {}
    agebt: boolean = false;
    linuxTool: boolean = false;
    windowsTool: boolean = false;
    question: boolean = false;
    @Output() getFunction = new EventEmitter();

    ngOnInit() {
        this.agebt = true;
    }

    change(menu){
        if(menu === "agebt"){
            this.agebt = true;
            this.linuxTool = false;
            this.windowsTool = false;
            this.question = false;
        }else if(menu === "linuxTool"){
            this.agebt = false;
            this.linuxTool = true;
            this.windowsTool = false;
            this.question = false;
        }else if(menu === "windowsTool"){
            this.agebt = false;
            this.linuxTool = false;
            this.windowsTool = true;
            this.question = false;
        }else if(menu === "question"){
            this.agebt = false;
            this.linuxTool = false;
            this.windowsTool = false;
            this.question = true;
        }
    }

    windows(){
        this.change('windowsTool');
        this.getFunction.emit("windowsTool");
    }


}

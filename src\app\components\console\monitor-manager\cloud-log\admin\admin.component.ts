import { Component, OnInit } from '@angular/core';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

@Component({
    selector: 'app-admin',
    templateUrl: './admin.component.html',
    styleUrls: ['./admin.component.less']
})
export class AdminComponent implements OnInit {
    constructor() {}

    keyword: string = '';

    cols = [
        {
            title: '日志组名称',
            ColumnKey: "name",
            width: '360px'
        },
        {
            title: '云主机ID',
            ColumnKey: "status",
            width: '180px'
        },
        {
            title: '创建时间',
            ColumnKey: "ipAddress",
            width: '250px'
        },
        {
            title: '日志存储时间（天）',
            ColumnKey: "",
            width: '370px'
        },
        {
            title: '操作',
            ColumnKey: ""
        }
    ];
    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
    }

    ngOnInit() {}
    search(){
        alert("我是一个搜索按钮")
    }
    refresh() {
        alert("我是一个刷新按钮")
    }
}

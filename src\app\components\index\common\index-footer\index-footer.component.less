@import '../../../../style/common/_variable.less';

.index-footer {
    background-color: @footer-bg;
    color: #fff;
    color: rgba(255, 255, 255, 0.9);
    padding: 30px 0;
    font-size: 0;
    height: @footer-height;

    .footer-links, .footer-contact {
        display: inline-block;
        vertical-align: top;
    }

    .footer-links {
        width: 70%;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            right: 0;
            border-left: 1px solid #666;
        }
    }
    
    .footer-contact {
        font-size: 12px;
        text-align: right;
        width: 30%;

        .text-left {
            display: inline-block;
            text-align: left;
        }
    }

    .links-section {
        font-size: 12px;
        display: inline-block;
        vertical-align: top;
        width: 100% / 3;

        .links-title {
            font-size: 14px;
            margin-bottom: 16px;
        }

        ul {
            li {
                margin-bottom: 10px;
                a {
                    color: @gray;

                    &:hover {
                        color: @primary;
                    }
                }
            }
        }
    }

    .tel {
        .icon-tel {
            display: inline-block;
            vertical-align: top;
            width: 76px;
            height: 76px;
            margin-right: 10px;
            margin-bottom: 30px;
            background: url(../../../../../assets/images/phone.png) 0 0 no-repeat;
        }
        .tel-num {
            display: inline-block;
            vertical-align: top;

            p {
                font-size: 14px;
                margin-top: 5px;
                margin-bottom: 20px;
            }

            div {
                font-size: 16px;
                
                a {
                    color: #fff;
                }
            }
        }
    }
    .qq-group, .address {
        color: @gray;
        margin-bottom: 10px;
    }
}
import { Component, OnInit} from '@angular/core';
import { registerLocaleData,formatDate } from '@angular/common';
import localeZh from '@angular/common/locales/zh';
import { EChartsOption } from 'echarts';
import * as echarts from 'echarts';
import { DashboardService } from 'src/app/service/console/dashboard/dashboard.service';
import { ApplicationService } from 'src/app/service/console/app-system/application.service';
import {Router} from "@angular/router";
import {environment} from "../../../../environments/environment";
import {NzTableQueryParams} from "ng-zorro-antd/table";

registerLocaleData(localeZh);
interface CostData {
    month: string; // "202403"
    amount: number; // 12345.67
}
@Component({
    selector: 'app-dashboard',
    templateUrl: './dashboard.component.html',
    styleUrls: ['./dashboard.component.less']
})

export class DashboardComponent implements OnInit {
    formattedDate: string;
    costChartOption: EChartsOption;
    constructor(
        private dashboardService: DashboardService,
        private applicationService: ApplicationService,
        private router: Router,
    ) {}


    isAdmin = window.localStorage.getItem('isAdmin')
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    isLoading: boolean = false;

    busyStatus = {};

    title = window.localStorage.getItem('title');
    username = window.localStorage.getItem('username');

    // 选项卡控制
    selectedTabIndex: number = 0; // 默认选中第一个选项卡（产品目录视图）
    messageData = []; // 我的消息

    tableData = [];
    allTableData = []; // 存储所有数据
    displayTableData = []; // 当前页显示的数据
    filteredTableData = []; // 过滤后的数据

    // 分页配置
    pager = {
        page: 1,
        pageSize: 20,
        total: 0
    };

    // 搜索过滤条件
    searchFilters = {
        searchText: '',
        serviceCatalog: '',
        serviceType: ''
    };

    // 第二个下拉框的选项
    serviceTypeOptions: string[] = [];

    // 日期显示控制
    showFullDate: boolean = false;

    catalogInstanceCounts= {
        ecs: "",
        evs: "",
        vdi: "",
        vpc: "",
        securityGroup: "",
        elasticip: "",
        k8s: "",
        redis: "",
        rds: "",
        kafka: "",
        rabbitmq: ""
    };

    private initCostChart() {
        // 生成过去12个月的时间标签
        const months = [];
        const now = new Date();
        for (let i = 11; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            months.push(
                `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}`
            );
        }

        // 生成更真实的成本数据
        const generateCostData = () => {
            const data = [];
            let baseValue = 15000; // 初始基准值
            for (let i = 0; i < 12; i++) {
                // 模拟业务波动
                const seasonFactor = i % 12 === 11 ? 1.3 : 1; // 年末高峰
                const randomFactor = 0.9 + Math.random() * 0.2; // ±10%波动
                const trendFactor = 1 + (i * 0.03); // 每月3%增长趋势

                const value = baseValue * seasonFactor * randomFactor * trendFactor;
                data.push(Number(value.toFixed(2)));

                baseValue = value * 0.98; // 轻微衰减
            }
            // return data;
            return 0;
        };

        // 生成配额数据示例
        const quotaData = {
            cpu: { used: 85, total: 100 }, // 单位：核
            memory: { used: 256, total: 512 }, // 单位：GB
            storage: { used: 2048, total: 4096 } // 单位：GB
        };

        // 模拟数据（需替换为真实数据）
        // const mockData = Array.from({length: 12}, () => Math.random() * 10000);
        const mockData = generateCostData();
        this.costChartOption = {
            xAxis: {
                type: 'category',
                data: months,
                axisLabel: { rotate: 0 }
            },
            yAxis: {
                type: 'value',
                name: '每月费用趋势（¥）',
                axisLabel: {
                    formatter: (value: number) => `¥${value.toFixed(0)}`
                }
            },
            grid: {
                containLabel: true,
                left: '3%',
                right: '4%',
                bottom: '12%'
            },
            series: [{
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2,
                    shadowColor: 'rgba(21,94,239,0.2)',
                    shadowBlur: 10,
                    shadowOffsetY: 5
                },
                emphasis: { // 高亮样式
                    itemStyle: {
                        color: '#ff9900',
                        borderColor: '#fff',
                        borderWidth: 2
                    }
                },
                data: mockData,
                type: 'line',
                smooth: true,
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#155eef' },
                            { offset: 1, color: 'rgba(21,94,239,0.1)' }
                        ])
                    }
                } as any, // 添加类型断言
                itemStyle: {
                    normal: {
                        color: '#155eef'
                    }
                } as any // 添加类型断言
            }] as any[],

            tooltip: {
                trigger: 'axis',
                formatter: (params: any[]) => {
                    const date = months[params[0].dataIndex];
                    const value = params[0].value;
                    return `
          ${date}<br/>
          <span style="color:#155eef">●</span> 当月费用：¥${Number(value).toFixed(2)}
        `;
                }
            },

        } as EChartsOption;
    }

    ngOnInit() {
        this.initCostChart();
        this.formattedDate = formatDate(new Date(), 'yyyy年MM月dd日 EEEE', 'zh-CN');
        this.instanceEnumInit();
        // this.getDataList();
        this.getMessageList();
    }

    getMessageList() {
        let params = {pageSize: 3,pageNum: 1}
        this.applicationService.query(params)
            .then(rs => {
                if (rs.success) {
                    if(rs.data){
                        this.messageData =  rs.data.dataList || [];
                    }else{
                        this.messageData =  [];
                    }
                } else {
                    // this.msg.error(`获取配额列表失败${ rs.message ? ': ' + rs.message : '' }`);
                }

            })
            .catch(msg => {
                // this.msg.error('获取配额列表失败');
            });
    }

    // 更新当前页显示的数据
    updateDisplayData() {
        const startIndex = (this.pager.page - 1) * this.pager.pageSize;
        const endIndex = startIndex + this.pager.pageSize;
        this.displayTableData = this.filteredTableData.slice(startIndex, endIndex);
        this.tableData = this.displayTableData; // 保持原有的 tableData 变量名
    }

    // 分页变化
    pageChanged(pageNum: number) {
        this.pager.page = pageNum;
        this.updateDisplayData();
    }

    // 页面大小变化
    pageSizeChanged(pageSize: number) {
        this.pager.pageSize = pageSize;
        this.pager.page = 1; // 重置到第一页
        this.updateDisplayData();
    }

    // 应用所有过滤条件
    applyFilters() {
        let filtered = [...this.allTableData];

        // 文本搜索过滤
        if (this.searchFilters.searchText) {
            const searchText = this.searchFilters.searchText.toLowerCase();
            filtered = filtered.filter(item =>
                item.name && item.name.toLowerCase().includes(searchText)
            );
        }

        // 服务类目过滤
        if (this.searchFilters.serviceCatalog) {
            filtered = filtered.filter(item =>
                item.serviceCatalog === this.searchFilters.serviceCatalog
            );
        }

        // 服务类型过滤
        if (this.searchFilters.serviceType) {
            filtered = filtered.filter(item =>
                item.serviceType === this.searchFilters.serviceType
            );
        }

        this.filteredTableData = filtered;
        this.pager.total = this.filteredTableData.length;
        this.pager.page = 1; // 重置到第一页
        this.updateDisplayData();
    }

    // 搜索框输入事件
    onSearchTextChange() {
        this.applyFilters();
    }

    // 服务类目选择变化
    onServiceCatalogChange(value: string) {
        this.searchFilters.serviceCatalog = value;
        this.searchFilters.serviceType = ''; // 重置服务类型

        // 更新服务类型选项
        if (value && this.enumDatas.serviceCatalogList) {
            const selectedCatalog = this.enumDatas.serviceCatalogList.find(item => item.key === value);
            this.serviceTypeOptions = selectedCatalog ? selectedCatalog.types || [] : [];
        } else {
            this.serviceTypeOptions = [];
        }

        this.applyFilters();
    }

    // 服务类型选择变化
    onServiceTypeChange(value: string) {
        this.searchFilters.serviceType = value;
        this.applyFilters();
    }



    // 格式化创建日期
    formatCreateDate(date: string): string {
        if (!date) return '';
        const dateObj = new Date(date);
        if (this.showFullDate) {
            return formatDate(dateObj, 'yyyy-MM-dd HH:mm:ss', 'zh-CN');
        } else {
            return formatDate(dateObj, 'yyyy-MM-dd', 'zh-CN');
        }
    }

    // 格式化更新日期
    formatUpdateDate(date: string): string {
        if (!date) return '';
        const dateObj = new Date(date);
        if (this.showFullDate) {
            return formatDate(dateObj, 'yyyy-MM-dd HH:mm:ss', 'zh-CN');
        } else {
            return formatDate(dateObj, 'yyyy-MM-dd', 'zh-CN');
        }
    }

    forword(url: string) {
        this.router.navigate([url], {replaceUrl: true });
        return undefined;
    }

    // 选项卡切换事件
    onTabChange(index: number) {
        this.selectedTabIndex = index;
    }

    enumDatas: {
        serviceTypeMap: any;
        serviceCatalogMap: any;
        serviceTypeList: any[];
        serviceCatalogList: Array<{key: string, types: string[], value: string}>;
    } = {
        serviceTypeMap: {},
        serviceCatalogMap: {},
        serviceTypeList: [],
        serviceCatalogList: [],
    }
    private instanceEnumInit() {
        this.dashboardService.init()
            .then(rs => {
                if (rs.success) {
                    this.enumDatas = rs.data;
                    this.getDataList();
                } else {
                    // this.msg.error(`获取配额列表失败${ rs.message ? ': ' + rs.message : '' }`);
                }
            })
            .catch(msg => {
                // this.msg.error('获取配额列表失败');
            });
    }

    getDataList() {
        let params = {}
        this.isLoading = true;
        this.dashboardService.list(params)
            .then(rs => {
                if (rs.success) {

                    if(rs.data){
                        this.allTableData = rs.data || [];
                        this.enumDatas.serviceTypeList.forEach(enumItem => {
                            this.catalogInstanceCounts[enumItem.key] = this.allTableData.filter(data => data.serviceType === enumItem.key).length;
                        });
                        this.applyFilters();
                    }else{
                        this.allTableData = [];
                        this.filteredTableData = [];
                        this.displayTableData = [];
                        this.pager.total = 0;
                    }
                } else {
                    // this.msg.error(`获取配额列表失败${ rs.message ? ': ' + rs.message : '' }`);
                }

                this.isLoading = false;
            })
            .catch(msg => {
                // this.msg.error('获取配额列表失败');
                this.isLoading = false;
            });
    }

    // 名称排序函数
    nameSortFn = (a: any, b: any): number => {
        if (!a.name || !b.name) {
            return 0;
        }
        return a.name.localeCompare(b.name, 'zh-CN');
    }

    // 创建日期排序函数
    createDateSortFn = (a: any, b: any): number => {
        if (!a.instanceCreateTm || !b.instanceCreateTm) {
            return 0;
        }
        const dateA = new Date(a.instanceCreateTm);
        const dateB = new Date(b.instanceCreateTm);
        return dateA.getTime() - dateB.getTime();
    }

    // 更新日期排序函数
    updateDateSortFn = (a: any, b: any): number => {
        if (!a.instanceUpdateTm || !b.instanceUpdateTm) {
            return 0;
        }
        const dateA = new Date(a.instanceUpdateTm);
        const dateB = new Date(b.instanceUpdateTm);
        return dateA.getTime() - dateB.getTime();
    }
}

// 嵌入式BPMN编辑器样式
.bpmn-editor-embedded {
  //height: calc(100vh - 140px); // 减去顶部导航和其他元素的高度
  height: calc(100vh - 20px);// 减去顶部导航和其他元素的高度
  min-height: 600px; // 确保最小高度
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;

  // 顶部工具栏
  .bpmn-toolbar {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 8px;
        
        &:hover {
          color: #1890ff;
        }
      }

      .divider {
        margin: 0 12px;
        color: #d9d9d9;
        font-size: 14px;
      }

      .process-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-left: 8px;
      }
    }

    .toolbar-center {
      display: flex;
      align-items: center;

      .button-group {
        display: flex;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        overflow: hidden;

        button {
          border: none;
          border-radius: 0;
          margin: 0;

          &:not(:last-child) {
            border-right: 1px solid #d9d9d9;
          }
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  // 编辑器容器
  .bpmn-editor-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 500px; // 确保最小高度

    &.loading {
      pointer-events: none;
    }

    .bpmn-main-content {
      display: flex;
      width: 100%;
      height: 100%;

      .bpmn-canvas {
        flex: 1;
        height: 100%;
        position: relative;

        .group{
          margin: 0 !important;
        }
      // 隐藏BPMN.IO水印
      :global(.fjs-powered-by),
      :global(.bjs-powered-by),
      :global(.djs-powered-by),
      :global([class*="powered-by"]) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
      }

      // bpmn-js 样式覆盖
      :global(.djs-container) {
        background: #fff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }

      // 工具面板样式
      :global(.djs-palette) {
        left: 20px;
        top: 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid #e8e8e8;
        background: #fff;

        .djs-palette-entries {
          padding: 8px;
        }

        .djs-palette-entry {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          margin: 2px;

          &:hover {
            background: #f5f5f5;
          }

          &.djs-palette-entry-selected {
            background: #e6f7ff;
            border-color: #1890ff;
          }
        }

        // 强制覆盖 group 的 margin - 使用多种选择器确保覆盖
        .group {
          margin: 0 !important;
          margin-top: 0 !important;
          margin-bottom: 0 !important;
          margin-left: 0 !important;
          margin-right: 0 !important;
        }

        // 更具体的选择器
        .djs-palette-entries .group {
          margin: 0 !important;
        }

        // 针对所有可能的 group 类
        div.group {
          margin: 0 !important;
        }
      }

      // 全局样式覆盖（在组件外部）
      :global(.djs-palette .group) {
        margin: 0 !important;
      }

      :global(.djs-palette .djs-palette-entries .group) {
        margin: 0 !important;
      }

      :global(div.djs-palette div.group) {
        margin: 0 !important;
      }

      // 针对弹出菜单中的 group 元素
      :global(.djs-popup .group) {
        margin: 0 !important;
      }

      :global(.djs-context-pad .group) {
        margin: 0 !important;
      }

      :global(.djs-overlay .group) {
        margin: 0 !important;
      }

      // 针对工具栏弹出菜单
      :global(.djs-palette-popup .group) {
        margin: 0 !important;
      }

      // 针对属性面板弹出菜单
      :global(.bio-properties-panel .group) {
        margin: 0 !important;
      }

      // 针对所有BPMN相关弹出菜单
      :global(.djs-popup-overlay .group) {
        margin: 0 !important;
      }

      :global(.djs-popup-body .group) {
        margin: 0 !important;
      }

      // 更通用的 group 样式覆盖
      :global(.group) {
        margin: 0 !important;
      }

      // 针对所有可能的弹出菜单容器
      :global([class*="djs-"] .group) {
        margin: 0 !important;
      }

      // 针对所有可能的BPMN弹出菜单
      :global([class*="popup"] .group) {
        margin: 0 !important;
      }

      // 上下文菜单样式
      :global(.djs-context-pad) {
        .djs-context-pad-group {
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          border: 1px solid #e8e8e8;
          background: #fff;
        }

        .djs-context-pad-entry {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          
          &:hover {
            background: #f5f5f5;
          }
        }
      }

      // 选中元素样式
      :global(.djs-shape.selected) {
        .djs-outline {
          stroke: #1890ff;
          stroke-width: 2px;
          stroke-dasharray: none;
        }
      }

      :global(.djs-connection.selected) {
        .djs-outline {
          stroke: #1890ff;
          stroke-width: 2px;
        }
      }

      // 悬停样式
      :global(.djs-shape:hover) {
        .djs-outline {
          stroke: #40a9ff;
          stroke-width: 1px;
        }
      }

      // 拖拽样式
      :global(.djs-drag-group) {
        opacity: 0.8;
      }

      // 连接预览样式
      :global(.djs-connection.djs-connection-preview) {
        stroke: #1890ff;
        stroke-width: 2px;
        stroke-dasharray: 5, 5;
      }

      // 网格样式
      :global(.djs-grid) {
        display: none; // 默认隐藏网格
      }

      // 缩放控件样式
      :global(.djs-zoom-controls) {
        right: 20px;
        bottom: 80px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 1px solid #e8e8e8;
        background: #fff;

        .djs-zoom-control {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          
          &:hover {
            background: #f5f5f5;
          }
        }
      }
      }

      // 属性面板样式
      .properties-panel {
        width: 300px;
        height: 100%;
        border-left: 1px solid #e8e8e8;
        background: #fafafa;
        overflow: auto;
        flex-shrink: 0;

        // 属性面板内容样式
        :global(.bpp-properties-panel) {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          font-size: 12px;

          .bpp-properties-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e8e8e8;
            background: #fff;
            font-weight: 500;
            font-size: 14px;
          }

          .bpp-properties-group {
            margin-bottom: 16px;

            .bpp-properties-group-header {
              padding: 8px 16px;
              background: #f5f5f5;
              border-bottom: 1px solid #e8e8e8;
              font-weight: 500;
              font-size: 13px;
            }

            .bpp-properties-group-entries {
              padding: 12px 16px;
              background: #fff;

              .bpp-field-wrapper {
                margin-bottom: 12px;

                &:last-child {
                  margin-bottom: 0;
                }

                label {
                  display: block;
                  margin-bottom: 4px;
                  font-weight: 500;
                  color: #262626;
                }

                input, textarea, select {
                  width: 100%;
                  padding: 4px 8px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  font-size: 12px;
                  transition: border-color 0.3s;

                  &:focus {
                    border-color: #1890ff;
                    outline: none;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                  }
                }

                textarea {
                  resize: vertical;
                  min-height: 60px;
                }
              }
            }
          }
        }
      }
    }

    // 加载遮罩
    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }

  // 底部状态栏
  .bpmn-statusbar {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;
    font-size: 12px;
    color: #8c8c8c;

    .statusbar-left,
    .statusbar-right {
      display: flex;
      align-items: center;
    }

    .status-item {
      display: flex;
      align-items: center;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }

      i {
        margin-right: 4px;
      }

      a {
        color: #8c8c8c;
        text-decoration: none;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bpmn-editor-embedded {
    .bpmn-toolbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .toolbar-left,
      .toolbar-center,
      .toolbar-right {
        margin: 4px 0;
      }

      .toolbar-center {
        order: 3;
      }
    }

    .bpmn-statusbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .statusbar-left,
      .statusbar-right {
        width: 100%;
        justify-content: center;
        margin: 2px 0;
      }
    }
  }
}

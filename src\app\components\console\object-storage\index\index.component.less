 @import '../../../../style/common/_variable.less';

// zlh
.tc-content .sele {
  height: auto;
}
.col-sele-2 {
 width: 20%;
 clear: both;
}
.col-sele-11, .sele-label {
 display: inline-block;
 clear: both;
 float: none;
}
.sele-label {
 width: 20%;
 vertical-align: top;
}
.col-sele-11 {
 line-height: 30px;
 word-break: break-all;
}
.create {
  margin-left: 10px;
  float: right;
}
.statistic-view {
  width: 100%;
  margin-top: 40px;
  text-align: center;
}

.upload-tips {
  font-size: 12px;
  margin: 10px 0;
  color: @placeholder-color;
}
.folder-tips {
  margin-left: 10px;
  li {
    list-style: decimal;
  }
}
.folder-tips-ul {
  margin-left: 10px;
  li {
    list-style: disc;
  }
}
.folder-tips-error {
  li {
    color: @red;
  }
}
.upload-tips-error {
  color: @red;
}
.question-icon {
  cursor: pointer;
  vertical-align: top;
  margin-left: 5px;
}
.on-table-actions {
  .on-table-action-item {
      display: inline-block;
      border-radius: 50%;
      cursor: pointer;
      width: 22px;
      height: 22px;
      line-height: 22px;
      vertical-align: top;
      border-radius: 50%;
      color: #666;
      font-size: 16px;
      transition: none;

      &.disabled {
          cursor: not-allowed;
          color: #ccc!important;
      }

      &:hover:not(.disabled){
          background-color: @primary;
          color: #fff;
      }

      .icon {
          display: inline-block;
          width: 100%;
          height: 100%;
      }
  }

  .on-table-action-item + .on-table-action-item {
      margin-left: 15px;
  }

  .action-loading-placeholder {
      font-size: 12px;
      color: #999;
      user-select: none;
      text-align: center;
      line-height: 22px;
      text-indent: -1em;

      .icon {
          vertical-align: middle;
          position: relative;
          top: -1px;
          margin-right: 6px;
      }
  }
}
.tb-title {
  cursor: pointer;
  &:hover {
    color: @blue;
  }
}
.disabled-title:hover {
  cursor: not-allowed;
}
.choosable-delete-icon {
  cursor: pointer;
  margin-left: 10px;
}
.select-tips {
  display: inline-block;
  margin-bottom: 20px;
  width: 100px;
  color: #666;
}
.ip-input {
  width: 300px; margin: 5px 0
}
// 增加内容样式
.choosable-add-text {
  cursor: pointer;
  width: 150px;
  line-height: 30px;
  margin-left: 10px;
  i {
      display: inline-block;
      margin-right: 8px
  }
}
// 重构

.on-panel-body-left {
  background-color: #fff;
  display: inline-block;
  text-align: center;
  width: 18%;
  vertical-align: top;
  i {
    display: inline-block;
    margin: 0 5px;
  }
  button {
    display: block;
    width: 100%;
    margin: 20px auto;
  }
  .icon {
    float: right;
    margin-top: 12px;
  }
  ul {
    width: 100%;
    // margin-left: 10px;
    // margin-bottom: 20px;
    border-right: 1px solid @light-border;
    background-color: #fff;
    text-align: left;
    li {
      ul {
        border: none;
      }
    }
  }
}

.on-panel-body {
  display: inline-block;
  width: 100%;
}
.menu-title {
  width: 80%;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

td {
  span,p {
    word-break: break-all;
  }
}
.content-body-item {
  // .statistic-view {
  //   width: 60%;
  //   text-align: center;
  // }
  .header {
    height: 32px;
    margin: 10px 0;
  }
  .pull-right {
    button {
      margin: 0 10px;
    }
  }
}
.back2 {
  display: inline-block;
  margin-left: 30px;
  font-size: 14px;
  color: #666666;
  margin-bottom: 5px;
  cursor: pointer;
}
.back2:hover {
  color: rgb(0, 128, 255);
}
.folder-name {
  color: @primary;
  cursor: pointer;
}
.upload-tips {
  font-size: 12px;
  margin: 10px 0;
  color: @placeholder-color;
}
.upload-tips-error {
  color: @red;
}
.modify-title {
  margin: 0 0 10px 30px;
  font-size: 14px;
  color: @gray;
}
.warning-tips-container {
  margin-left: 30px;
  color: @red;
}
.underlint-text {
  font-size: 12px;
  color: @primary;
  text-decoration: underline;
}
.disabled {
  cursor: not-allowed;
  color: @light-border;
}
.download-link {
  color: @blue;
  text-decoration: underline;
}
.info-container {
  padding: 5px 0;
  border: 1px solid rgba(31,50,82,.1);
  margin: 15px 0;
  &>p {
    border-left: 4px solid #666;
    padding: 0 10px;
  }
  .info-details-container {
    margin-bottom: 15px;
    padding-left: 12px;
    .info-details-item {
      .danger {
        color: @red;
      }
      &>span:nth-child(1) {
        display: inline-block;
        margin-right: 50px;
      }
      i {
        display: inline-block;
        margin-left: 5px;
        cursor: pointer;
        &:hover {
          color: @blue;
        }
      }
    }
  }
}
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { FormService } from 'src/app/service/console/system/form.service';
import { NzTableQueryParams } from 'ng-zorro-antd/table';

// 表单状态枚举
export const FormStatusMap = {
    'draft': '草稿',
    'published': '已发布',
    'suspended': '已暂停',
    'archived': '已归档'
};

@Component({
    selector: 'app-form',
    templateUrl: './form.component.html',
    styleUrls: ['./form.component.less']
})
export class FormComponent implements OnInit {
    constructor(
        private router: Router,
        private msg: NzMessageService,
        private modal: NzModalService,
        private formService: FormService
    ) {}

    isLoading: boolean = false;
    keyword: string = '';
    tableData = [];
    busyStatus = {};

    // 配置弹窗相关
    configModalVisible: boolean = false;
    editData: any = null;
    
    // 分页配置
    pager = {
        page: 1,
        pageSize: 10,
        total: 0
    };

    // 查询过滤条件
    filters = {
        pageNum: 0,
        pageSize: 10,
        sortName: '',
        sortOrder: ''
    };

    ngOnInit(): void {
        this.getDataList();
    }

    // 获取数据列表
    getDataList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);

        // 初始化bean对象
        params.bean = params.bean || {};

        // 添加名称搜索条件
        if (keyword) {
            params.bean.name = keyword;
        }

        this.isLoading = true;
        this.formService.query(params)
        .then(rs => {
            if (rs.success) {
                this.tableData = rs.data.dataList || [];
                this.pager = {
                    page: rs.data.pageNum + 1,
                    pageSize: rs.data.pageSize,
                    total: rs.data.recordCount,
                };
            } else {
                this.msg.error(`获取表单列表失败${ rs.message ? ': ' + rs.message : '' }`);
            }

            this.isLoading = false;
        })
        .catch(err => {
            this.msg.error('获取表单列表失败');
            this.isLoading = false;
        });
    }

    // 搜索
    search() {
        this.filters.pageNum = 0;
        this.getDataList();
    }

    // 分页变化
    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getDataList();
    }

    // 表格查询参数变化
    onParamsChange(params: NzTableQueryParams): void {
        const { pageSize, pageIndex, sort } = params;
        const currentSort = sort.find(item => item.value !== null);

        this.filters.pageNum = pageIndex - 1;
        this.filters.pageSize = pageSize;

        if (currentSort) {
            this.filters.sortName = currentSort.key;
            this.filters.sortOrder = currentSort.value === 'ascend' ? 'asc' : 'desc';
        } else {
            this.filters.sortName = '';
            this.filters.sortOrder = '';
        }

        this.getDataList();
    }

    // 新增表单
    addForm() {
        // 跳转到表单设计器页面
        this.router.navigate(['/console/system/form-editor']);
    }

    // 编辑表单
    editForm(data: any) {
        // 跳转到表单设计器页面，传递表单ID
        this.router.navigate(['/console/system/form-editor', data.id]);
    }

    // 设计表单
    designForm(data: any) {
        // 跳转到表单设计器页面进行表单设计
        this.router.navigate(['/console/system/form-editor', data.id]);
    }

    // 查看表单
    viewForm(data: any) {
        const left = (window.screen.availWidth - 800) / 2 + window.screenX;
        const top = (window.screen.availHeight - 800) / 2 + window.screenY;
        // 在新窗口中打开预览页面，传入表单ID
        const previewUrl = `/form-preview/${data.id}`;
        window.open(previewUrl, '_blank', 'width=800,height=800,left='+left+',top='+top);
    }

    // 删除表单
    deleteForm(item) {
        this.modal.confirm({
            nzTitle: '确认删除',
            nzContent: `确定要删除表单"${item.name}"吗？`,
            nzOnOk: () => {
                this.busyStatus[item.id] = 'delete';
                this.formService.delete(item.id)
                .then(rs => {
                    if (rs.success) {
                        this.msg.success('删除成功');
                        this.getDataList();
                    } else {
                        this.msg.error(`删除失败${ rs.message ? ': ' + rs.message : '' }`);
                    }
                    this.busyStatus[item.id] = '';
                })
                .catch(err => {
                    this.msg.error('删除失败');
                    this.busyStatus[item.id] = '';
                });
            }
        });
    }

    // 发布表单
    publishForm(item) {
        this.busyStatus[item.id] = 'publish';
        this.formService.publish(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('发布成功');
                this.getDataList();
            } else {
                this.msg.error(`发布失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('发布失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 暂停表单
    suspendForm(item) {
        this.busyStatus[item.id] = 'suspend';
        this.formService.suspend(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('暂停成功');
                this.getDataList();
            } else {
                this.msg.error(`暂停失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('暂停失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 获取表单状态显示文本
    getFormStatus(status: string): string {
        return FormStatusMap[status] || status;
    }

    // 格式化日期
    formatDate(date: string): string {
        if (!date) return '-';
        return new Date(date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 跟踪函数
    trackById(index: number, item: any): any {
        return item.id;
    }

    // 配置保存回调
    onConfigSave() {
        this.getDataList();
    }
}

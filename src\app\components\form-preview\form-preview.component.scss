// 独立的表单预览页面样式 - 全屏布局
.form-preview-standalone {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// 顶部工具栏
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 32px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 100;

    .header-left {
        display: flex;
        align-items: center;

        .logo-section {
            display: flex;
            align-items: center;
            margin-right: 24px;

            .logo {
                height: 32px;
                width: auto;
                margin-right: 12px;
            }

            .brand-name {
                font-size: 18px;
                font-weight: 600;
                color: #1890ff;
            }
        }

        .divider {
            width: 1px;
            height: 24px;
            background-color: #e8e8e8;
            margin-right: 24px;
        }

        .preview-title {
            margin: 0;
            color: #262626;
            font-size: 20px;
            font-weight: 500;

            .form-id {
                color: #8c8c8c;
                font-size: 16px;
                font-weight: normal;
            }
        }
    }

    .header-right {
        display: flex;
        align-items: center;
    }
}

// 主体内容
.preview-body {
    flex: 1;
    position: relative;
    overflow: hidden;

    &.loading {
        pointer-events: none;
    }
}

.form-preview-content {
    height: 100%;
    overflow: auto;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
}

.form-wrapper {
    width: 750px;
    max-width: 800px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 120px); // 限制最大高度，为顶部工具栏和底部留出空间
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-header {
    padding: 32px 32px 24px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    flex-shrink: 0; // 防止头部被压缩

    .form-title {
        margin: 0 0 12px;
        color: #262626;
        font-size: 24px;
        font-weight: 600;
    }

    .form-description {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
        line-height: 1.6;
    }
}

.form-container {
    padding: 32px;
    min-height: 400px;
    flex: 1;
    overflow-y: auto; // 添加垂直滚动条
    overflow-x: hidden; // 隐藏水平滚动条

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
        transition: background 0.3s;

        &:hover {
            background: #a8a8a8;
        }
    }

    // 隐藏BPMN.IO水印
    :deep(.fjs-powered-by),
    :deep(.bjs-powered-by),
    :deep(.djs-powered-by),
    :deep([class*="powered-by"]) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    // form-js样式覆盖
    :deep(.fjs-container) {
        font-family: inherit;
    }

    :deep(.fjs-form) {
        .fjs-form-field {
            margin-bottom: 24px;

            .fjs-form-field-label {
                font-weight: 500;
                color: #262626;
                margin-bottom: 8px;
                font-size: 14px;
            }

            .fjs-input {
                border: 1px solid #d9d9d9;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                transition: all 0.3s;
                width: 100%;
                box-sizing: border-box;

                &:focus {
                    border-color: #1890ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    outline: none;
                }

                &:hover {
                    border-color: #40a9ff;
                }
            }

            .fjs-select {
                border: 1px solid #d9d9d9;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: #fff;
                cursor: pointer;
                width: 100%;
                box-sizing: border-box;
                transition: all 0.3s;

                &:focus {
                    border-color: #1890ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    outline: none;
                }

                &:hover {
                    border-color: #40a9ff;
                }
            }

            .fjs-checkbox,
            .fjs-radio {
                margin-right: 8px;
                transform: scale(1.1);
            }

            .fjs-button {
                background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
                border: none;
                color: #fff;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);

                &:hover {
                    background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
                    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
                    transform: translateY(-1px);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }
    }
}

.form-footer {
    padding: 24px 32px;
    border-top: 1px solid #f0f0f0;
    text-align: center;
    flex-shrink: 0; // 防止底部被压缩

    .powered-by {
        margin: 0;
        color: #bfbfbf;
        font-size: 12px;
    }
}

.loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

// 打印样式
@media print {
    .preview-header {
        display: none;
    }

    .preview-body {
        height: auto;
    }

    .form-preview-content {
        padding: 0;
    }

    .form-wrapper {
        box-shadow: none;
        border: none;
        max-width: none;
        border-radius: 0;
    }

    .form-footer {
        display: none;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .preview-header {
        padding: 12px 16px;
        flex-direction: column;
        gap: 12px;

        .header-left {
            .logo-section {
                margin-right: 16px;
            }

            .divider {
                display: none;
            }

            .preview-title {
                font-size: 18px;
            }
        }
    }

    .form-preview-content {
        padding: 20px 10px;
    }

    .form-wrapper {
        border-radius: 8px;
    }

    .form-header,
    .form-container {
        padding: 20px;
    }
}


import {Component, OnInit} from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-cloud-server',
    templateUrl: './cloud-server.component.html',
    styleUrls: ['./cloud-server.component.less']
})
export class CloudServerComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '云服务器',
        enName: 'Elastic Compute Service',
        desc: '云服务器是一种简单高效、安全可靠、处理能力可弹性伸缩的计算服务。',
        bgColor: '#1a284d',
        orderLink: '/console/cloud-server/instance-config',
        type: 'production',
    }

    ngOnInit() {}

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

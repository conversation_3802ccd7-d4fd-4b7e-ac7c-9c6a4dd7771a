// Form-js 专用翻译器
// 这是一个更强力的翻译实现，专门针对form-js的DOM结构

export class FormJSTranslator {
  private container: HTMLElement;
  private observer: MutationObserver | null = null;
  private translateInterval: number | null = null;
  private isTranslating = false;

  // 翻译映射表
  private translations: Record<string, string> = {
    // 左侧工具面板
    'Components': '组件',
    'Input': '输入',
    'Selection': '选择',
    'Presentation': '展示',
    'Containers': '容器',
    'Action': '操作',
    
    // 组件名称
    'Text field': '文本框',
    'Text area': '多行文本',
    'Number': '数字',
    'Date time': '日期时间',
    'Expression': '表达式',
    'File picker': '文件选择',
    'Checkbox': '复选框',
    'Checkbox group': '复选框组',
    'Radio group': '单选框组',
    'Select': '下拉选择',
    'Tag list': '标签列表',
    'Text view': '文本展示',
    'Image view': '图片展示',
    'Table': '表格',
    'HTML view': 'HTML展示',
    'Document preview': '文档预览',
    'Spacer': '间隔符',
    'Separator': '分隔线',
    'Group': '分组',
    'Dynamic list': '动态列表',
    'iFrame': '内嵌框架',
    'Button': '按钮',

    // 右侧属性面板
    'General': '常规',
    'Validation': '校验',
    'Condition': '条件',
    'Appearance': '外观',
    'Layout': '布局',
    'ID': 'ID',
    'Label': '标签',
    'Key': '键名',
    'Description': '描述',
    'Required': '必填',
    'Readonly': '只读',
    'Disabled': '禁用',
    'Default value': '默认值',
    'Placeholder': '占位符',
    'Read only': '只读',
    'Binds to a form variable': '绑定到表单变量',
    'Condition under which the field is hidden': '字段隐藏的条件',
    'Custom properties': '自定义属性',
    'Auto': '自动',

    // 通用操作
    'Create': '创建',
    'Remove': '移除',
    'Delete': '删除',
    'Add': '添加',
    'Save': '保存',
    'Cancel': '取消',
    'Apply': '应用',
    'OK': '确定',
    'Yes': '是',
    'No': '否',
    'True': '真',
    'False': '假',
    'None': '无',
    'Default': '默认'
  };

  constructor(container: HTMLElement) {
    this.container = container;
    this.init();
  }

  private init(): void {
    // 立即执行一次翻译
    setTimeout(() => this.translate(), 100);
    
    // 设置定时翻译
    this.translateInterval = window.setInterval(() => {
      this.translate();
    }, 500);

    // 10秒后停止定时翻译
    setTimeout(() => {
      if (this.translateInterval) {
        clearInterval(this.translateInterval);
        this.translateInterval = null;
      }
    }, 10000);

    // 设置DOM变化监听
    this.setupMutationObserver();
  }

  private setupMutationObserver(): void {
    this.observer = new MutationObserver((mutations) => {
      let shouldTranslate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldTranslate = true;
        }
      });

      if (shouldTranslate && !this.isTranslating) {
        setTimeout(() => this.translate(), 50);
      }
    });

    this.observer.observe(this.container, {
      childList: true,
      subtree: true
    });
  }

  private translate(): void {
    if (this.isTranslating) return;
    
    this.isTranslating = true;
    
    try {
      // 翻译所有文本节点
      this.translateTextNodes();
      
      // 翻译特定元素
      this.translateSpecificElements();
      
    } catch (error) {
      console.error('翻译过程中出错:', error);
    } finally {
      this.isTranslating = false;
    }
  }

  private translateTextNodes(): void {
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      (node) => {
        const parent = node.parentElement;
        if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
          return NodeFilter.FILTER_REJECT;
        }
        return NodeFilter.FILTER_ACCEPT;
      }
    );

    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    textNodes.forEach(textNode => {
      const text = textNode.textContent?.trim();
      if (text && this.translations[text]) {
        textNode.textContent = this.translations[text];
      }
    });
  }

  private translateSpecificElements(): void {
    // 翻译所有可能包含文本的元素
    const selectors = [
      'span', 'div', 'label', 'button', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      '.fjs-palette-group-header',
      '.fjs-palette-entry',
      '.bio-properties-panel-group-header',
      '.bio-properties-panel-entry'
    ];

    selectors.forEach(selector => {
      this.container.querySelectorAll(selector).forEach(element => {
        if (element.children.length === 0) { // 只处理叶子节点
          const text = element.textContent?.trim();
          if (text && this.translations[text]) {
            element.textContent = this.translations[text];
          }
        }
      });
    });
  }

  public forceTranslate(): void {
    this.translate();
  }

  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    if (this.translateInterval) {
      clearInterval(this.translateInterval);
      this.translateInterval = null;
    }
    
  }
}

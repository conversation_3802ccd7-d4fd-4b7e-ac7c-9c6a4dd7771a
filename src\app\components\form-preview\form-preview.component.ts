import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormService } from 'src/app/service/console/system/form.service';

// 导入form-js查看器
import { Form } from '@bpmn-io/form-js-viewer';

@Component({
    selector: 'app-form-preview',
    templateUrl: './form-preview.component.html',
    styleUrls: ['./form-preview.component.scss']
})
export class FormPreviewComponent implements OnInit, OnDestroy {
    @ViewChild('formContainer', { static: true }) formContainer!: ElementRef;

    private formViewer: Form;
    public formId: string | null = null;
    public formData: any = null;
    public formName: string = '表单预览';
    public loading = false;

    constructor(
        private route: ActivatedRoute,
        private msg: NzMessageService,
        private formService: FormService
    ) {}
    logo = window.localStorage.getItem("logo");
    pageTitle = window.localStorage.getItem('title').replace('欢迎来到','');

    ngOnInit(): void {
        // 获取路由参数
        this.formId = this.route.snapshot.paramMap.get('id');

        // 判断是否有传入id，如果有则调用接口获取数据，否则从sessionStorage获取
        if (this.formId && this.formId !== 'new') {
            this.loadFormDataFromApi();
        } else {
            // 从sessionStorage获取预览数据
            this.loadPreviewData();
        }
    }

    ngOnDestroy(): void {
        if (this.formViewer) {
            this.formViewer.destroy();
        }
    }

    // 从API加载表单数据
    private async loadFormDataFromApi(): Promise<void> {
        try {
            this.loading = true;
            this.formService.get(Number(this.formId)).then(rs => {
                if (rs.success) {
                    console.log("rs", rs);
                    this.formData = rs.data;
                    // 保存表单名称
                    this.formName = rs.data.name || '表单预览';
                    console.log('加载预览数据:', this.formData);
                    // 初始化表单查看器
                    this.initFormViewer();
                } else {
                    this.msg.error(`获取表单数据失败${ rs.message ? ': ' + rs.message : '' }`);
                }
            })
            .catch(err => {
                this.msg.error('获取表单数据失败');
            });


            // if (response.success && response.data) {
            //     // 解析jsonData.schema作为参数
            //     if (response.data.jsonData && response.data.jsonData.schema) {
            //         this.formData = {
            //             schema: response.data.jsonData.schema,
            //             data: {} // 可以提供默认数据
            //         };
            //         console.log('从API加载表单数据成功:', this.formData);
            //         // 初始化表单查看器
            //         await this.initFormViewer();
            //     } else {
            //         this.msg.error('表单数据格式不正确');
            //     }
            // } else {
            //     this.msg.error(response.message || '获取表单数据失败');
            // }
        } catch (error) {
            console.error('从API加载表单数据失败:', error);
            this.msg.error('获取表单数据失败');
        } finally {
            this.loading = false;
        }
    }

    private loadPreviewData(): void {
        try {
            const previewDataStr = sessionStorage.getItem('formPreviewData');
            if (previewDataStr) {
                const previewData = JSON.parse(previewDataStr);
                this.formData = previewData;
                console.log('加载预览数据:', this.formData);
                // 初始化表单查看器
                this.initFormViewer();
            } else {
                this.msg.error('未找到预览数据');
            }
        } catch (error) {
            console.error('加载预览数据失败:', error);
            this.msg.error('加载预览数据失败');
        }
    }

    private async initFormViewer(): Promise<void> {
        if (!this.formData || !this.formData.schema) {
            this.msg.error('表单数据无效');
            return;
        }

        try {
            this.loading = true;

            // 创建表单查看器实例
            this.formViewer = new Form({
                container: this.formContainer.nativeElement
            });

            // 导入表单schema和数据
            await this.formViewer.importSchema(this.formData.schema, this.formData.data || {});

            console.log('表单查看器初始化成功');

        } catch (error) {
            console.error('初始化表单查看器失败:', error);
            this.msg.error('初始化表单查看器失败');
        } finally {
            this.loading = false;
        }
    }

    // 关闭预览窗口
    closePreview(): void {
        window.close();
    }

    // 打印表单
    printForm(): void {
        window.print();
    }

    // 导出表单数据
    // exportData(): void {
    //     try {
    //         if (this.formViewer) {
    //             // 获取当前表单数据
    //             const data = this.formViewer.submit();
    //             const json = JSON.stringify(data, null, 2);
    //
    //             const blob = new Blob([json], { type: 'application/json' });
    //             const url = window.URL.createObjectURL(blob);
    //             const link = document.createElement('a');
    //             link.href = url;
    //             link.download = `form-data-${this.formId || 'preview'}.json`;
    //             link.click();
    //             window.URL.revokeObjectURL(url);
    //         }
    //     } catch (error) {
    //         console.error('导出表单数据失败:', error);
    //         this.msg.error('导出表单数据失败');
    //     }
    // }

}

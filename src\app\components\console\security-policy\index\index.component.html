<div class="table-content">
    <ol class="on-breadcrumb">
        <li><span>安全策略</span></li>
    </ol>

    <div class="on-panel">
        <div class="on-panel-header">
            <h3 class="title">
                安全策略
            </h3>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <form nz-form nzLayout="inline"
                    (ngSubmit)="search()">
                    <nz-input-group nzSearch
                        [nzAddOnAfter]="suffixIconButton">
                        <input type="text" name="keyword"
                            autocomplete="off"
                            [(ngModel)]="filters.condition" nz-input
                            placeholder="名称/状态/组件类型/配置" />
                    </nz-input-group>
                    <ng-template #suffixIconButton>
                        <button nz-button nzType="primary"
                            nzSearch><i nz-icon
                                nzType="search"></i></button>
                    </ng-template>
                </form>
                <div class="pull-right" *ngIf="isAdmin === 'true'">
                    <a nz-button nzType="primary"
                        [ngClass]="{'disabled': resLimit}"
                        [routerLink]="resLimit ? null : '../security-policy-config'">
                        <i nz-icon nzType="plus"
                            nzTheme="outline"></i>
                        创建安全策略
                    </a>
                </div>
            </div>
            <nz-table
                #security
                [nzItemRender]="renderItemTemplate"
                [nzLoading]="isLoading"
                [nzLoadingDelay]="300"
                [nzFrontPagination]="false"
                [nzTotal]="pager.total"
                [nzPageIndex]="pager.page"
                [nzPageSize]="pager.pageSize"
                (nzPageIndexChange)="pageChanged($event)"
                [nzData]="">
                <thead>
                    <tr>
                        <!-- <th>名称</th>
                        <th>源</th>
                        <th>目标</th>
                        <th>服务</th>
                        <th>优先权</th>
                        <th>已启用</th>
                        <th>操作</th> -->
                        <ng-container *ngFor="let col of cols">
                            <th
                              *ngIf="col.width"
                              nz-resizable
                              nzBounds="window"
                              nzPreview
                              nzBreakWord="true"
                              [nzWidth]="col.width"
                              [nzMaxWidth]="600"
                              [nzMinWidth]="100"
                              [nzShowSort]="col.showSort"
                              [nzSortFn]="col.sortFlag"
                              [nzSortOrder]="col.allowSort"
                              [nzColumnKey]="col.ColumnKey"
                              (nzResizeEnd)="onResize($event, col.title)"
                            >
                              {{ col.title }}
                              <nz-resize-handle nzDirection="right">
                                <div class="resize-trigger"></div>
                              </nz-resize-handle>
                            </th>
                            <th *ngIf="!col.width" style="min-width: 120px;">
                              {{ col.title }}
                            </th>
                          </ng-container>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of ">
                        <td>{{ item }}</td>
                        <td>{{ item ? item : '无'}}</td>
                        <td>{{ item }}</td>
                        <td></td>
                        <td>
                            <div class="on-table-actions"
                                [hidden]="busyStatus[item.id]">
                                <div class="on-table-action-item"
                                    (click)="showEditModal(item)">
                                    <i nzTitle="编辑"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-edit"></i>
                                </div>
                                <div class="on-table-action-item"
                                    nz-popconfirm
                                    nzPlacement="top"
                                    nzTitle="确定要删除该监控规则吗？"
                                    (nzOnConfirm)="deleteMonitor(item);">
                                    <i nzTitle="删除"
                                        nzPlacement="bottom"
                                        nz-tooltip
                                        class="icon fa fa-trash-o">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions"
                                [hidden]="!busyStatus[item.id]">
                                <div
                                    class="action-loading-placeholder">
                                    <i class="icon" nz-icon
                                        [nzType]="'loading'"></i>
                                    {{ getBusyText(item) }}
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr [hidden]="security.data.length || !isLoading"
                        class="loading-placeholder">
                        <td colspan="100%"></td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type
                let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>
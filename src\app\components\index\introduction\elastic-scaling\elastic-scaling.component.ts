import { Component, OnInit } from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-elastic-scaling',
    templateUrl: './elastic-scaling.component.html',
    styleUrls: ['./elastic-scaling.component.less']
})
export class ElasticScalingComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '弹性伸缩',
        enName: 'Auto Scaling',
        desc: '弹性伸缩（Auto Scaling）是根据用户的业务需求，通过策略自动调整其业务资源的服务。您可以根据业务需求自行定义伸缩策略，从而降低人为反复调整资源以应对业务变化和负载高峰的工作量，帮您节约资源和人力运维成本。弹性伸缩目前仅支持对一个负载均衡下的云服务器进行自动调整，伸缩策略支持的监控指标有CPU和内存在负载均衡下的平均使用率，用户可以设置相应指标的阈值，实现负载均衡下云服务器的自动调整。',
        bgColor: '#1a284d',
        orderLink: '/console/elastic-scaling/scaling-groups',
        type: 'elasticScalingProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

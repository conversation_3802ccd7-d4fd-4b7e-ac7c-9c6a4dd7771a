<!--banner图-->
<div class="intro">
    <app-intro-banner [intro]="intro"></app-intro-banner>
    <!--锚点导航-->
    <div class="nav-choose">
        <div class="grid-1200">
            <a href="javascript:void(0)" id="box1"
                (click)="clickbox1()">产品优势</a>
            <a href="javascript:void(0)" id="box2"
                (click)="clickbox2()">应用场景</a>
        </div>
    </div>
    <!--产品优势-->
    <div class="advantage-box" id="advantage">
        <div class="grid-1200">
            <h2 class="advantage-title">产品优势</h2>
            <div nz-row nzGutter="30">
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy pic-one"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">安全可靠</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>租户之间网络隔离，实例的网络访问在租户间天然隔离</p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy pic-two"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">高性价比</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>无需采购、部署和运维费用，基于自有机房进行大规模部署，成本低廉
                            </p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy pic-three"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">高可用</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>使用集群形态部署，稳定可靠</p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy pic-four"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">开箱即用</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>只需几分钟时间，即可获得一个高可用Kafka集群</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
<!--应用场景-->
<div class="scene-box-network" id="scene">
    <div class="grid-1200">
        <h2 class="scene-title">应用场景</h2>
        <div nz-row nzGutter="30">
            <div nz-col nzSpan="12" class="scene-col scene-col-special">
                <div class="scene-list">
                    <a href="javascript:void(0)" class="scene-icon"><img src="./assets/images/rz-default.png"></a>
                    <div class="list-caption">
                        <h3><a href="javascript:void(0)">日志分析系统</a></h3>
                        <div class="white-line"></div>
                        <div class="scene-txt">
                            <p>消息队列在日志分析系统中扮演重要角色。日志产生后异步发送至消息队列，再有消息队列分发至不同的日志分析系统，其高吞吐特新使其保证日志采集分发的稳定、可靠</p>
                        </div>
                    </div>
                </div>
            </div>
            <div nz-col nzSpan="12" class="scene-col">
                <div class="scene-list">
                    <a href="javascript:void(0)" class="scene-icon"><img src="./assets/images/scene-disk-two.png"></a>
                    <div class="list-caption">
                        <h3><a href="javascript:void(0)">大数据分析</a></h3>
                        <div class="white-line"></div>
                        <div class="scene-txt">
                            <p>通过agent采集到消息队列中的服务，依托其低延迟特性可将数据推送至实时数据分析平台，完成实时图表展示、异常检测等；依托其高吞吐特性将数据推送至离线数据仓库，作为离线数据分析的数据入口</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
  <div class="clear"></div>
  <!--免费试用-->
  <div class="try-box" id="try">
      <div class="grid-1200">
          <h2 class="advantage-title">免费试用</h2>
          <div class="maintitle-tip">
              <p>所有用户可免费试用AIC应用托管服务的全栈服务（试用时有资源限制）</p>
          </div>
      </div>
  </div>
  

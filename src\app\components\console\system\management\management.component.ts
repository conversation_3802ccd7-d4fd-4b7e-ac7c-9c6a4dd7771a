import { Component, OnInit, Input, Output, EventEmitter, <PERSON><PERSON><PERSON>roy,ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl, AbstractControl } from '@angular/forms';
import { ManagementService } from 'src/app/service/console/system/management.service';
import { environment } from 'src/environments/environment';
import {formatDate} from "@angular/common";
import {NzResizeEvent} from "ng-zorro-antd/resizable";
import { NzMessageService } from 'ng-zorro-antd/message';
import {ConfigService} from "src/app/service/config/config.service";
import { NzUploadFile } from 'ng-zorro-antd/upload';

interface InitDataType {
    [name: string]: any;
};

interface FormOptions {
    allowedKeyType: string;
    userScript: boolean;
    buyAmount: boolean;
    publicNetworkIp: boolean;
    hidePublicNetworkBindTip: boolean;
}

@Component({
    selector: 'app-system-management',
    templateUrl: './management.component.html',
    styleUrls: ['./management.component.less']
})
export class ManagementComponent implements OnInit {
    dateFormat = 'yyyy/MM/dd';

    constructor(
        private managementService: ManagementService,
        private fb: FormBuilder,
        private msg: NzMessageService,
        private configService: ConfigService,
    ) {}

    managementGroup: FormGroup;
    systemConfigGroup: FormGroup;

    isAdmin = window.localStorage.getItem('isAdmin')
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    isLoading: boolean = false;

    busyStatus = {};

    typeList: any;

    // logoList = [
    //     // { key: 'default', value: 'AI云管家', active: ''},
    //     {key: 'hengshi', value: '恒时多云底座', active: ''},
    //     {key: 'hexinchuangtian', value: '和信创天', active: ''},
    //     {key: 'qinming', value: '秦明', active: '' },
    //     {key: 'thinkstation', value: 'ThinkStation', active: '' },
    //     {key: 'amste', value: 'AMSTE', active: '' },
    //     {key: 'centific', value: 'CENTIFIC', active: ''}
    // ];

    logoList: any;

    // 文件上传相关属性
    fullLogoFileList: NzUploadFile[] = [];
    miniLogoFileList: NzUploadFile[] = [];
    isUploadingFullLogo: boolean = false;
    isUploadingMiniLogo: boolean = false;
    fullLogoError: string = '';
    miniLogoError: string = '';

    // 预览图相关属性
    fullLogoPreviewUrl: string = '';
    miniLogoPreviewUrl: string = '';
    showFullLogoPreview: boolean = false;
    showMiniLogoPreview: boolean = false;

    @Input() formSubmitAttempt: boolean;
    ai_url: any;

    ngOnInit() {
        this.managementGroup = this.fb.group({
            type: ['', {}],
            as: ['', {
                validators: [
                    Validators.required,
                ]
            }],
            sk: ['', {
                validators: [
                    Validators.required,
                ]
            }],
        });
        this.systemConfigGroup = this.fb.group({
            title: ['', {}],
            logo: ['', {}],
            fullLogo: ['', {}],
            miniLogo: ['', {}],
        });
        this.typeList = [{ key: 'CMCC', value: '和云' }];
        this.getConnection();
        this.managementGroup.patchValue({type : 'CMCC'});
        this.configService.initTitle().then(res => {
            if(res.success){
                // this.systemConfigGroup.value.title = res.data;
                this.systemConfigGroup.patchValue({title : res.data})
                // window.localStorage.setItem('title', res.data);
            }
        });
        this.configService.logoList().then(res => {
            if(res.success){
                this.logoList = res.data.map(item => {
                    return {key: item, active: ''};
                })
                console.log(this.logoList)
                this.configService.initLogo().then(res => {
                    if(res.success){
                        // this.systemConfigGroup.value.logo = res.data;
                        this.systemConfigGroup.patchValue({logo : res.data})
                        this.selectLogo({key: res.data});
                        // window.localStorage.setItem('logo', res.data);
                    }
                })
            }
        })


        this.ai_url = window.localStorage.getItem('ai_url');

        // 加载现有的LOGO预览
        this.loadLogoPreview();
    }

    // desensitizeUrl(url){
    //     const ipRegex = /(\d{1,3}\.\d{1,3}\.)\d{1,3}\.\d{1,3}/;
    //     const lastPartRegex = /\/([^/]+)$/;
    //     const desensitizedIp = url.replace(ipRegex, (match, p1) => {
    //         return p1 + '**.**';
    //     });
    //     const desensitizedUrl = desensitizedIp.replace(lastPartRegex, (match, p1) => {
    //         return '/***';
    //     });
    //     return desensitizedUrl;
    // }

    getConnection(){
        this.managementService.getConnectionData()
            .then(rs => {
                if (rs.success) {
                    if(rs && rs.data && rs.data.username){
                        this.managementGroup.patchValue({as : rs.data.username});
                    }
                    if(rs && rs.data && rs.data.password){
                        this.managementGroup.patchValue({as : rs.data.username, sk: rs.data.password});
                    }
                } else {
                    console.log(`获取连接信息失败${rs.message ? ': ' + rs.message : ''}`)
                }
            })
            .catch(err => {
                console.log(`获取连接信息失败${err}`)
            });
    }

    testConnection(){
        this.formSubmitAttempt = true;

        // 检查表单是否有效
        if (this.managementGroup.valid) {
            // 表单有效，执行提交逻辑
            // 这里可以调用服务进行数据提交

            const params = {username: this.managementGroup.value.as, password: this.managementGroup.value.sk}
            this.managementService.checkConnectionData(params)
                .then(rs => {
                    if (rs.success) {
                        this.msg.success(`连接测试成功`);
                    } else {
                        this.msg.error(`连接测试失败`);
                        console.log(`连接测试失败${rs.message ? ': ' + rs.message : ''}`)
                        // this.isCreating = false;
                    }
                })
                .catch(err => {
                    this.msg.error(`连接测试失败`);
                    // this.isCreating = false;
                });
        }
    }

    submitData() {
        // 设置表单提交尝试标志
        this.formSubmitAttempt = true;

        // 检查表单是否有效
        if (this.managementGroup.valid) {
            // 表单有效，执行提交逻辑
            const params = {username: this.managementGroup.value.as, password: this.managementGroup.value.sk}
            this.managementService.saveConnection(params)
                .then(rs => {
                    if (rs.success) {
                        this.msg.success(`保存成功`);
                    } else {
                        this.msg.error(`保存连接失败`);
                        console.log(`保存连接失败${rs.message ? ': ' + rs.message : ''}`)
                        // this.isCreating = false;
                    }
                })
                .catch(err => {
                    this.msg.error(`保存连接失败`);
                    // this.isCreating = false;
                });
        }
    }

    isInvalid(fc: AbstractControl): boolean {
        return (!fc.valid && fc.touched) ||
            (fc.untouched && this.formSubmitAttempt);
    }

    submitSystemConfigData() {
        const params = {title: this.systemConfigGroup.value.title, logo: this.systemConfigGroup.value.logo}
        this.managementService.setbrand(params)
            .then(rs => {
                if (rs.success) {
                    this.msg.success(`操作成功`);
                    window.localStorage.setItem('logo', params.logo);
                    window.localStorage.setItem('title', params.title);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    this.msg.error(`操作失败`);
                    console.log(`操作失败${rs.message ? ': ' + rs.message : ''}`)
                    // this.isCreating = false;
                }
            })
            .catch(err => {
                this.msg.error(`操作失败`);
                // this.isCreating = false;
            });
    }

    selectLogo(item) {
        this.systemConfigGroup.patchValue({logo : item.key});
        this.logoList.forEach((obj) => obj.active = (item.key === obj.key ? 'active' : ''));
    }

    // 全尺寸LOGO上传前检查
    beforeUploadFullLogo = (file: NzUploadFile): boolean => {
        const isImage = file.type!.indexOf('image/') === 0;
        if (!isImage) {
            this.fullLogoError = '只能上传图片文件！';
            this.fullLogoFileList = [];
            return false;
        }
        const isLt2M = file.size! / 1024 / 1024 < 2;
        if (!isLt2M) {
            this.fullLogoError = '图片大小不能超过2MB！';
            this.fullLogoFileList = [];
            return false;
        }
        this.fullLogoError = '';
        // 手动添加文件到列表
        this.fullLogoFileList = [file];
        return false; // 阻止自动上传，使用自定义上传
    };

    // 缩略版LOGO上传前检查
    beforeUploadMiniLogo = (file: NzUploadFile): boolean => {
        const isImage = file.type!.indexOf('image/') === 0;
        if (!isImage) {
            this.miniLogoError = '只能上传图片文件！';
            this.miniLogoFileList = [];
            return false;
        }
        const isLt2M = file.size! / 1024 / 1024 < 2;
        if (!isLt2M) {
            this.miniLogoError = '图片大小不能超过2MB！';
            this.miniLogoFileList = [];
            return false;
        }
        this.miniLogoError = '';
        // 手动添加文件到列表
        this.miniLogoFileList = [file];
        return false; // 阻止自动上传，使用自定义上传
    };

    // 全尺寸LOGO上传
    uploadFullLogo(): void {
        if (this.fullLogoFileList.length === 0) {
            this.msg.warning('请选择要上传的全尺寸LOGO文件！');
            return;
        }

        this.isUploadingFullLogo = true;
        const formData = new FormData();
        formData.append('file', this.fullLogoFileList[0] as any);

        this.managementService.uploadLogo(formData, 'full')
            .then(rs => {
                this.isUploadingFullLogo = false;
                if (rs.success) {
                    this.msg.success('全尺寸LOGO上传成功！');
                    this.systemConfigGroup.patchValue({ fullLogo: rs.data });
                    // 更新预览图
                    this.updateLogoPreview('full');
                } else {
                    this.msg.error(`全尺寸LOGO上传失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.isUploadingFullLogo = false;
                this.msg.error('全尺寸LOGO上传失败！');
            });
    }

    // 缩略版LOGO上传
    uploadMiniLogo(): void {
        if (this.miniLogoFileList.length === 0) {
            this.msg.warning('请选择要上传的缩略版LOGO文件！');
            return;
        }

        this.isUploadingMiniLogo = true;
        const formData = new FormData();
        formData.append('file', this.miniLogoFileList[0] as any);

        this.managementService.uploadLogo(formData, 'mini')
            .then(rs => {
                this.isUploadingMiniLogo = false;
                if (rs.success) {
                    this.msg.success('缩略版LOGO上传成功！');
                    this.systemConfigGroup.patchValue({ miniLogo: rs.data });
                    // 更新预览图
                    this.updateLogoPreview('mini');
                } else {
                    this.msg.error(`缩略版LOGO上传失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.isUploadingMiniLogo = false;
                this.msg.error('缩略版LOGO上传失败！');
            });
    }

    // 移除全尺寸LOGO文件
    removeFullLogo = (): boolean => {
        this.fullLogoFileList = [];
        this.fullLogoError = '';
        return true;
    };

    // 移除缩略版LOGO文件
    removeMiniLogo = (): boolean => {
        this.miniLogoFileList = [];
        this.miniLogoError = '';
        return true;
    };

    // 加载LOGO预览图
    loadLogoPreview(): void {
        // 构建预览图URL，添加时间戳防止缓存
        const timestamp = new Date().getTime();
        this.fullLogoPreviewUrl = `/cloud/logo/full-logo.png?t=${timestamp}`;
        this.miniLogoPreviewUrl = `/cloud/logo/mini-logo.png?t=${timestamp}`;

        // 检查图片是否存在
        this.checkImageExists(this.fullLogoPreviewUrl).then(exists => {
            this.showFullLogoPreview = exists;
        });

        this.checkImageExists(this.miniLogoPreviewUrl).then(exists => {
            this.showMiniLogoPreview = exists;
        });
    }

    // 检查图片是否存在
    private checkImageExists(url: string): Promise<boolean> {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }

    // 更新预览图
    private updateLogoPreview(type: 'full' | 'mini'): void {
        const timestamp = new Date().getTime();
        if (type === 'full') {
            this.fullLogoPreviewUrl = `/cloud/logo/full-logo.png?t=${timestamp}`;
            this.showFullLogoPreview = true;
        } else {
            this.miniLogoPreviewUrl = `/cloud/logo/mini-logo.png?t=${timestamp}`;
            this.showMiniLogoPreview = true;
        }
    }

    saveAiUrl() {
        const params = {value: this.ai_url}
        // console.log(this.ai_url)
        // return;
        this.managementService.saveAiUrl(params)
            .then(rs => {
                if (rs.success) {
                    this.msg.success(`操作成功`);
                    window.localStorage.setItem('ai_url', this.ai_url);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    this.msg.error(`操作失败`);
                    console.log(`操作失败${rs.message ? ': ' + rs.message : ''}`)
                    // this.isCreating = false;
                }
            })
            .catch(err => {
                this.msg.error(`操作失败`);
                // this.isCreating = false;
            });
    }
}


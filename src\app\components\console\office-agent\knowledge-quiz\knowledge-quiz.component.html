<div class="office-content">
  <!-- 页面头部 -->
    <div class="header" *ngIf="!hasConversation">
    <div class="logo-container">
      <h1 class="title">知识问答</h1>
    </div>
    </div>

    <p class="subtitle" *ngIf="!hasConversation">随时提出您的问题，我将竭诚为您解答</p>

    <div class="main-container" [ngClass]="{'has-conversation': hasConversation}">

    <!-- 对话区域 -->
    <div class="chat-container">
      <div class="chat-messages" #chatMessages>
      <div
        *ngFor="let message of messages; trackBy: trackByMessageId"
        class="message-wrapper"
        [ngClass]="{'user-message': message.isUser, 'ai-message': !message.isUser}">

        <!-- AI消息 -->
        <div *ngIf="!message.isUser" class="message-item ai-item">
          <div class="message-avatar">
            <nz-avatar nzIcon="robot" [nzSize]="32"></nz-avatar>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">AI助手</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-text"
                 [ngClass]="{'markdown-content': message.isMarkdown}"
                 [innerHTML]="message.isMarkdown ? message.renderedContent : message.content"></div>
          </div>
        </div>

        <!-- 用户消息 -->
        <div *ngIf="message.isUser" class="message-item user-item">
          <div class="message-content">
            <div class="message-header">
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              <span class="sender-name">我</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
          <div class="message-avatar">
            <nz-avatar nzIcon="user" [nzSize]="32"></nz-avatar>
          </div>
        </div>
      </div>

      <!-- 正在输入指示器 -->
      <div *ngIf="isSending" class="message-wrapper ai-message">
        <div class="message-item ai-item">
          <div class="message-avatar">
            <nz-avatar nzIcon="robot" [nzSize]="32"></nz-avatar>
          </div>
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">AI助手</span>
              <span class="message-time">正在思考...</span>
            </div>
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <div class="input-container">
        <div class="input-box">
            <textarea
                class="input-textarea"
                placeholder="输入您的问题或 @召唤技能；Enter发送，Shift+Enter换行"
                [(ngModel)]="currentMessage"
                (keydown)="onKeyDown($event)"
                [disabled]="isSending"></textarea>
            <div class="input-footer">
                <button
                        class="input-button"
                        nz-dropdown
                        [nzDropdownMenu]="modelMenu"
                        nzPlacement="topLeft">
                    <span class="feature-icon"><i nz-icon nzType="robot" nzTheme="outline"></i></span>
                    <span>{{ getSelectedModelName() }}</span>
                    <i nz-icon nzType="down" nzTheme="outline"></i>
                </button>
                <nz-dropdown-menu #modelMenu="nzDropdownMenu">
                    <ul nz-menu>
                        <!-- 加载状态 -->
                        <li nz-menu-item *ngIf="isLoadingModels" class="loading-item">
                            <i nz-icon nzType="loading" nzSpin></i>
                            <span>加载模型中...</span>
                        </li>

                        <!-- 分组显示模型 -->
                        <ng-container *ngFor="let group of modelGroups">
                            <li nz-menu-group [nzTitle]="group.groupName">
                                <ul>
                                    <li
                                        nz-menu-item
                                        *ngFor="let model of group.models"
                                        (click)="selectModel(model.id)"
                                        [class.selected]="selectedModel === model.id">
                                        <div class="model-item">
                                            <div class="model-name">{{ model.name }}</div>
<!--                                            <div class="model-description" *ngIf="model.description">{{ model.description }}</div>-->
                                        </div>
                                    </li>
                                </ul>
                            </li>
                        </ng-container>

                        <!-- 无数据提示 -->
                        <li nz-menu-item *ngIf="!isLoadingModels && modelGroups.length === 0" class="no-data-item">
                            <span>暂无可用模型</span>
                        </li>
                    </ul>
                </nz-dropdown-menu>
                <button
                        class="send-button"
                        (click)="sendMessage()"
                        [disabled]="!currentMessage.trim() || !selectedModel || isSending">
                    <i nz-icon nzType="enter" nzTheme="outline" *ngIf="!isSending"></i>
                    <i nz-icon nzType="loading" nzSpin *ngIf="isSending"></i>
                </button>
            </div>
        </div>
    </div>

  <!-- 页面底部说明 -->
  <div class="footer">
    内容由AI生成，仅供参考，不代表平台立场
  </div>
</div>

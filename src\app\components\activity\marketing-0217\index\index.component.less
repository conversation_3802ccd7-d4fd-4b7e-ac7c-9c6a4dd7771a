@import "../../../../style/common/_variable.less";

@small-color: #AEB9DF;

nz-layout {
    background-color: #01114f;
    color: #fff;

    a {
        color: #fff;
    }
}

nz-header {
    background-color: #071b67;
    height: 42px;
    line-height: 42px;
    padding-left: 15px;
    padding-right: 15px;
}

header {
    color: #fff;
    a {
        display: inline-block;
        color: #fff;
    }

    .brand {
        img {
            height: 24px;
        }
    }

    .user-avatar {
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }


    h1 {
        display: inline-block;
        color: #fff;
        text-align: center;
        font-size: 18px;
        line-height: 42px;
    }
}

#main-content {
    background-image: url(../assets/main-banner.jpg);
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 100% auto;
    padding: 0 15px;
    position: relative;

    &:after {
        content: '';
        position: absolute;
        background-color: rgba(1, 17, 79, 0.6);
        left: 0;
        right: 0;
        top: 0;
        height: 200px;
    }

    .title {
        width: 80%;
        height: 120px;
        margin: auto;
        margin-bottom: 20px;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
        background-image: url(../assets/title.png);
        position: relative;
        right: -10px;
        z-index: 2;
    }

    .intro {
        background-color: #071b67;
        padding: 10px;
        letter-spacing: 1px;
        font-size: 13px;
        position: relative;
        z-index: 2;
    }

    .section {
        .menu-box {
            margin: 20px auto;
            text-align: center;
            .icon {
                display: inline-block;
                width: 26px;
                height: 26px;
                margin-right: 10px;
                margin-left: 10px;
                position: relative;
                top: -1px;
            }

            .product {
                margin: 10px 0;
            }

            .product-name {
                font-size: 14px;
                margin-top: 3px;
            }
        }
        margin-top: 40px;
        .section-title {
            
            height: 18px;
            margin-bottom: 20px;
        }
        .title-1 {
            background-image: url(../assets/title-1.jpg);
            background-repeat: no-repeat;
            background-position: center 0;
            background-size: contain;
        }
        .title-2 {
            background-image: url(../assets/title-2.jpg);
            background-repeat: no-repeat;
            background-position: center 0;
            background-size: contain;
        }
        .title-3 {
            background-image: url(../assets/title-3.jpg);
            background-repeat: no-repeat;
            background-position: center 0;
            background-size: contain;
        }
    }

    .product {
        .product-list {
            .product-item {
                background-color: rgba(15, 27, 111, 0.67);
                padding: 5px;

                .product-item-wrapper {
                    background-color: #122983;
                    padding: 12px 8px;
                    position: relative;
                    overflow: hidden;
                    background-image: url(../assets/product-bg.png);
                    background-position: right 0;
                    background-repeat: no-repeat;
                    background-size: auto 80%;

                    .featured-tag {
                        font-size: 12px;
                        position: absolute;
                        width: 150px;
                        text-align: center;
                        right: -54px;
                        top: 12px;
                        background-color: #3f61e4;
                        color: #fff;
                        transform: rotate(45deg);
                    }
                }

                .product-name {
                    font-size: 13px;
                }

                .product-info {
                    font-size: 13px;
                    color: #fffffe;
                    margin-top: 5px;
                    white-space: nowrap;

                    span {
                        &.name {
                            margin-right: 5px;
                        }

                        &.num {
                            color: #d9c27f;
                            margin-right: 10px;

                        }

                        &.free {
                            display: inline-block;
                            padding: 3px 12px;
                            font-size: 13px;
                            background-color: #2540ac;
                            .free-tag {
                                display: inline-block;
                                height: 12px;
                                background-image: url(../assets/free-tag.png);
                                width: 36px;
                                background-position: 0 0;
                                background-size: contain;
                                background-repeat: no-repeat;
                                vertical-align: middle;
                                line-height: 20px;
                                position: relative;
                                top: -1px;
                                margin-left: 10px;
                            }
                            .price-year-now {
                                color: #FFC874;
                                margin-left: 5px;
                                .money {
                                    font-size: 20px;
                                }
                            }
                            .price-month-now,
                            .price-year-original {
                                color: @small-color;
                                font-size: 12px;
                            }

                            .price-year-original {
                                margin-left: 5px;
                                text-decoration: line-through;
                            }
                        }
                        &.free2 {
                            padding: 3px 5px;
                        }
                    }
                }

                .product-action {
                    text-align: center;
                    button {
                        margin-top: 12px;
                    }
                }
            }
        }
    }

    .buy-btn {
        width: 90px;
        background-color: #d9c27f;
        border: none;
        font-size: 12px;
        color: #051968;
        height: 20px;
        border-radius: 2px;
    }
}

nz-footer {
    padding: 0;
    background-color: #2e3033;
    color: #fff;

    .foot-section-header {
        margin: -12px -15px -12px -12px;
        padding: 15px;
        border-bottom: 1px solid lighten(#323437, 5%);
    }

    .foot-section-name {
        color: #fff;
        display: inline-block;
    }
    .pull-right {
        color: #666;
        margin-top: 2px;
    }

    .links-list {
        margin: 0 -12px;
        li {
            a {
                display: block;
                padding: 10px 15px;
                font-size: 12px;
                color: #999;
            }
        }
    }

    .contact-us {
        padding: 30px 15px 0px;
        text-align: center;
        
        .qr-code {
            display: block;
            width: 40%;
            margin: auto;
        }

        .hint {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 15px;
        }

        a {
            margin-bottom: 15px;
        }

        a[nzType="default"] {
            color: #ccc;
        }
    }

    .copyright {
        font-size: 12px;
        text-align: center;
        padding: 10px 15px 15px;
        color: #ccc;
    }
}

.product-content {
    padding: 15px;
    // max-height: 405px;
    margin-bottom: 0;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
}

.product-brief {
    background-color: #efefef;
    padding: 10px;
    margin-bottom: 15px;
    
    .product-type {
        color: #999;
    }
}

.order-form {
    nz-form-label {
        padding-bottom: 4px;
    }

    nz-form-item {
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .selected-item {
        height: 32px;
        border: 1px solid @primary;
        line-height: 30px;
        padding: 0 10px;
        background-color: lighten(@primary, 50%);
    }
}

.action-bar {
    height: 40px;
    line-height: 40px;
    bottom: 0;
    left: 0;
    right: 0;
    padding-left: 15px;
    background-color: #f7f8fa;
    .buy-now {
        background-color: #ae9455;
        color: #fff;
        text-align: center;
    }
}

.item-title {
    font-size: 18px;
        font-weight: bold;
        margin-bottom: 20px;

        &:before {
            content: '';
            display: inline-block;
            height: 20px;
            width: 2px;
            background-color: @primary;
            vertical-align: middle;
            position: relative;
            top: -2px;
            margin-right: 14px;
        }
}
.form-hint {
    padding: 0 !important;
    color: @red;
    font-size: 12px;
}

.small.tip {
    font-size: 12px;
    margin-bottom: 0;
    margin-top: 2px;
    color: #999;

    a {
        color: @primary;
    }
}
.logout-header {
    background: @light-gray;
    padding: 10px;
    // height: 30px;
}
.logout-btn {
    width: 100%;
    height: 50px;
    font-size: 18px;
    text-align: center;
    line-height: 50px;
    background: #fff;
    color: @red;
}
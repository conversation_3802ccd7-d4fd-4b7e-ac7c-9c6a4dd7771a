import {Component, OnInit} from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-cloud-disk',
    templateUrl: './cloud-disk.component.html',
    styleUrls: ['./cloud-disk.component.less']
})
export class CloudDiskComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '云硬盘',
        enName: 'Elastic Volume Service',
        desc: '云盘是采用分布式三副本设计的，为云服务器提供基于网络连接的持久化块级数据存储服务的设备，支持动态的将云盘挂载到DC2云服务器上，并对挂载后的云盘进行分区。',
        bgColor: '#1a284d',
        orderLink: '/console/cloud-server/cloud-disk-config',
        type: 'cloudDiskProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

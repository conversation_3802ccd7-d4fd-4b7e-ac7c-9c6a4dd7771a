import { Component, OnInit } from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-rds',
    templateUrl: './rds.component.html',
    styleUrls: ['./rds.component.less']
})
export class RdsComponent implements OnInit {

    constructor(
    ) {}

    intro = {
        title: 'RDS',
        enName: 'Cloud Relational Database Service',
        desc: 'AIC应用托管平台提供云上关系型数据库（Cloud Relational Database Service，简称云RDS），让用户可以更方便、快速使用高可用的关系型数据库服务，为用户提供免费的服务运维和管理工作，节省运维资源，支持灵活的规模扩展，适应高速的业务发展。',
        bgColor: '#1a284d',
        orderLink: '/console/rds/rds-config',
        type: 'rdsProduction',
    }

    ngOnInit() {}

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

@import '../../../../style/common/_variable.less';

@left-menu-width: 40px;

#console-layout.full {
    height: 100%;
    
    .console-left-container {
        width: @left-menu-width;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        background-color: #000f1d;
        overflow: auto;
    }

    .console-right-container {
        position: fixed;
        top: 0;
        bottom: 0;
        left: @left-menu-width;
        right: 0;

        .console-nav-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
        }

        .feature-container {
            padding: 50px 0 0 0;
            height: 100%;
            overflow: auto;
        }
    }

    .console-menu {
        padding: 8px 0;
        .logo {
            a {
                color: #fff;
                font-size: 16px;
                text-decoration: none;
            }
            img {
                width: 30px;
                height: 35px;
                margin-right: 5px;
                margin-left: 4px;
            }
        }
        
        .menu-list {
            padding: 5px 0;
            margin: 0;
            list-style: none;
    
            .menu-item {
                a {
                    display: block;
                    color: #a4a4a4;
                    text-decoration: none;
                    font-size: 14px;
                    padding: 10px 0;
    
                    &:hover {
                        color: lighten(#a4a4a4, 20%);
                    }
                }

                &.active {
                    a {
                        color: @primary;
                        cursor: default;
                    }
                }
    
                .icon {
                    display: inline-block;
                    width: 18px;
                    height: 18px;
                    margin-right: 10px;
                    margin-left: 12px;
                }
            }
        }
    }
}
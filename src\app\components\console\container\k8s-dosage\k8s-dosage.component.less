// .content-body-item {
//     width: 100%;
//     min-width: 1280px;
//     margin-top: 30px;
//     display: flex;
//     justify-content: left;
//     /*border: 1px solid #000;*/
//     overflow: hidden;
//     overflow-wrap: break-word;
//     flex-wrap: wrap;
// }

.echarts-container {
    display: inline-block;
    width: 100%;
    height: 400px;
    border: 1px solid #DBDEE3;
    border-radius: 3px;
    margin: 10px 15px;
}
.echarts-container-gauge {
    display: inline-block;
    position: relative;
    width: 400px;
    height: 400px;
    //border: 1px solid #DBDEE3;
    //border-radius: 3px;
    margin: 10px 15px;
}
.echarts-container > div, .echarts-container-gauge > div {
    width: 100%;
    height: 100%;
}
.overview-tips {
    position: absolute;
    bottom: 10px;
    left: 140px;
    p {
        margin: 3px 0;
    }
}
.gauge-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.tips-text {
    font-size: 12px;
    color: #999;
}

import { Component, OnInit, OnDestroy, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { VdiPoolService } from '../../../../service/console/vdi-pool/vdi-pool.service';
import { CloudServerService } from '../../../../service/console/cloud-server/cloud-server.service';
import { VdiService } from '../../../../service/console/vdi/vdi.service';
import { ServicePlanService } from '../../../../service/console/system/service-plan.service';

@Component({
    selector: 'app-vdi-pool',
    templateUrl: './vdi-pool.component.html',
    styleUrls: ['./vdi-pool.component.less']
})
export class VdiPoolComponent implements OnInit, OnDestroy {
    @ViewChild('renderItemTemplate', { static: true }) renderItemTemplate: TemplateRef<any>;

    // 视图状态控制
    currentView: 'pool' | 'vdi' = 'pool'; // 'pool' 显示虚拟机池列表，'vdi' 显示VDI列表
    currentPool: any = null; // 当前选中的虚拟机池

    // 虚拟机池列表数据
    vdiPoolList: any[] = [];
    isLoading = false;
    keyword = '';

    // VDI列表数据
    vdiList: any[] = [];
    vdiLoading = false;
    vdiKeyword = '';
    vdiPager = {
        page: 1,
        pageSize: 10,
        total: 0
    };
    vdiFilters = {
        pageNum: 0,
        pageSize: 10
    };

    // 分页配置
    pager = {
        page: 1,
        pageSize: 10,
        total: 0
    };

    filters = {
        pageNum: 0,
        pageSize: 10
    };

    // 表格列配置
    cols = [
        { title: '名称', key: 'name', sortable: true },
        { title: '虚拟机配置', key: 'servicePlanName', sortable: false },
        { title: '镜像', key: 'imageName', sortable: true },
        { title: '网络', key: 'vpcName', sortable: true },
        { title: '虚拟机数量', key: 'vmCount', sortable: true },
        { title: '用户组', key: 'userGroup', sortable: false },
        { title: '创建时间', key: 'createTm', sortable: true },
        { title: '架构名', key: 'architecture', sortable: true },
        { title: '操作', key: 'action', sortable: false }
    ];

    // 创建弹框相关
    createModalVisible = false;
    createForm: FormGroup;

    // 绑定用户组弹框相关
    bindUserGroupModalVisible = false;
    bindUserGroupForm: FormGroup;
    currentVdiPool: any = null;
    
    // 初始化数据
    initData = {
        vpc: [],
        images: [],
        servicePlans: []
    };

    // 写死的数据
    servicePlanList = [
        { servicePlanId: 1, servicePlanName: "4c16g", cpu: 4, memory: 16 },
        { servicePlanId: 2, servicePlanName: "8c32g", cpu: 8, memory: 32 }
    ];

    imageList = [];

    // 用户组列表（模拟数据）
    userGroupList = [
        { id: 1, name: "开发组", description: "开发人员用户组", memberCount: 15 },
        { id: 2, name: "测试组", description: "测试人员用户组", memberCount: 8 },
        { id: 3, name: "运维组", description: "运维人员用户组", memberCount: 5 },
        { id: 4, name: "产品组", description: "产品经理用户组", memberCount: 3 },
        { id: 5, name: "设计组", description: "UI/UX设计师用户组", memberCount: 4 },
        { id: 6, name: "管理组", description: "管理层用户组", memberCount: 2 },
        { id: 7, name: "实习生组", description: "实习生用户组", memberCount: 10 }
    ];

    constructor(
        private fb: FormBuilder,
        private router: Router,
        private msg: NzMessageService,
        private modal: NzModalService,
        private vdiPoolService: VdiPoolService,
        private cloudServerService: CloudServerService,
        private vdiService: VdiService,
        private servicePlanService: ServicePlanService
    ) {
        this.initCreateForm();
        this.initBindUserGroupForm();
    }

    ngOnInit(): void {
        this.getVdiPoolList();
        this.getInitData();
        this.loadImageList();
    }

    loadImageList(): Promise<any> {
        // 如果已经在加载中或已加载，直接返回
        if (this.imageList.length > 0) {
            return Promise.resolve({ success: true });
        }

        const params = {
            bean: {
                description: 'vdi'
            }
        };
        return this.cloudServerService.getImage(params)
            .then(rs => {
                if (rs.success) {
                    this.imageList = rs.data || [];
                }
                return rs;
            })
            .catch(err => {
                console.error('加载镜像列表失败', err);
                return { success: false };
            });
    }

    ngOnDestroy(): void {
        // 清理订阅
    }

    initCreateForm(): void {
        this.createForm = this.fb.group({
            name: ['', [Validators.required]],
            servicePlanId: [null, [Validators.required]],
            imageId: [null, [Validators.required]],
            vpcId: [null, [Validators.required]],
            networkId: [null, [Validators.required]]
        });
    }

    initBindUserGroupForm(): void {
        this.bindUserGroupForm = this.fb.group({
            userGroupIds: [[], [Validators.required]]
        });
    }

    // 获取虚拟机池列表
    getVdiPoolList(filters?: any): void {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);
        
        if (keyword) {
            params.bean = {
                name: keyword
            };
        }

        this.isLoading = true;

        // 暂时屏蔽真实接口调用，使用假数据
        // this.vdiPoolService.getVdiPoolList(params)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.vdiPoolList = rs.data.dataList || [];
        //             this.pager = {
        //                 page: rs.data.pageNum + 1,
        //                 pageSize: rs.data.pageSize,
        //                 total: rs.data.recordCount,
        //             };
        //         } else {
        //             this.msg.error(`获取虚拟机池列表失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //         this.isLoading = false;
        //     })
        //     .catch(err => {
        //         this.msg.error('获取虚拟机池列表失败');
        //         this.isLoading = false;
        //     });

        // 使用假数据模拟
        setTimeout(() => {
            const mockVdiPoolList = this.generateMockVdiPoolData(keyword);
            this.vdiPoolList = mockVdiPoolList;
            this.pager = {
                page: filters.pageNum + 1,
                pageSize: filters.pageSize,
                total: mockVdiPoolList.length + 3, // 模拟总数比当前页多一些
            };
            this.isLoading = false;
        }, 800); // 模拟网络延迟
    }

    // 生成模拟VDI Pool数据
    private generateMockVdiPoolData(keyword?: string): any[] {
        const mockData = [
            {
                id: 1,
                name: 'dev-pool-001',
                servicePlanName: '4核16G',
                imageName: 'Windows 10 Enterprise',
                vpcName: 'vpc-dev',
                vmCount: 5,
                userGroup: '开发组、测试组',
                userGroupIds: [1, 2],
                createTm: '2024-01-15 10:30:00',
                architecture: 'x86_64'
            },
            {
                id: 2,
                name: 'test-pool-002',
                servicePlanName: '8核32G',
                imageName: 'Ubuntu 20.04',
                vpcName: 'vpc-test',
                vmCount: 3,
                userGroup: '测试组',
                userGroupIds: [2],
                createTm: '2024-01-16 14:20:00',
                architecture: 'x86_64'
            },
            {
                id: 3,
                name: 'prod-pool-003',
                servicePlanName: '16核32G',
                imageName: 'CentOS 8',
                vpcName: 'vpc-prod',
                vmCount: 8,
                userGroup: '运维组、管理组',
                userGroupIds: [3, 6],
                createTm: '2024-01-17 09:15:00',
                architecture: 'x86_64'
            },
            {
                id: 4,
                name: 'design-pool-004',
                servicePlanName: '8核32G',
                imageName: 'Windows 11 Pro',
                vpcName: 'vpc-design',
                vmCount: 2,
                userGroup: '设计组',
                userGroupIds: [5],
                createTm: '2024-01-18 16:45:00',
                architecture: 'x86_64'
            },
            {
                id: 5,
                name: 'intern-pool-005',
                servicePlanName: '4核16G',
                imageName: 'Ubuntu 22.04',
                vpcName: 'vpc-intern',
                vmCount: 10,
                userGroup: '', // 未绑定用户组
                userGroupIds: [],
                createTm: '2024-01-19 11:30:00',
                architecture: 'x86_64'
            }
        ];

        // 如果有搜索关键词，过滤数据
        if (keyword) {
            return mockData.filter(item =>
                item.name.toLowerCase().includes(keyword.toLowerCase()) ||
                item.servicePlanName.toLowerCase().includes(keyword.toLowerCase()) ||
                item.imageName.toLowerCase().includes(keyword.toLowerCase()) ||
                item.userGroup.toLowerCase().includes(keyword.toLowerCase())
            );
        }

        return mockData;
    }

    // 获取初始化数据
    getInitData(): void {
        // 使用与vm-config相同的方式获取VPC数据
        this.cloudServerService.getCloudServerInitData(window.localStorage.getItem('userId'))
            .then(rs => {
                if (rs.success) {
                    this.initData.vpc = rs.data.vpc || [];
                } else {
                    this.msg.error(`获取VPC数据失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.msg.error('获取VPC数据失败');
            });

        // 从API获取VDI类型的服务计划
        this.servicePlanService.listByType('vdi')
            .then(rs => {
                if (rs.success) {
                    this.initData.servicePlans = rs.data || [];
                    // 更新servicePlanList以保持兼容性
                    this.servicePlanList = rs.data || [];
                } else {
                    this.msg.error(`获取虚拟机配置失败${rs.message ? ': ' + rs.message : ''}`);
                    // 如果API失败，使用默认数据
                    this.initData.servicePlans = this.servicePlanList;
                }
            })
            .catch(err => {
                this.msg.error('获取虚拟机配置失败');
                // 如果API失败，使用默认数据
                this.initData.servicePlans = this.servicePlanList;
            });

        // 设置镜像数据（暂时保持写死）
        this.initData.images = this.imageList;
    }

    // 搜索
    search(): void {
        this.filters.pageNum = 0;
        this.getVdiPoolList();
    }

    // 刷新
    refresh(): void {
        this.getVdiPoolList();
    }

    // 分页变化
    pageChanged(pageNum: number): void {
        this.filters.pageNum = pageNum - 1;
        this.getVdiPoolList();
    }

    // 排序变化
    onParamsChange(params: any): void {
        // 处理排序逻辑
        this.getVdiPoolList();
    }

    // 显示创建弹框
    showCreateModal(): void {
        this.createModalVisible = true;
        this.createForm.reset();
        // 重置时清空网络选择
        this.createForm.patchValue({
            vpcId: null,
            networkId: null
        });
    }

    // 取消创建
    cancelCreate(): void {
        this.createModalVisible = false;
        this.createForm.reset();
        // 重置时清空网络选择
        this.createForm.patchValue({
            vpcId: null,
            networkId: null
        });
    }

    // 选择专有网络后设置子网
    selectVpc(vpc: any): void {
        if (!vpc) {
            this.createForm.patchValue({
                networkId: null
            });
            return;
        }
        
        if (vpc.network && vpc.network.length > 0) {
            this.createForm.patchValue({
                networkId: vpc.network[0].id
            });
        } else {
            this.createForm.patchValue({
                networkId: null
            });
        }
    }

    // 获取选中的网络名称
    getSelectedNetworkName(): string {
        const vpcId = this.createForm.get('vpcId')?.value;
        const networkId = this.createForm.get('networkId')?.value;

        if (!vpcId || !networkId) {
            return '';
        }

        const network = vpcId.network?.find((net: any) => net.id === networkId);
        return network ? network.name : '';
    }

    // 提交创建
    submitCreate(): void {
        if (this.createForm.invalid) {
            Object.values(this.createForm.controls).forEach(control => {
                if (control.invalid) {
                    control.markAsDirty();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });
            return;
        }

        const formValue = this.createForm.value;
        const selectedServicePlan = this.initData.servicePlans.find(sp => sp.id === formValue.servicePlanId);

        const params = {
            name: formValue.name,
            imageId: formValue.imageId,
            servicePlan: {id : formValue.servicePlanId},
            price: selectedServicePlan?.price || 0,
            vpcId: formValue.vpcId?.id || formValue.vpcId,
            networkId: formValue.networkId
        };

        this.vdiPoolService.createVdiPool(params)
            .then(rs => {
                if (rs.success) {
                    this.msg.success('正在创建虚拟机池，请稍候');
                    this.cancelCreate();
                    this.refresh();
                } else {
                    this.msg.error(`创建虚拟机池失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.msg.error('创建虚拟机池失败');
            });
    }

    // 查看虚拟机池中的虚拟机列表 - 切换到VDI视图
    viewVdiList(vdiPool: any): void {
        this.currentPool = vdiPool;
        this.currentView = 'vdi';
        this.vdiKeyword = '';
        this.vdiFilters.pageNum = 0;
        this.getVdiList();
    }

    // 返回虚拟机池列表
    backToPoolList(): void {
        this.currentView = 'pool';
        this.currentPool = null;
        this.vdiList = [];
    }

    // 绑定用户组
    bindUserGroup(item: any): void {
        this.currentVdiPool = item;
        this.bindUserGroupModalVisible = true;
        // 如果已有绑定的用户组，预选中
        const currentUserGroups = item.userGroupIds || [];
        this.bindUserGroupForm.patchValue({
            userGroupIds: currentUserGroups
        });
    }

    // 取消绑定用户组
    cancelBindUserGroup(): void {
        this.bindUserGroupModalVisible = false;
        this.bindUserGroupForm.reset();
        this.currentVdiPool = null;
    }

    // 提交绑定用户组
    submitBindUserGroup(): void {
        if (this.bindUserGroupForm.invalid) {
            Object.values(this.bindUserGroupForm.controls).forEach(control => {
                if (control.invalid) {
                    control.markAsDirty();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });
            return;
        }

        const userGroupIds = this.bindUserGroupForm.value.userGroupIds;
        const selectedGroups = this.userGroupList.filter(group => userGroupIds.includes(group.id));
        const groupNames = selectedGroups.map(group => group.name).join('、');

        // 模拟绑定用户组操作
        this.msg.success(`成功将虚拟机池 "${this.currentVdiPool.name}" 绑定到用户组：${groupNames}`);

        // 更新本地数据
        const vdiPool = this.vdiPoolList.find(pool => pool.id === this.currentVdiPool.id);
        if (vdiPool) {
            vdiPool.userGroupIds = userGroupIds;
            vdiPool.userGroup = groupNames;
            vdiPool.updateTime = new Date().toLocaleString();
        }

        this.cancelBindUserGroup();
    }

    // 获取用户组显示名称
    getUserGroupDisplayName(group: any): string {
        return `${group.name} (${group.memberCount}人)`;
    }

    // 解绑用户组
    unbindUserGroup(item: any): void {
        this.modal.confirm({
            nzTitle: '解绑用户组',
            nzContent: '确定要解绑用户组吗？',
            nzOnOk: () => {
                // TODO: 实现解绑用户组逻辑
                this.msg.success('解绑用户组成功');
            }
        });
    }

    // 删除虚拟机池
    deleteVdiPool(item: any): void {
        this.vdiPoolService.deleteVdiPool(item.id)
            .then(rs => {
                if (rs.success) {
                    this.msg.success('删除虚拟机池成功');
                    this.refresh();
                } else {
                    this.msg.error(`删除虚拟机池失败${rs.message ? ': ' + rs.message : ''}`);
                }
            })
            .catch(err => {
                this.msg.error('删除虚拟机池失败');
            });
    }

    // ==================== VDI 相关方法 ====================

    // 获取VDI列表
    getVdiList(filters?: any): void {
        if (!this.currentPool) return;

        filters = filters || this.vdiFilters;
        let keyword = this.vdiKeyword.trim();
        let params = Object.assign({}, filters, { poolId: this.currentPool.id });

        if (keyword) {
            params.bean = {
                name: keyword
            };
        }

        this.vdiLoading = true;

        // 暂时屏蔽真实接口调用，使用假数据
        // this.vdiService.getVdiList(params)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.vdiList = rs.data.dataList || [];
        //             this.vdiPager = {
        //                 page: rs.data.pageNum + 1,
        //                 pageSize: rs.data.pageSize,
        //                 total: rs.data.recordCount,
        //             };
        //         } else {
        //             this.msg.error(`获取虚拟机列表失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //         this.vdiLoading = false;
        //     })
        //     .catch(err => {
        //         this.msg.error('获取虚拟机列表失败');
        //         this.vdiLoading = false;
        //     });

        // 使用假数据模拟
        setTimeout(() => {
            const mockVdiList = this.generateMockVdiData(keyword);
            this.vdiList = mockVdiList;
            this.vdiPager = {
                page: filters.pageNum + 1,
                pageSize: filters.pageSize,
                total: mockVdiList.length + 5, // 模拟总数比当前页多一些
            };
            this.vdiLoading = false;
        }, 800); // 模拟网络延迟
    }

    // 生成假数据
    private generateMockVdiData(keyword?: string): any[] {
        const poolName = this.currentPool?.name || 'Pool';
        const mockData = [
            {
                id: 1,
                name: `VDI-${poolName}-001`,
                imageName: 'Linux',
                account: 'user001',
                ipAddress: '*************',
                cpu: '4核',
                memory: '8G',
                disk: '100G',
                vdiStatus: 'ACTIVE',
                vdiTask: '',
                powerStatus: 'ACTIVE',
                resetOnReboot: true,
                gpuSupport: true,
                share: false,
                vip: false,
                locked: false,
                host: 'host-01',
                group: 'group-dev'
            },
            {
                id: 2,
                name: `VDI-${poolName}-002`,
                imageName: 'Linux',
                account: 'user002',
                ipAddress: '*************',
                cpu: '8核',
                memory: '16G',
                disk: '200G',
                vdiStatus: 'STOPPED',
                vdiTask: '',
                powerStatus: 'SHUTOFF',
                resetOnReboot: false,
                gpuSupport: false,
                share: true,
                vip: true,
                locked: false,
                host: 'host-02',
                group: 'group-test'
            },
            {
                id: 3,
                name: `VDI-${poolName}-003`,
                imageName: 'Linux',
                account: 'admin',
                ipAddress: '*************',
                cpu: '16核',
                memory: '32G',
                disk: '500G',
                vdiStatus: 'ACTIVE',
                vdiTask: '',
                powerStatus: 'ACTIVE',
                resetOnReboot: true,
                gpuSupport: true,
                share: false,
                vip: false,
                locked: true,
                host: 'host-03',
                group: 'group-prod'
            },
            {
                id: 4,
                name: `VDI-${poolName}-004`,
                imageName: 'Linux',
                account: 'developer',
                ipAddress: '*************',
                cpu: '4核',
                memory: '8G',
                disk: '150G',
                vdiStatus: 'ACTIVE',
                vdiTask: '',
                powerStatus: 'ACTIVE',
                resetOnReboot: false,
                gpuSupport: false,
                share: false,
                vip: false,
                locked: false,
                host: 'host-01',
                group: 'group-dev'
            }
        ];

        // 如果有搜索关键词，过滤数据
        if (keyword) {
            return mockData.filter(item =>
                item.name.toLowerCase().includes(keyword.toLowerCase()) ||
                item.account.toLowerCase().includes(keyword.toLowerCase()) ||
                item.ipAddress.includes(keyword)
            );
        }

        return mockData;
    }

    // VDI搜索
    searchVdi(): void {
        this.vdiFilters.pageNum = 0;
        this.getVdiList();
    }

    // VDI刷新
    refreshVdi(): void {
        this.getVdiList();
    }

    // VDI分页变化
    vdiPageChanged(pageNum: number): void {
        this.vdiFilters.pageNum = pageNum - 1;
        this.getVdiList();
    }

    // VDI状态映射
    private VDI_STATUS_MAP = {
        'ACTIVE': '运行中',
        'BUILD': '创建中',
        'REBUILD': '重装中',
        'SUSPENDED': '休眠中',
        'PAUSED': '已暂停',
        'STOPPED': '已停止',
        'SHUTOFF': '关机',
        'ERROR': '错误',
        'UNKNOWN': '未知'
    };

    private POWER_STATUS_MAP = {
        'ACTIVE': '开机',
        'STOPPED': '关机',
        'SHUTOFF': '关机',
        'SUSPENDED': '休眠',
        'PAUSED': '暂停',
        'ERROR': '错误',
        'UNKNOWN': '未知'
    };

    // 获取VDI状态文本
    getVdiStatusText(item: any): string {
        return this.VDI_STATUS_MAP[item.vdiStatus] || '-';
    }

    // 获取电源状态文本
    getPowerStatusText(item: any): string {
        return this.POWER_STATUS_MAP[item.powerStatus] || '-';
    }

    // VDI操作方法 (暂时使用模拟数据)
    powerOnVdi(item: any): void {
        // 暂时屏蔽真实接口调用
        // this.vdiService.powerOnVdi(item.id)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.msg.success('开机操作已提交');
        //             this.refreshVdi();
        //         } else {
        //             this.msg.error(`开机失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //     })
        //     .catch(err => {
        //         this.msg.error('开机操作失败');
        //     });

        // 模拟开机操作
        this.msg.success('开机操作已提交');
        setTimeout(() => {
            // 更新本地数据状态
            const vdi = this.vdiList.find(v => v.id === item.id);
            if (vdi) {
                vdi.powerStatus = 'ACTIVE';
                vdi.vdiStatus = 'ACTIVE';
            }
        }, 1000);
    }

    powerOffVdi(item: any): void {
        // 暂时屏蔽真实接口调用
        // this.vdiService.powerOffVdi(item.id)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.msg.success('关机操作已提交');
        //             this.refreshVdi();
        //         } else {
        //             this.msg.error(`关机失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //     })
        //     .catch(err => {
        //         this.msg.error('关机操作失败');
        //     });

        // 模拟关机操作
        this.msg.success('关机操作已提交');
        setTimeout(() => {
            // 更新本地数据状态
            const vdi = this.vdiList.find(v => v.id === item.id);
            if (vdi) {
                vdi.powerStatus = 'SHUTOFF';
                vdi.vdiStatus = 'STOPPED';
            }
        }, 1000);
    }

    rebootVdi(item: any): void {
        // 暂时屏蔽真实接口调用
        // this.vdiService.rebootVdi(item.id)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.msg.success('重启操作已提交');
        //             this.refreshVdi();
        //         } else {
        //             this.msg.error(`重启失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //     })
        //     .catch(err => {
        //         this.msg.error('重启操作失败');
        //     });

        // 模拟重启操作
        this.msg.success('重启操作已提交');
        setTimeout(() => {
            // 更新本地数据状态
            const vdi = this.vdiList.find(v => v.id === item.id);
            if (vdi) {
                vdi.vdiTask = '重启中';
            }
            // 2秒后恢复正常状态
            setTimeout(() => {
                if (vdi) {
                    vdi.vdiTask = '无';
                    vdi.powerStatus = 'ACTIVE';
                    vdi.vdiStatus = 'ACTIVE';
                }
            }, 2000);
        }, 500);
    }

    remoteDesktop(item: any): void {
        // 暂时屏蔽真实接口调用
        // this.vdiService.getRemoteDesktopUrl(item.id)
        //     .then(rs => {
        //         if (rs.success && rs.data.url) {
        //             window.open(rs.data.url, '_blank');
        //         } else {
        //             this.msg.error('获取远程桌面连接失败');
        //         }
        //     })
        //     .catch(err => {
        //         this.msg.error('连接远程桌面失败');
        //     });

        // 模拟远程桌面连接
        if (item.powerStatus === 'ACTIVE') {
            this.msg.success(`正在连接到 ${item.name} 的远程桌面...`);
            // 模拟打开新窗口
            setTimeout(() => {
                this.msg.info('远程桌面连接已建立（模拟）');
            }, 1000);
        } else {
            this.msg.warning('虚拟机未运行，无法连接远程桌面');
        }
    }

    deleteVdi(item: any): void {
        // 暂时屏蔽真实接口调用
        // this.vdiService.deleteVdi(item.id)
        //     .then(rs => {
        //         if (rs.success) {
        //             this.msg.success('删除操作已提交');
        //             this.refreshVdi();
        //         } else {
        //             this.msg.error(`删除失败${rs.message ? ': ' + rs.message : ''}`);
        //         }
        //     })
        //     .catch(err => {
        //         this.msg.error('删除操作失败');
        //     });

        // 模拟删除操作
        this.msg.success('删除操作已提交');
        setTimeout(() => {
            // 从本地数据中移除
            const index = this.vdiList.findIndex(v => v.id === item.id);
            if (index > -1) {
                this.vdiList.splice(index, 1);
                this.vdiPager.total = Math.max(0, this.vdiPager.total - 1);
            }
        }, 1000);
    }
}

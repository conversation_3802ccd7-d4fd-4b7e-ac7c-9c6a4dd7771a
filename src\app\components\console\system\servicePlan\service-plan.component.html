<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入服务计划名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch>
                        <i nz-icon nzType="search"></i>
                    </button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button nzType="primary" class="primary" (click)="addServicePlan()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新增服务计划
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">服务计划</span>
            </div>
            <nz-table #tableList
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="tableData">
                <thead>
                    <tr>
                        <th nzSortKey="name" [nzSortFn]="true">名称</th>
                        <th nzSortKey="servicePlanType" [nzSortFn]="true">类型</th>
                        <th>服务编码</th>
<!--                        <th>配置</th>-->
                        <th nzSortKey="autoEffectiveDate" [nzSortFn]="true">生效日期</th>
                        <th nzSortKey="autoExpiryDate" [nzSortFn]="true">结束日期</th>
                        <th>Region</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data; trackBy: trackById">
                        <td>{{data.name}}</td>
                        <td>{{getServicePlanType(data.servicePlanType)}}</td>
                        <td>{{data.servicePlanCode || '-'}}</td>
<!--                        <th class="flex" style="flex-direction: column">-->
<!--                            <span *ngFor="let item of data.servicePlanItems; trackBy: trackById">{{item.servicePlanItemTypeTxt + ':' + (item.amount ? item.amount : '') + (item.imageName ? item.imageName : '') }}</span>-->
<!--                        </th>-->
                        <td>{{formatDate(data.autoEffectiveDate)}}</td>
                        <td>{{formatDate(data.autoExpiryDate)}}</td>
                        <td>{{formatRegions(data.servicePlanRegionRelations)}}</td>
                        <td>
                            <div class="on-table-actions" [hidden]="busyStatus[data.id]">
                                <div class="on-table-action-item" (click)="editServicePlan(data)">
                                    <i nzTooltipTitle="编辑"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon" nz-icon nzType="edit" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipPlacement="top"
                                     nzPopconfirmTitle="确定要删除该服务计划吗？"
                                     (nzOnConfirm)="deleteServicePlan(data)">
                                    <i nzTooltipTitle="删除"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="delete" nzTheme="outline"></i>
                                </div>
                            </div>
                            <div class="on-table-actions" [hidden]="!busyStatus[data.id]">
                                <i nz-icon nzType="loading" nzSpin></i>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>

            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>
        </div>
    </div>
</div>

<!-- 配置弹窗 -->
<app-service-plan-config
    [(visible)]="configModalVisible"
    [editData]="editData"
    (onSave)="onConfigSave()">
</app-service-plan-config>
.stat-card {
    // box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    // background-color: ;

    @size: 30px;

    i {
        position: relative;
        top: -2px;
    }

    position: relative;

    &:after {
        content: '=';
        position: absolute;
        width: @size;
        height: @size;
        line-height: @size;
        top: 50%;
        margin-top: -@size / 2;
        right: -(@size + 1px);
        text-align: center;
        font-size: @size;
        color: #888;
        opacity: 0;
        transition: opacity 0.3s ease 0s;
    }

    &.card-balance:after {
        content: '-';
    }

    &.card-bill:after {
        display: none;
    }
}

.balance-info {
    &:hover {
        .stat-card:after {
            opacity: 1;
        }
    }
}
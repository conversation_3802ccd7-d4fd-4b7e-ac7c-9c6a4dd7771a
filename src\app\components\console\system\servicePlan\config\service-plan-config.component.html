<nz-modal [(nzVisible)]="visible"
          [nzTitle]="modalTitle"
          [nzOkText]="editData ? '修改' : '新增'"
          (nzOnCancel)="handleCancel()"
          [nzWidth]="700"
          [nzOkLoading]="isLoading"
          [nzCancelLoading]="isLoading"
          (nzOnOk)="submit()">
    <ng-container *nzModalContent>
        <form [formGroup]="servicePlanForm" class="config-content sm">
            <!-- 基本信息 -->
            <div class="field-group service-plan-content">
                <div class="field-item required">
                    <label>
                        <span class="label-text">名称</span>
                        <input type="text" formControlName="name" placeholder="请输入服务计划名称">
                    </label>
                    <div *ngIf="isDirty(servicePlanForm.get('name'))" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('name').hasError('required')">
                            名称不能为空
                        </div>
                    </div>
                </div>
                <div class="field-item required">
                    <label>
                        <span class="label-text">类型</span>
                        <nz-select formControlName="servicePlanType" nzPlaceHolder="请选择服务计划类型">
                            <nz-option *ngFor="let option of servicePlanTypeOptions"
                                       [nzValue]="option.value"
                                       [nzLabel]="option.label">
                            </nz-option>
                        </nz-select>
                    </label>
                    <div *ngIf="isDirty(servicePlanForm.get('servicePlanType'))" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('servicePlanType').hasError('required')">
                            请选择服务计划类型
                        </div>
                    </div>
                </div>
            </div>
            <div class="field-group service-plan-content">
                <div class="field-item required">
                    <label>
                        <span class="label-text">价格</span>
                        <nz-input-number formControlName="price"
                                         [nzMin]="0"
                                         [nzStep]="0.01"
                                         [nzPrecision]="2"
                                         nzPlaceHolder="请输入价格">
                        </nz-input-number>
                    </label>
                    <div *ngIf="isDirty(servicePlanForm.get('price'))" class="form-hint error">
                        <div *ngIf="servicePlanForm.get('price').hasError('required')">
                            价格不能为空
                        </div>
                        <div *ngIf="servicePlanForm.get('price').hasError('min')">
                            价格不能小于0
                        </div>
                    </div>
                </div>
                <div class="field-item">
                    <div class="field-wrapper">
                        <span class="label-text">Region</span>
                        <nz-select formControlName="servicePlanRegionRelations"
                                   nzMode="multiple"
                                   nzPlaceHolder="请选择Region"
                                   style="pointer-events: auto !important; z-index: 999 !important; position: relative !important;">
                            <nz-option *ngFor="let region of regionList"
                                       [nzValue]="region.id"
                                       [nzLabel]="region.name">
                            </nz-option>
                        </nz-select>
                    </div>
                </div>
            </div>
            <div class="field-group service-plan-content">
                <div class="field-item">
                    <label>
                        <span class="label-text">生效日期</span>
                        <nz-date-picker formControlName="autoEffectiveDate"
                                        nzShowTime
                                        nzFormat="yyyy-MM-dd HH:mm"
                                        nzPlaceHolder="请选择生效日期">
                        </nz-date-picker>
                    </label>
                </div>
                <div class="field-item">
                    <label>
                        <span class="label-text">结束日期</span>
                        <nz-date-picker formControlName="autoExpiryDate"
                                        nzShowTime
                                        nzFormat="yyyy-MM-dd HH:mm"
                                        nzPlaceHolder="请选择结束日期">
                        </nz-date-picker>
                    </label>
                </div>
            </div>
            <div class="field-group">
                <div class="field-item">
                    <label>
                        <span class="label-text">说明</span>
                        <textarea formControlName="remark" 
                                  rows="3" 
                                  placeholder="请输入说明信息">
                        </textarea>
                    </label>
                </div>
            </div>

            <!-- 服务组件 -->
            <div class="field-group">
                <div class="field-title">
                    <span class="label-text" style="flex:8">服务组件</span>
                    <div class="add-button-wrapper" style="flex:1">
                        <button type="button"
                                nz-button
                                nzType="dashed"
                                nzSize="small"
                                [disabled]="!canAddMoreItems()"
                                [nz-tooltip]="!canAddMoreItems() ? '已达到最大组件数量限制' : ''"
                                nzTooltipPlacement="top"
                                (click)="addServicePlanItem()">
                            <i nz-icon nzType="plus"></i> 添加组件
                        </button>
                        <span class="component-count-info" *ngIf="!canAddMoreItems()">
                            ({{servicePlanItems.length}}/{{servicePlanItemTypeOptions.length}})
                        </span>
                    </div>
                </div>

                <div formArrayName="servicePlanItems" class="service-plan-grid">
                    <div *ngFor="let item of servicePlanItems.controls; let i = index"
                         [formGroupName]="i"
                         class="service-plan-card">

                        <div class="card-header">
                            <span class="card-title">组件 {{i + 1}}</span>
                            <button type="button"
                                    nz-button
                                    nzType="text"
                                    nzDanger
                                    nzSize="small"
                                    [disabled]="servicePlanItems.length <= 1"
                                    (click)="removeServicePlanItem(i)"
                                    class="delete-btn">
                                <i nz-icon nzType="delete"></i>
                            </button>
                        </div>

                        <div class="card-content">
                            <!-- 组件类型 - 始终显示 -->
                            <div class="field-item required">
                                <span class="field-label">组件类型</span>
                                <nz-select formControlName="servicePlanItemType"
                                           nzPlaceHolder="请选择组件类型"
                                           (ngModelChange)="onServicePlanItemTypeChange(i, $event)"
                                           class="field-control">
                                    <nz-option *ngFor="let option of servicePlanItemTypeOptions"
                                               [nzValue]="option.value"
                                               [nzLabel]="option.label"
                                               [nzDisabled]="isServicePlanItemTypeUsed(i, option.value)">
                                    </nz-option>
                                </nz-select>
                                <div *ngIf="isDirty(item.get('servicePlanItemType'))" class="field-error">
                                    <div *ngIf="item.get('servicePlanItemType').hasError('required')">
                                        请选择组件类型
                                    </div>
                                </div>
                            </div>

                            <!-- 子类 - 仅磁盘类型显示 -->
                            <div class="field-item"
                                 *ngIf="item.get('servicePlanItemType').value === 'DISK_GB'"
                                 [class.required]="item.get('servicePlanItemType').value === 'DISK_GB'">
                                <span class="field-label">子类</span>
                                <nz-select formControlName="servicePlanItemSubType"
                                           nzPlaceHolder="请选择子类"
                                           class="field-control">
                                    <nz-option *ngFor="let option of servicePlanItemSubTypeOptions"
                                               [nzValue]="option.value"
                                               [nzLabel]="option.label">
                                    </nz-option>
                                </nz-select>
                                <div *ngIf="isDirty(item.get('servicePlanItemSubType'))" class="field-error">
                                    <div *ngIf="item.get('servicePlanItemSubType').hasError('required')">
                                        请选择子类
                                    </div>
                                </div>
                            </div>

                            <!-- 镜像 - 仅镜像类型显示 -->
                            <div class="field-item"
                                 *ngIf="item.get('servicePlanItemType').value === 'IMAGE'"
                                 [class.required]="item.get('servicePlanItemType').value === 'IMAGE'">
                                <span class="field-label">镜像</span>
                                <nz-select formControlName="imageId"
                                           nzPlaceHolder="请选择镜像"
                                           nzAllowClear
                                           class="field-control">
                                    <nz-option *ngFor="let image of imageList"
                                               [nzValue]="image.id"
                                               [nzLabel]="image.name">
                                    </nz-option>
                                </nz-select>
                                <div *ngIf="isDirty(item.get('imageId'))" class="field-error">
                                    <div *ngIf="item.get('imageId').hasError('required')">
                                        请选择镜像
                                    </div>
                                </div>
                            </div>

                            <!-- 数量 - CPU、内存、带宽、磁盘类型显示 -->
                            <div class="field-item"
                                 *ngIf="item.get('servicePlanItemType').value && item.get('servicePlanItemType').value !== 'IMAGE'"
                                 [class.required]="item.get('servicePlanItemType').value && item.get('servicePlanItemType').value !== 'IMAGE'">
                                <span class="field-label">数量</span>
                                <nz-input-number formControlName="amount"
                                                 [nzMin]="1"
                                                 [nzStep]="1"
                                                 nzPlaceHolder="请输入数量"
                                                 class="field-control">
                                </nz-input-number>
                                <div *ngIf="isDirty(item.get('amount'))" class="field-error">
                                    <div *ngIf="item.get('amount').hasError('required')">
                                        数量不能为空
                                    </div>
                                    <div *ngIf="item.get('amount').hasError('min')">
                                        数量不能小于1
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </ng-container>
</nz-modal>

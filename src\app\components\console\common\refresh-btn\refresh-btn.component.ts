import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

@Component({
    selector: 'app-refresh-btn',
    templateUrl: './refresh-btn.component.html',
    styleUrls: ['./refresh-btn.component.less']
})
export class RefreshBtnComponent implements OnInit {
    constructor() {}

    @Input() option = {
        title: '点击刷新',
        iconName: 'reload',
    };

    @Output() refresh = new EventEmitter();

    clickSubject = new Subject();
    spinStatus = {
        start: false,
        reset: false,
    };

    ngOnInit() {
        this.clickSubject
        .pipe(
            debounceTime(500)
        )
        .subscribe((e) => {
            // 设置刷新按钮动画
            this.spinStatus.start = true;
            setTimeout(() => {
                this.spinStatus.reset = true;
                this.spinStatus.start = false;
                setTimeout(() => {
                    this.spinStatus.reset = false;
                }, 400)
            }, 400)

            this.refresh.emit(e)
        });
    }

    btnClick(evt) {
        this.clickSubject.next(evt);
    }
}

import {Component, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, Renderer2, After<PERSON>iewInit, HostListener, ChangeDetectorRef } from '@angular/core';
import { Router, ChildActivationEnd, NavigationEnd, NavigationStart, ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { filter, take, map, switchMap } from 'rxjs/operators';
import { ObservableService } from 'src/app/service/common/observable/observable.service';
import { k8sMenuData, title as k8sTitle } from './k8sMenuData';
import { operationMenuData, title as operationTitle } from './operationMenuData';
import { MenuService } from 'src/app/service/common/menu/menu.service';
// import { ScrollbarAppearance } from 'ngx-scrollbar/lib/ng-scrollbar-config';
import {ConsoleNavComponent} from 'src/app/components/console/common/console-nav/console-nav.component';
import { Subscription } from 'rxjs';

const subList: Subscription[] = [];

@Component({
    selector: 'app-console-layout',
    templateUrl: './console-layout.component.html',
    styleUrls: ['./console-layout.component.less']
})
export class ConsoleLayoutComponent implements OnInit, OnDestroy, AfterViewInit {

    @ViewChild(ConsoleNavComponent) consoleNavComponent: ConsoleNavComponent;
    currentRouteSnapshot: ActivatedRouteSnapshot | null = null;
    currentK8sId: string | null = null;
    menuData: any[] = [];

    ngAfterViewInit() {
        this.setAiModelHeight();
    }

    @HostListener('window:resize')
    onWindowResize() {
        this.setAiModelHeight();
    }

    setAiModelHeight() {
        const aiModal = this.el.nativeElement.querySelector('app-ai');
        const height = window.innerHeight - 54;
        this.renderer.setStyle(aiModal, 'height', height + 'px');
    }

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private obsService: ObservableService,
        private menuService: MenuService,
        private el: ElementRef,
        private renderer: Renderer2,
        private cdr: ChangeDetectorRef
    ) {
        if (!this.menuService.getCurrentMenuData().some(m => m.name === 'clusterInfo')) {
             this.menuService.registerMenuData('k8s', k8sMenuData);
        }

        // 确保运维管理菜单数据已注册
        if (!this.menuService.getCurrentMenuData().some(m => m.name === 'management')) {
             this.menuService.registerMenuData('operation', operationMenuData);
        }

        const menuDataSub = this.menuService.menuData$.subscribe(data => {
            this.menuData = data;
            this.updateFilteredMenuData();
            if (this.currentMenu) {
                const currentMenuName = this.currentMenu.name;
                let newCurrentMenu = this.menuData.find(item => item.name === currentMenuName);
                if (newCurrentMenu) {
                    this.currentMenu = newCurrentMenu;
                } else if (this.menuData.length > 0) {
                    // 当前菜单不存在时，选择第一个菜单并添加标签
                    this.setSubMenu(this.menuData[0]);
                }
            } else if (this.menuData.length > 0) {
                // 没有当前菜单时，选择第一个菜单并添加标签
                this.setSubMenu(this.menuData[0]);
            }
            if (this.currentMenu && this.filtedMenuData) {
                const currentMenuExistsInFiltered = this.filtedMenuData.some(item => item.name === this.currentMenu.name);
                if (!currentMenuExistsInFiltered && this.filtedMenuData.length > 0) {
                    const firstFiltered = this.filtedMenuData[0];
                    this.setSubMenu(firstFiltered);
                }
            }
        });
        subList.push(menuDataSub);

        const routeEventSub = this.router.events
            .pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(() => {
                let currentActivatedRoute = this.route;
                while (currentActivatedRoute.firstChild) {
                    currentActivatedRoute = currentActivatedRoute.firstChild;
                }
                this.currentRouteSnapshot = currentActivatedRoute.snapshot;
                if (this.currentRouteSnapshot && this.currentRouteSnapshot.paramMap.has('id')) {
                    this.currentK8sId = this.currentRouteSnapshot.paramMap.get('id');
                } else {
                    this.currentK8sId = null;
                }
            });
         subList.push(routeEventSub);

        const routerSub1 = this.router.events
        .pipe(
            filter(evt => evt instanceof NavigationEnd),
            take(1),
        )
        .subscribe(() => {
             let routeToCheck = this.route;
             while(routeToCheck.firstChild) routeToCheck = routeToCheck.firstChild;
             const subMenuName = routeToCheck.snapshot.data.name;
             if (subMenuName) {
                this.setCustomSubMenu(subMenuName);
                let initialCurrentMenu = this.menuData.find(item => item.name === subMenuName);
                if(initialCurrentMenu) {
                    this.currentMenu = initialCurrentMenu;
                    this.updateFilteredMenuData();
                }
            } else {
                 if (this.menuData.length > 0) {
                    this.setSubMenu(this.menuData[0]);
                    this.updateFilteredMenuData();
                }
            }
        });
        subList.push(routerSub1);

        const routerSub2 = this.router.events
        .pipe(
            filter(evt => evt instanceof NavigationEnd)
        )
        .subscribe((evt: NavigationEnd) => {
            // 根据URL自动切换菜单
            this.menuService.switchMenuByUrl(evt.urlAfterRedirects || evt.url);

            if(evt.url === '/console' || evt.urlAfterRedirects === '/console') {
                this.router.navigate(['/console/dashboard'], {
                    replaceUrl: true
                });
                 this.setSubMenuByNav('dashboard');
            }
            if (evt.urlAfterRedirects === '/console/dashboard') {
                this.setSubMenuByNav('dashboard');
            }
        });
        subList.push(routerSub2);

        const routerSub3 = this.router.events
            .pipe(
                filter(evt => evt instanceof NavigationStart)
            )
            .subscribe((e) => {
                if (this.currentMenu && this.currentMenu.subMenus) {
                    this.currentMenu.subMenus.forEach(item => {
                        if (item.hasOwnProperty('unfold')) {
                            delete (item as any).unfold;
                        }
                    });
                }
            });
        subList.push(routerSub3);
    }

    AIModalVisible = false;
    filtedMenuData : any;
    currentMenu: any = null;

    mainMenuFold:boolean = false;
    customSubMenu: boolean = false;
    rules = JSON.parse(window.localStorage.getItem("rules") || '[]');
    isAdmin = window.localStorage.getItem('isAdmin') === 'true'

    title = window.localStorage.getItem("title");
    logo = window.localStorage.getItem("logo");

    // 获取当前菜单标题
    get currentMenuTitle(): string {
        const menuType = this.menuService.getCurrentMenuType();
        if (menuType === 'k8s') {
            return k8sTitle;
        } else if (menuType === 'operation') {
            return operationTitle;
        }
        return '';
    }

    // 判断是否显示菜单标题
    get showMenuTitle(): boolean {
        const menuType = this.menuService.getCurrentMenuType();
        return menuType === 'k8s' || menuType === 'operation';
    }

    ngOnInit() {
        const menuSub = this.obsService.mainMenuFold.subscribe(fold => {
            this.mainMenuFold = fold;
        });
        subList.push(menuSub);
    }

    updateFilteredMenuData() {
        let menuDataToFilter = this.menuData || [];
        let filtered = this.filterMenu(menuDataToFilter);
        if(this.isAdmin) {
            this.filtedMenuData = filtered;
        } else {
            const currentUrl = this.rules.map(item => item.url);
            this.filtedMenuData = filtered.filter(menuItem =>
                currentUrl.includes(menuItem.routerLink) || menuItem.routerLink === 'app-system' || this.hasVisibleSubmenu(menuItem, currentUrl)
            );
        }
        if (this.currentMenu && this.filtedMenuData && !this.filtedMenuData.some(item => item.name === this.currentMenu.name)) {
             if (this.filtedMenuData.length > 0) {
                this.setSubMenu(this.filtedMenuData[0]);
             } else {
                this.currentMenu = null;
             }
        }
    }

    hasVisibleSubmenu(menuItem: any, allowedUrls: string[]): boolean {
        if (!menuItem.subMenus || this.isAdmin) {
            return false;
        }
        return menuItem.subMenus.some(sub => allowedUrls.includes(`${menuItem.routerLink}/${sub.routerLink}`));
    }

    filterMenu(menu: any[]) {
        return menu.filter(item => !item.hidden);
    }

    setSubMenu(subMenu) {
        if (!subMenu) return;

        if(subMenu.subMenus && subMenu.subMenus.length > 0){
            // 先清除所有子菜单的active状态
            subMenu.subMenus.forEach(item => {
                item.active = '';
            });
            // 设置第一个子菜单为active
            subMenu.subMenus[0].active = 'active';

            // 手动触发变更检测
            this.cdr.detectChanges();

            // 如果第一个子菜单有路由，自动导航到它
            if (subMenu.subMenus[0].routerLink) {
                setTimeout(() => {
                    this.navigateToSubMenu(subMenu, subMenu.subMenus[0]);
                }, 0);
            }
        } else {
            // 当一级菜单没有二级菜单时，直接为该一级菜单添加标签
            this.addTagForMainMenu(subMenu);
        }

        this.currentMenu = subMenu;
        this.setCustomSubMenu(subMenu.name);

        // 再次手动触发变更检测
        this.cdr.detectChanges();
    }

    setSubMenuByNav(subMenuName) {
        let activeSubMenu = this.menuData.find(item => item.name === subMenuName);
        if(activeSubMenu) {
            // 使用setSubMenu方法来确保子菜单的active状态被正确设置
            this.setSubMenu(activeSubMenu);
        } else {
            this.setCustomSubMenu(subMenuName);
        }
    }

    toggleMainMenu() {
        this.obsService.mainMenuFold.next(!this.mainMenuFold);
    }

    setCustomSubMenu(currenSubMenuName) {
        if (currenSubMenuName === 'devops') {
            this.customSubMenu = true;
        } else {
            this.customSubMenu = false;
        }
    }

    stopClick(evt) {
        evt.stopPropagation();
    }

    unfoldSubMenu(evt, subMenu) {
        let target = evt.target;
        let isSubMenuRouterActive = target.className.indexOf('active-sub') > -1;
        if (typeof (subMenu as any).unfold == 'undefined') {
            (subMenu as any).unfold = !isSubMenuRouterActive;
        } else {
            (subMenu as any).unfold = !(subMenu as any).unfold;
        }
    }

    navigateToSubMenu(mainMenu: any, subMenu: any): void {
        if (!mainMenu || !subMenu) return;

        // 确保所有子菜单的active状态被清除
        if (mainMenu.subMenus) {
            mainMenu.subMenus.forEach(item => {
                item.active = '';
            });
        }

        // 设置当前子菜单为active
        subMenu.active = 'active';

        // 手动触发变更检测
        this.cdr.detectChanges();

        const baseRoutePath = mainMenu.routerLink;
        const subRoutePath = subMenu.routerLink;
        let finalRoute: any[] = [];

        // 检查是否是运维管理菜单
        const isOperationMenu = this.menuService.getCurrentMenuType() === 'operation';

        // 检查是否需要ID参数
        const needsId = baseRoutePath.includes('k8s-detail');
        let currentId: string | null = null;

        if (needsId && this.currentRouteSnapshot && this.currentRouteSnapshot.paramMap.has('id')) {
            currentId = this.currentRouteSnapshot.paramMap.get('id');
        }

        if (needsId && currentId) {
            finalRoute = ['/console', baseRoutePath.replace(':id', currentId), subRoutePath];
        } else if (needsId && !currentId) {
             console.warn('导航错误：缺少必要的路由参数 ID');
             finalRoute = ['/console', baseRoutePath.replace(':id', 'PLACEHOLDER_ID')];
        } else if (isOperationMenu && !subRoutePath.startsWith('system/')) {
            // 运维管理菜单的子菜单项，直接导航到 /console/system/subRoutePath
            finalRoute = ['/console/system', subRoutePath];
        } else if (subRoutePath.includes('/')) {
            // 处理包含斜杠的路径，例如 monitor-brand/huawei 和 monitor-brand/aliyun
            // 分割路径
            const pathSegments = subRoutePath.split('/');
            // 完整路径构建 /console/cloud-monitor/monitor-brand/huawei
            finalRoute = ['/console/' + baseRoutePath].concat(pathSegments);
        } else {
            finalRoute = ['/console', baseRoutePath, subRoutePath];
        }

        this.router.navigate(finalRoute);
        this.updateNavTagsForSubMenu(mainMenu, subMenu, currentId);
    }

    // 为没有子菜单的一级菜单添加标签
    addTagForMainMenu(mainMenu: any) {
        if (!mainMenu) return;

        // 检查是否是k8s菜单，如果是则不添加标签
        const isK8sMenu = this.menuService.getCurrentMenuType() === 'k8s';
        if (isK8sMenu) {
            return; // k8s菜单不添加标签
        }

        // 检查是否是运维管理菜单
        const isOperationMenu = this.menuService.getCurrentMenuType() === 'operation';
        let tagRouterLink = mainMenu.routerLink;

        // 如果是运维管理菜单，需要处理路由前缀
        if (isOperationMenu && !tagRouterLink.startsWith('system/')) {
            // 对于运维管理菜单，保持原有的routerLink格式
            tagRouterLink = mainMenu.routerLink;
        }

        // 创建标签数据
        let data = {
            name: mainMenu.name,
            routerLink: tagRouterLink,
            title: mainMenu.title,
            active: 'active'
        };

        // 发送标签数据到导航组件
        this.obsService.refreshData(data);
    }

    updateNavTagsForSubMenu(mainMenu: any, subMenu: any, dynamicId: string | null = null) {
        // 检查是否是k8s菜单，如果是则不添加标签
        const isK8sMenu = this.menuService.getCurrentMenuType() === 'k8s';
        if (isK8sMenu) {
            // k8s菜单不添加标签，但仍需要设置当前菜单和active状态
            if (!subMenu.active) {
                subMenu.active = 'active';
                this.cdr.detectChanges();
            }
            this.currentMenu = mainMenu;
            this.setCustomSubMenu(mainMenu.name);
            return;
        }

        let tagRouterLink = subMenu.routerLink;
        const baseRoutePath = mainMenu.routerLink;
        const isOperationMenu = this.menuService.getCurrentMenuType() === 'operation';

        if (baseRoutePath.includes('k8s-detail') && dynamicId) {
            tagRouterLink = `${baseRoutePath}/${dynamicId}/${subMenu.routerLink}`;
        } else if (isOperationMenu) {
            // 对于运维管理菜单，需要特殊处理路径拼接
            if (baseRoutePath.endsWith(`/${subMenu.routerLink}`)) {
                // 如果主菜单路径已经包含子菜单路径，直接使用主菜单路径
                tagRouterLink = baseRoutePath;
            } else if (baseRoutePath.startsWith('system/') && !subMenu.routerLink.startsWith('system/')) {
                // 对于运维管理菜单，子菜单路径直接拼接到 system/ 后面
                tagRouterLink = `system/${subMenu.routerLink}`;
            } else {
                // 否则正常拼接
                tagRouterLink = `${baseRoutePath}/${subMenu.routerLink}`;
            }
        } else {
            tagRouterLink = `${baseRoutePath}/${subMenu.routerLink}`;
        }

        // 确保active状态被正确传递
        if (!subMenu.active) {
            subMenu.active = 'active';
            // 手动触发变更检测
            this.cdr.detectChanges();
        }

        let data = { name: subMenu.name, routerLink: tagRouterLink, title: subMenu.title, active: subMenu.active };
        this.obsService.refreshData(data);

        this.currentMenu = mainMenu;
        this.setCustomSubMenu(mainMenu.name);
    }

    ngOnDestroy() {
        while(subList.length) {
            const sub = subList.shift();
            if(sub && typeof sub.unsubscribe === 'function') {
                sub.unsubscribe();
            }
        }
    }

    @Output() toggleMenu = new EventEmitter<boolean>();
    toggle() {
        this.toggleMainMenu();
    }

    findFullImage = true;
    onFullImageError($event: ErrorEvent) {
        this.findFullImage = false;
        const imgElement = $event.target as HTMLImageElement;
        imgElement.src = `./assets/images/logo/default.png`;
    }

    onMiniImageError($event: ErrorEvent) {
        const imgElement = $event.target as HTMLImageElement;
        if(this.findFullImage){
            imgElement.src = `/cloud/logo/full-logo.png`;
        }else{
            imgElement.src = `./assets/images/logo/default.png`;
        }
    }

    generateRouterLink(routerLink: string): any[] {
        const prefix = '/console'; // Define the prefix

        if (!routerLink) {
            return [prefix]; // Handle empty routerLink case
        }

        if (routerLink.includes('/:id')) {
            const pathWithoutId = routerLink.split('/:id')[0]; // e.g., 'container/k8s-detail'
            const pathSegments = pathWithoutId.split('/'); // e.g., ['container', 'k8s-detail']

            if (this.currentK8sId) {
                // Combine prefix, path segments, and the ID
                return [prefix, ...pathSegments, this.currentK8sId]; // e.g., ['/console', 'container', 'k8s-detail', '2']
            } else {
                // ID is missing
                console.warn(`Navigating to ${pathWithoutId} without a required ID.`);
                // Combine prefix and path segments
                return [prefix, ...pathSegments]; // e.g., ['/console', 'container', 'k8s-detail']
            }
        }

        // No /:id parameter, split the original link
        const pathSegments = routerLink.split('/'); // e.g., ['dashboard'] or ['app-system', 'users']
        // Combine prefix and path segments
        return [prefix, ...pathSegments]; // e.g., ['/console', 'dashboard']
    }

    navigateToConsole() {
        this.menuService.switchMenu('default');
        this.router.navigate(['/console/']);
        // this.router.navigate(['/index.html']);
    }

    // 处理点击运维管理菜单的事件
    navigateToOperation() {
        this.menuService.switchMenu('operation');
        this.router.navigate(['/console/system/']);
    }
}

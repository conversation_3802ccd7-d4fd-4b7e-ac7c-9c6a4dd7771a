/* tslint:disable */
import { Component, OnInit } from '@angular/core';
import * as URL from 'src/app/service/common/URL';
import {Router} from '@angular/router';
import * as echarts from 'echarts';
import { RequestService } from 'src/app/service/common/request/request.service';

declare let $;

@Component({
    selector: 'app-rds-dosage',
    templateUrl: './rds-dosage.component.html',
    styleUrls: ['./rds-dosage.component.less']
})
export class RdsDosageComponent implements OnInit {
    username = '';

    navUnfoldFlg = false;
    state = 'small';
    showSmallWindowNav = false;
    showSmallWindowPrompt = false;
    rdsId = window.location.search.split('=')[1];
    color: any[] = ['#BCACFF', '#C0DD8E', '#FF9933', '#FFDE33', '#009966', '#4E86FF', '#90D9FF', '#6CD9F1', '#B2B9BE'];
    // 基本信息
    basicInfor1 = [{
        title: '名称',
        text: '',
        change: true,
    }, {
        title: 'ID',
        text: '',
        change: false,
    }, {
        title: '数据库版本',
        text: '',
        change: false,
    }, {
        title: '状态',
        text: '',
        change: false,
    }];
    basicInfor2 = [{
        title: '地域/可用区',
        text: '广州/广州一区',
        change: false,
    }, {
        title: '创建时间',
        text: '2019.01.07 17:06:50',
        change: false,
    }, {
        title: '可维护时间段',
        text: '2:00-6:00',
        change: false,
    }, {
        title: '字符集',
        text: 'UTF8MB4',
        change: true,
    }];
    rdsNameValue = '';
    timeOptions: any[] = [{
        title: '最近1小时',
        value: 1 * 60
    } , {
        title: '最近6小时',
        value: 6 * 60
    } , {
        title: '最近24小时',
        value: 24 * 60
    } , {
        title: '最近7天',
        value: 7 * 24 * 60
    } , {
        title: '最近30天',
        value: 30 * 24 * 60
    }];
    selectedValue = this.timeOptions[0].value;
    nowTime = Number(new Date().getTime().toString());
    endTime = this.nowTime - 1 * 60 * 60000;
    progress = false;
    errorShowMsg = ''; // 错误信息

    constructor(
        private router: Router,
        private req: RequestService
    ) { }

    ngOnInit() {
        const self = this;
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/list/' + this.rdsId).then(res => {
            if (res.code === '200') {
                const data = res.data;
                // 基本信息
                self.rdsNameValue = data.serverName;
                self.basicInfor1[0].text = data.serverName;
                self.basicInfor1[1].text = data.id;
                self.basicInfor1[2].text = data.version ? data.version : 'null';
                self.basicInfor1[3].text = data.status === 0 ? '已关机' : '运行中';
                self.basicInfor2[3].text = data.characterSet;
            }
        });
        // 显示图表
        this.showCpuEcharts();
    }




    getdate(time) {
        const now = new Date(time),
            // y = now.getFullYear(),
            m = now.getMonth() + 1,
            d = now.getDate();
        return (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8);
    }
    splitDate(time) {
        const createTime1 = time.split('T')[0].split('-')[1]
            + '-' + time.split('T')[0].split('-')[2];
        const createTime2 = time.split('T')[1].split('.')[0];
        return (createTime1 + ' ' + createTime2);
    }
    // 选择时间段
    selectTime() {
        this.nowTime = Number(new Date().getTime().toString());
        this.endTime = this.nowTime - this.selectedValue * 60000; // 11位时间戳 一分钟 = 600
        this.showCpuEcharts();
    }
    // 显示弹出层图表
    showCpuEcharts() {
        // this.progress = false;
        const self = this;
        // $('#progressWindow').addClass('is-visible-window');
        const cpuCharts = echarts.init(document.getElementById('cpu-echarts-container') as HTMLDivElement);
        cpuCharts.showLoading();
        // const diskCharts = echarts.init(document.getElementById('disk-echarts-container'));
        // const iopsCharts = echarts.init(document.getElementById('iops-echarts-container'));
        const connectCharts = echarts.init(document.getElementById('connect-echarts-container') as HTMLDivElement);
        connectCharts.showLoading();
        // const networkCharts = echarts.init(document.getElementById('network-echarts-container'));
        const tpsCharts = echarts.init(document.getElementById('tps-echarts-container') as HTMLDivElement);
        tpsCharts.showLoading();
        const innoDBCacheCharts = echarts.init(document.getElementById('innoDB-cache-echarts-container') as HTMLDivElement);
        innoDBCacheCharts.showLoading();
        const innoDBIoCharts = echarts.init(document.getElementById('innoDB-io-echarts-container') as HTMLDivElement);
        innoDBIoCharts.showLoading();
        // const innoDBCacheTimeCharts = echarts.init(document.getElementById('innoDB-cache-time-echarts-container'));
        // const innoDBLogCharts = echarts.init(document.getElementById('innoDB-log-echarts-container'));
        const mysqlTempSheetCharts = echarts.init(document.getElementById('mysql-temp-sheet-container') as HTMLDivElement);
        mysqlTempSheetCharts.showLoading();
        const mysqlCOMDMLCharts = echarts.init(document.getElementById('mysql-COMDML-container') as HTMLDivElement);
        mysqlCOMDMLCharts.showLoading();
        const mysqlRowDMLCharts = echarts.init(document.getElementById('mysql-RowDML-container') as HTMLDivElement);
        mysqlRowDMLCharts.showLoading();
        const myISAMTimeCharts = echarts.init(document.getElementById('myISAM-time-container') as HTMLDivElement);
        myISAMTimeCharts.showLoading();
        const myISAMKeyBufferCharts = echarts.init(document.getElementById('myISAM-key-buffer-container') as HTMLDivElement);
        myISAMKeyBufferCharts.showLoading();
        // cpu、内存
        const cpuOption = {
            color: this.color,
            title: {
                text: 'CPU和内存使用率',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            // 横坐标值
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            // 纵坐标值
            yAxis: {
                name: '%',
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
                type: 'value',
                splitLine: {
                    show: false
                },
            },
            tooltip: {
                trigger: 'axis'
            },
            // 图例
            legend: {
                data: ['CPU使用率', '内存使用率', '磁盘使用率'],
                bottom: '1%',
            },
            grid: {
                left: '12%'
            },
            series: [{
                name: 'CPU使用率',
                data: [],
                type: 'line'
            }, {
                name: '内存使用率',
                data: [],
                type: 'line'
            }, {
                name: '磁盘使用率',
                data: [],
                type: 'line'
            }]
        };
        // 获取CPU等使用率
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/vm', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // cpuOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    cpuOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    cpuOption.series[0].data.push(data[i].cpuValue);
                    cpuOption.series[1].data.push(data[i].memValue);
                    cpuOption.series[2].data.push(data[i].diskValue);
                }
                cpuCharts.hideLoading();
                cpuCharts.setOption(cpuOption as any);
            }
        });
        // 连接数
        const connectOption = {
            color: this.color,
            title: {
                text: '连接数',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['总连接数', '活跃连接数'],
                bottom: '1%'
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '个',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            series: [
                {
                    name: '总连接数',
                    type: 'line',
                    data: []
                },
                {
                    name: '活跃连接数',
                    type: 'line',
                    data: []
                }]
        };
        // 获取总连接数
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/connection/', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // connectOption.xAxis.data.push(self.splitDate(data[i].createdDate));
                    connectOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    // 总连接数
                    connectOption.series[0].data.push(data[i].totalConnections);
                    // 活跃连接数
                    connectOption.series[1].data.push(data[i].runningConnections);
                }
                connectCharts.hideLoading();
                connectCharts.setOption(connectOption as any);
            }
        });
        // tps和qps
        const tpsOption = {
            color: this.color,
            title: {
                text: 'tps和qps',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['平均每秒SQL执行次数', '平均每秒事务数'],
                bottom: '1%'
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '次',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [
                {
                    name: '平均每秒SQL执行次数',
                    type: 'line',
                    data: []
                },
                {
                    name: '平均每秒事务数',
                    type: 'line',
                    data: []
                }]
        };
        // 获取qps
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/ps/qps', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // tpsOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    tpsOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    tpsOption.series[0].data.push(data[i].qps);
                }
                tpsCharts.setOption(tpsOption as any);
            }
        });
        // 获取tps
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/ps/tps', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    tpsOption.series[1].data.push(data[i].tps);
                }
                tpsCharts.hideLoading();
                tpsCharts.setOption(tpsOption as any);
            }
        });
        // InnodDB缓存
        const innoDBCacheOption = {
            color: this.color,
            title: {
                text: 'InnoDB缓存',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['innodb缓存脏块率', 'innodb缓存读取命中率', 'innodb缓存读取利用率'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [
                {
                    name: 'innodb缓存脏块率',
                    type: 'line',
                    data: []
                },
                {
                    name: 'innodb缓存读取命中率',
                    type: 'line',
                    data: []
                },
                {
                    name: 'innodb缓存读取利用率',
                    type: 'line',
                    data: []
                }]
        };
        // 获取InnodDB缓存脏块率
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/buffer/dirty_block_rate', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // innoDBCacheOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    innoDBCacheOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    innoDBCacheOption.series[0].data.push(data[i].rate);
                }
                innoDBCacheCharts.setOption(innoDBCacheOption as any);
            }
        });
        // 获取InnodDB命中率
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/buffer/hit', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    innoDBCacheOption.series[1].data.push(data[i].rate);
                }
                innoDBCacheCharts.setOption(innoDBCacheOption as any);
            }
        });
        // 获取InnodDB读取利用率
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/buffer/read_use_rate', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    innoDBCacheOption.series[2].data.push(data[i].rate);
                }
                innoDBCacheCharts.hideLoading();
                innoDBCacheCharts.setOption(innoDBCacheOption as any);
            }
        });
        // InnodDB读写量
        const innoDBIoOption = {
            color: this.color,
            title: {
                text: 'InnoDB读写量',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['平均每秒innodb数据读取数据量', '平均每秒innodb数据写入数据量'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '个',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [
                {
                    name: '平均每秒innodb数据读取数据量',
                    type: 'line',
                    data: []
                },
                {
                    name: '平均每秒innodb数据写入数据量',
                    type: 'line',
                    data: []
                }]
        };
        // 获取读写量
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/read_write', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // innoDBIoOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    innoDBIoOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    innoDBIoOption.series[0].data.push(data[i].readNum);
                    innoDBIoOption.series[1].data.push(data[i].writeNum);
                }
                innoDBIoCharts.hideLoading();
                innoDBIoCharts.setOption(innoDBIoOption as any);
            }
        });
        // MySQL临时表数量
        const mysqlTempSheetOption = {
            color: this.color,
            title: {
                text: 'MySQL临时表数量',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['执行语句时硬盘上自动创建的临时表数量', '执行语句时内存上自动创建的临时表数量'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '个',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [{
                name: '执行语句时硬盘上自动创建的临时表数量',
                type: 'line',
                data: []
            } , {
                name: '执行语句时内存上自动创建的临时表数量',
                type: 'line',
                data: []
            }]
        };
        // 获取磁盘MySQL临时表数量
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/temp_table', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // mysqlTempSheetOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    mysqlTempSheetOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    mysqlTempSheetOption.series[0].data.push(data[i].diskNum);
                    mysqlTempSheetOption.series[1].data.push(data[i].memNum);
                }
                mysqlTempSheetCharts.hideLoading();
                mysqlTempSheetCharts.setOption(mysqlTempSheetOption as any);
            }
        });
        // MySQL_COMDML
        const mysqlCOMDMLOption = {
            color: this.color,
            title: {
                text: 'MySQL_COMDML',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['inset', 'delete', 'update', 'select'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '次',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [
                {
                    name: 'inset',
                    type: 'line',
                    data: []
                },
                {
                    name: 'delete',
                    type: 'line',
                    data: []
                },
                {
                    name: 'update',
                    type: 'line',
                    data: []
                },
                {
                    name: 'select',
                    type: 'line',
                    data: []
                }]
        };
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/com_dml', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // mysqlCOMDMLOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    mysqlCOMDMLOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    mysqlCOMDMLOption.series[0].data.push(data[i].insertNum);
                    mysqlCOMDMLOption.series[1].data.push(data[i].deleteNum);
                    mysqlCOMDMLOption.series[2].data.push(data[i].updateNum);
                    mysqlCOMDMLOption.series[3].data.push(data[i].selectNum);
                }
                mysqlCOMDMLCharts.hideLoading();
                mysqlCOMDMLCharts.setOption(mysqlCOMDMLOption as any);
            }
        });
        // MySQL_RowDML
        const mysqlRowDMLOption = {
            color: this.color,
            title: {
                text: 'MySQL_RowDML',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['db_rows_inserted', 'db_row_deleted', 'db_rows_updated', 'db_log_select'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '次',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [
                {
                    name: 'db_rows_inserted',
                    type: 'line',
                    data: []
                },
                {
                    name: 'db_row_deleted',
                    type: 'line',
                    data: []
                },
                {
                    name: 'db_rows_updated',
                    type: 'line',
                    data: []
                },
                {
                    name: 'db_log_select',
                    type: 'line',
                    data: []
                }]
        };
        // 获取MySQL_RowDML
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/row_dml', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // mysqlRowDMLOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    mysqlRowDMLOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    mysqlRowDMLOption.series[0].data.push(data[i].insertnNum);
                    mysqlRowDMLOption.series[1].data.push(data[i].deleteNum);
                    mysqlRowDMLOption.series[2].data.push(data[i].updateNum);
                    mysqlRowDMLOption.series[3].data.push(data[i].selectNum);
                }
                mysqlRowDMLCharts.hideLoading();
                mysqlRowDMLCharts.setOption(mysqlRowDMLOption as any);
            }
        });
        // MyISAM读写次数
        const myISAMTimeOption = {
            color: this.color,
            title: {
                text: 'MyISAM读写次数',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['缓存池中读取的次数', '缓存池中写入的次数', '硬盘上读取的次数', '硬盘上写入的次数'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            grid: {
                left: '12%'
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '次',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [{
                name: '缓存池中读取的次数',
                type: 'line',
                data: []
            },
            {
                name: '缓存池中写入的次数',
                type: 'line',
                data: []
            },
            {
                name: '硬盘上读取的次数',
                type: 'line',
                data: []
            },
            {
                name: '硬盘上写入的次数',
                type: 'line',
                data: []
            }]
        };
        // 获取MyISAM读写次数
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/myisam/read_times', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                // self.progress = true;
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // myISAMTimeOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    myISAMTimeOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    myISAMTimeOption.series[0].data.push(data[i].num0);
                    myISAMTimeOption.series[1].data.push(data[i].num1);
                    myISAMTimeOption.series[2].data.push(data[i].num2);
                    myISAMTimeOption.series[3].data.push(data[i].num3);
                }
                myISAMTimeCharts.hideLoading();
                myISAMTimeCharts.setOption(myISAMTimeOption as any);
            }
        });
        // MyISAM Key Buffer
        const myISAMKeyBufferOption = {
            color: this.color,
            title: {
                text: 'MyISAM Key Buffer',
                textStyle: {
                    fontSize: 14,
                },
                left: 'center',
                top: '2%',
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['MyISAM平均每秒key buffer读命中率', 'MyISAM平均每秒key buffer写命中率'],
                bottom: '1%',
                itemGap: 3,
                padding: 1,
                textStyle: {
                    fontSize: 10,
                },
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                },
            },
            yAxis: {
                name: '次',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    },
                }
            },
            series: [{
                name: 'MyISAM平均每秒key buffer读命中率',
                type: 'line',
                data: []
            }, {
                name: 'MyISAM平均每秒key buffer写命中率',
                type: 'line',
                data: []
            }]
        };
        // 获取MyISAM读写次数
        this.req.get(URL.CLOUD_SERVER_RDS_URL + '/rds/' + this.rdsId + '/myisam/key_buffer', {
            startTime: new Date(+this.endTime + 8 * 3600 * 1000).toISOString(),
            endTime: new Date(+this.nowTime + 8 * 3600 * 1000).toISOString(),
        }).then(res => {
            if (res.code === '200') {
                // self.progress = true;
                const data = res.data;
                const length = data.length;
                for (let i = 0; i < length; i++) {
                    // myISAMTimeOption.xAxis.data.push(self.splitDate(data[i].createdDate);
                    myISAMKeyBufferOption.xAxis.data.push(self.getdate(data[i].createdDate));
                    myISAMKeyBufferOption.series[0].data.push(data[i].readRate);
                    myISAMKeyBufferOption.series[1].data.push(data[i].writeRate);
                }
                myISAMKeyBufferCharts.hideLoading();
                myISAMKeyBufferCharts.setOption(myISAMKeyBufferOption as any);
            }
        });
    }

}


// 表单编辑器样式
.form-editor-container {
  height: calc(100vh - 20px);
  min-height: 600px;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;

  // 顶部工具栏
  .form-toolbar {
    height: 56px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 8px;
        color: #666;
        
        &:hover {
          color: #1890ff;
        }
      }

      .divider {
        margin: 0 12px;
        color: #d9d9d9;
      }

      .form-title-editor {
        display: flex;
        align-items: center;

        .form-title-label {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          margin-right: 8px;
          white-space: nowrap;
        }

        .form-title-input {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          border: 1px solid transparent;
          background: transparent;
          padding: 4px 8px;
          border-radius: 4px;
          min-width: 200px;
          max-width: 300px;
          transition: all 0.3s;

          &:hover {
            border-color: #d9d9d9;
            background: #fafafa;
          }

          &:focus {
            border-color: #1890ff;
            background: #fff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
          }

          &::placeholder {
            color: #bfbfbf;
            font-weight: normal;
          }
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  // 编辑器主体
  .form-editor-main {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 500px;

    &.loading {
      pointer-events: none;
    }

    // form-js编辑器内容区域
    .form-editor-content {
      width: 100%;
      height: 100%;
      position: relative;

      // form-js编辑器全局样式覆盖
      :global(.fjs-container) {
        background: #fff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Microsoft YaHei', '微软雅黑', sans-serif;
        height: 100%;
        display: flex;
      }

      // 确保中文字体正确显示
      :global(.fjs-palette-entry),
      :global(.fjs-properties-panel),
      :global(.fjs-form) {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Microsoft YaHei', '微软雅黑', sans-serif;
      }

      // form-js汉化样式
      :global(.fjs-palette-entry),
      :global(.fjs-properties-panel-header),
      :global(.fjs-form-field-label) {
        transition: all 0.2s ease;
      }

      // 使用CSS强制覆盖一些固定文本
      :global(.fjs-palette-group-header) {
        font-weight: 600;
        font-size: 12px;
        text-transform: none !important;
      }

      // 确保属性面板文本正确显示
      :global(.bio-properties-panel-group-header),
      :global(.fjs-properties-panel-group-header) {
        font-weight: 600;
        font-size: 13px;
        color: #666;
        text-transform: none !important;
      }

      // 确保标签文本正确显示
      :global(.bio-properties-panel-entry label),
      :global(.fjs-properties-panel-entry label) {
        font-size: 12px;
        color: #333;
        font-weight: normal;
      }

      // 修复属性面板中的缩进问题
      :global(.bio-properties-panel-group),
      :global(.fjs-properties-panel-group) {
        .bio-properties-panel-entry,
        .fjs-properties-panel-entry {
          padding-left: 0 !important;
          margin-left: 0 !important;
        }
      }

      // 确保选项数据源等标签的正确缩进
      :global(.bio-properties-panel-entry),
      :global(.fjs-properties-panel-entry) {
        padding: 8px 0 !important;

        label {
          padding-left: 0 !important;
          margin-left: 0 !important;
          text-indent: 0 !important;
        }
      }

      // 修复约束部分的缩进
      :global(.fjs-properties-panel-group-entries) {
        padding: 16px !important;

        .bio-properties-panel-entry,
        .fjs-properties-panel-entry {
          padding-left: 0 !important;
          margin-left: 0 !important;

          &:first-child {
            margin-top: 0 !important;
          }
        }
      }

      // 确保所有属性面板条目的一致缩进
      :global(.fjs-properties-panel) {
        .fjs-properties-group {
          .fjs-properties-group-entries {
            > * {
              padding-left: 0 !important;
              margin-left: 0 !important;
            }
          }
        }
      }

      // 左侧工具面板样式
      :global(.fjs-palette-container) {
        width: 280px;
        background: #f8f9fa;
        border-right: 1px solid #e8e8e8;
        flex-shrink: 0;
      }

      :global(.fjs-palette) {
        background: transparent;
        border: none;
        box-shadow: none;
        padding: 16px;
      }

      :global(.fjs-palette-group) {
        margin-bottom: 16px;

        .fjs-palette-group-header {
          font-size: 12px;
          font-weight: 600;
          color: #666;
          text-transform: uppercase;
          margin-bottom: 8px;
        }
      }

      :global(.fjs-palette-entry) {
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin-bottom: 8px;
        padding: 8px 12px;
        cursor: grab;
        transition: all 0.2s;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
        }

        &:active {
          cursor: grabbing;
        }
      }

      // 中间画布区域样式
      :global(.fjs-form-container) {
        flex: 1;
        background: #f5f5f5;
        overflow: auto;
        min-width: 400px;
      }

      :global(.fjs-form) {
        padding: 24px;
        max-width: none;
        background: #fff;
        margin: 16px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      :global(.fjs-form-field) {
        margin-bottom: 16px;
      }

      :global(.fjs-drag-container) {
        min-height: 200px;
        border: 2px dashed #d9d9d9;
        border-radius: 4px;
        background: #fafafa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 14px;

        &.fjs-drag-over {
          border-color: #1890ff;
          background: #f0f8ff;
          color: #1890ff;
        }
      }

      // 隐藏BPMN.IO水印
      :global(.fjs-powered-by),
      :global(.bjs-powered-by),
      :global(.djs-powered-by),
      :global([class*="powered-by"]) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
      }

      // 右侧属性面板样式
      :global(.fjs-properties-panel-container) {
        width: 320px;
        background: #f8f9fa;
        border-left: 1px solid #e8e8e8;
        flex-shrink: 0;
      }

      :global(.fjs-properties-panel) {
        background: transparent;
        border: none;
        box-shadow: none;
        padding: 16px;
      }

      :global(.fjs-properties-group) {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        margin-bottom: 16px;
        overflow: hidden;

        .fjs-properties-group-header {
          background: #fafafa;
          padding: 12px 16px;
          border-bottom: 1px solid #e8e8e8;
          font-weight: 500;
          font-size: 13px;
          color: #262626;
        }

        .fjs-properties-group-entries {
          padding: 16px;
        }
      }

      :global(.fjs-properties-entry) {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .fjs-properties-entry-label {
          font-size: 12px;
          font-weight: 500;
          color: #666;
          margin-bottom: 4px;
          display: block;
        }

        .fjs-properties-entry-input {
          width: 100%;
          padding: 6px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          font-size: 13px;

          &:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
          }
        }
      }
    }

    // 加载遮罩
    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .form-editor-container {
    .form-editor-main {
      .form-editor-layout {
        .palette-panel {
          width: 240px;
        }

        .properties-panel {
          width: 280px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .form-editor-container {
    .form-editor-main {
      .form-editor-layout {
        flex-direction: column;

        .palette-panel,
        .properties-panel {
          width: 100%;
          height: 200px;
        }

        .canvas-panel {
          min-height: 400px;
        }
      }
    }
  }
}

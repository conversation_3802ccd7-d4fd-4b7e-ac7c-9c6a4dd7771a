<!--banner图-->
<div class="intro">
    <app-intro-banner [intro]="intro"></app-intro-banner>
    <!--锚点导航-->
    <div class="nav-choose">
        <div class="grid-1200">
            <a href="javascript:void(0)" id="box1" (click)="clickbox1()">产品优势</a>
            <a href="javascript:void(0)" id="box2" (click)="clickbox2()">应用场景</a>
        </div>
    </div>
    <!--产品优势-->
    <div class="advantage-box" id="advantage">
        <div class="grid-1200">
            <h2 class="advantage-title">产品优势</h2>
            <div nz-row nzGutter="30">
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy pic-one"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">私有化</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>独享集群，拥有该镜像仓库最高管理权限</p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a href="javascript:void(0)" class="lazy pic-two"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">一键部署</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>省去集群搭建的烦恼，用户无需手动运维，全程自动化部署，开箱即用
                            </p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy pic-three"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">监控全面</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>Kubernetes内置监控组件，实现对节点、应用的多维度监控，并及时告警
                            </p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a href="javascript:void(0)" class="lazy pic-four"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">秒级启动</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>结合私有镜像仓库，可实现用户容器化应用快速部署</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--应用场景-->
    <div class="scene-box-black" id="scene">
        <div class="grid-1200">
            <h2 class="scene-title">应用场景</h2>
            <div nz-row nzGutter="30">
                <div nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)" class="scene-icon lazy scene-one"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">微服务架构</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>微服务架构将功能分解到各个离散的容器服务中以实现对解决方案的解耦，大幅提高系统功能扩展性，Kubernetes平台对每个容器服务进行全生命周期管理，保证服务可用
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)" class="scene-icon lazy scene-two"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">企业镜像存储</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>企业用户可根据部门或团队情况划分合适的镜像仓库，并根据业务或者人员分配相应权限，存取企业业务相关镜像，并无需关注集群运维情况，降低维护成本
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)" class="scene-icon lazy scene-three"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">应用托管</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>利用Kubernetes对于容器的全生命周期管理特性，在用户应用故障的情况下自动重启应用，无需人为干预。同时支持用户无状态应用多副本部署，保证应用高可用
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clear"></div>
<!--免费试用-->
<div class="try-box" id="try">
    <div class="grid-1200">
        <h2 class="advantage-title">免费试用</h2>
        <div class="maintitle-tip">
            <p>所有用户可免费试用AIC应用托管服务的全栈服务（试用时有资源限制）</p>
        </div>
    </div>
</div>

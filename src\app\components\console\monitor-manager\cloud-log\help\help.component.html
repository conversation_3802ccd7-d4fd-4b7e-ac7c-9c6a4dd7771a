<!-- 安装准备  -->
<div *ngIf="agebt" style="margin: 0 auto;">
    <p style="text-align: left;"><span style="font-size: 28px; font-family: 微软雅黑, Microsoft YaHei;">安装准备</span></p>
    <p style="text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">获取软件包和工具</span></p>
    <p style="margin-left: 28px; text-align: left;"><span style="font-size: 14px;"><span style="font-size: 14px; font-family: Wingdings;">l
        <span>&emsp;&emsp;</span></span><strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">软件包列表</span></strong></span></p>
    <table cellspacing="0" cellpadding="0" width="635">
        <thead>
            <tr style="height:40px" class="firstRow">
                <td width="96" valign="top" style="border: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">操作系统</span></p>
                </td>
                <td width="94" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">系统位数</span></p>
                </td>
                <td width="104" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">工具版本</span></p>
                </td>
                <td width="104" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">文件名称</span></p>
                </td>
                <td width="104" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">作用</span></p>
                </td>
                <td width="132" valign="top" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="40">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">下载链接</span></p>
                </td>
            </tr>
            <tr style="height:21px">
                <td width="96" nowrap="" valign="top" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">Linux(CentOS、BC-Linux)</span></p>
                </td>
                <td width="94" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">32/64</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">6.2.2及以上</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">filebeat</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">日志采集</span></p>
                </td>
                <td width="132" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">在线<a
                             href="https://www.elastic.co/cn/downloads/past-releases/filebeat-6-2-2" target="_blank" style="word-break: break-word;">下载</a></span></p>
                </td>
            </tr>
            <tr style="height:21px">
                <td width="96" nowrap="" valign="top" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">Linux(Ubuntu、Debian)</span></p>
                </td>
                <td width="94" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">32/64</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">6.2.2及以上</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">filebeat</span></p>
                </td>
                <td width="104" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">日志采集</span></p>
                </td>
                <td width="132" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="21">
                    <p style="margin: 11px 0px; text-align: left;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">在线<a
                             href="https://www.elastic.co/cn/downloads/past-releases/filebeat-6-2-2" target="_blank" style="word-break: break-word;">下载</a></span></p>
                </td>
            </tr>
        </thead>
    </table>
    <p style="margin-left: 28px; text-align: left;"><span style="font-size: 14px;"><span style="font-size: 14px; font-family: Wingdings;">l
        <span>&emsp;&emsp;</span></span><strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">工具列表</span></strong></span></p>
    <table cellspacing="0" cellpadding="0" width="633">
        <thead>
            <tr style="height:28px" class="firstRow">
                <td width="245" style="border: 1px solid windowtext; background: rgb(217, 217, 217); padding: 0px 7px;" height="28">
                    <p style="text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">工具</span></p>
                </td>
                <td width="388" style="border-top: 1px solid windowtext; border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-image: initial; border-left: none; background: rgb(217, 217, 217); padding: 0px 7px;"
                 height="28">
                    <p style="text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">工具用途</span></p>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr style="height:28px">
                <td width="245" valign="top" style="border-right: 1px solid windowtext; border-bottom: 1px solid windowtext; border-left: 1px solid windowtext; border-image: initial; border-top: none; padding: 0px 7px;"
                 height="28">
                    <p style="text-align: left;"><span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">PuTTY</span></p>
                </td>
                <td width="388" valign="top" style="border-top: none; border-left: none; border-bottom: 1px solid windowtext; border-right: 1px solid windowtext; padding: 0px 7px;"
                 height="28">
                    <p style="text-align: left;"><span style="font-family: 微软雅黑, sans-serif; color: black; font-size: 14px;">跨平台远程访问工具。用于在软件安装过程中在windowsTool操作系统上访问各节点。</span></p>
                </td>
            </tr>
        </tbody>
    </table>
    <p><br></p>
</div>

<!-- 安装Linux版工具 -->
<div *ngIf="linuxTool">
    <p style="text-align: left;">
		    
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <strong><span style="font-family:微软雅黑,sans-serif">前提说明</span></strong>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">请确保本次安装为首次安装，否则，可以按照首次步骤进行操作（如果不确定主机上是否安装或者，也可在此步骤中进行检测）。</span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">保证可以获取对应主机的root用户权限。</span>
    </p>
    <h5 style="text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 16px;">首次操作步骤</span>
    </h5>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-size: 14px;"><em><span style="font-family: 微软雅黑, sans-serif;">以下步骤以安装Centos 7.3 64位的filebeat 7.4.0为示例。</span></em></span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 1</span><span style="font-size: 14px;"><span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">将安装包传至”/root/”目录下</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573700374236088865.png" title="201911141573700374236088865.png" alt="15.png" width="100%">
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 2<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal;">&nbsp;&nbsp;</span></span><span style="font-size: 14px;"><span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">在安装包所在目录下执行命令：rpm -ivh filebeat-7.4.0-x86_64.rpm（文件名以实际为准）</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573700379260067939.png" title="201911141573700379260067939.png" alt="16.png" width="100%">
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 3</span><span style="font-size: 14px;"><span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">修改配置文件/etc/filebeat/filebeat.yml（配置信息与平台页面生成的配置文件一致即可），命令：vi /etc/filebeat/filebeat.yml</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573700396168040478.png" title="201911141573700396168040478.png" alt="17.png" width="100%">
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑,Microsoft YaHei;">步骤 4</span><span style="font-size: 14px;"><span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">设置开机自启动：systemctl enable filebeat</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573700405915007627.png" title="201911141573700405915007627.png" alt="18.png" width="100%">
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 启动filebeat服务：systemctl restart filebeat</span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 6&nbsp;&nbsp;</span><span style="font-size: 14px;"><span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">查看filebeat服务状态：systemctl status filebeat</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573700418078086952.png" title="201911141573700418078086952.png" alt="19.png" width="100%">
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">如果操作至与步骤6状态一致即完成首次安装。</span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"></span>
    </p>
    <p style="margin: 11px 0px 11px 28px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-family: Wingdings;">l<span style="font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; line-height: normal; font-family: Times New Roman;">&nbsp; </span></span><strong><span style="font-family: 微软雅黑, sans-serif;">非centos系统安装步骤</span></strong></span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">如果操作系统为Ubuntu或者Debian，则安装<strong>步骤二</strong>略有不同。</span>
    </p>
    <p style="margin: 11px 0px 11px 67px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;"><span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>步骤 1<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">将安装包传至”/root/”目录下</span></span>
    </p>
    <p style="margin-top:11px;margin-right:0;margin-bottom:11px;margin-left:47px;text-indent:0;line-height:normal">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">&nbsp;</span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 2<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">在安装包所在目录下执行命令：dpkg -i filebeat-6.2.2-amd64.deb（文件名以实际为准）</span></span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/202001101578640777253009859.png" title="202001101578640777253009859.png" alt="7.png" width="100%">
    </p><p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"></span><br>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 3<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">修改配置文件/etc/filebeat/filebeat.yml（配置信息与平台页面生成的配置文件一致即可），命令：vi /etc/filebeat/filebeat.yml</span></span>
    </p>
    <p style="margin: 11px 0px 11px 37px; text-indent: 14px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, sans-serif; color: rgb(23, 43, 77); background: rgb(244, 245, 247);">Linux路径样例：/var/log/messages</span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><img src="../../../../../assets/images/202001101578640786703090609.png" title="202001101578640786703090609.png" alt="8.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 4<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">设置开机自启动：systemctl enable filebeat</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><img src="../../../../../assets/images/202001101578640798257059905.png" title="202001101578640798257059905.png" alt="9.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 5<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">启动filebeat服务：systemctl restart filebeat</span></span>
    </p>
    <p style="margin: 11px 0px 11px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 6<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">查看filebeat服务状态：systemctl status filebeat</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><img src="../../../../../assets/images/202001101578640808518044340.png" title="202001101578640808518044340.png" alt="10.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;">如果操作至与步骤6状态一致即完成首次安装。</span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><br style="text-align: left;"></span>
    </p>
</div>

<!-- 安装windows版工具 -->
<div *ngIf="windowsTool">
    <h5 style="text-align: left;">
        <span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">前提说明</span>
    </h5>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">请根据需求，获取对应的filebeat版本，具体对应关系见安装准备。</span>
    </p>
    <h5 style="text-align: left;">
        <span style="font-size: 16px; font-family: 微软雅黑, Microsoft YaHei;">操作步骤</span>
    </h5>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><em><span style="font-family: 微软雅黑, sans-serif;">以下步骤以安装Windows 7 64位的winlogbeat-7.4.0为示例，安装Windows版filebeat类似。</span></em></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">步骤 1<span style="font-family: 微软雅黑, Microsoft YaHei; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>将安装包解压至” C:\Program Files”目录下，修改文件夹名为 “winlogbeat”</span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="../../../../../assets/images/201911141573700995039031052.png" title="201911141573700995039031052.png" alt="22.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 2&nbsp;<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">&nbsp;以管理员权限打开软件“powershell”，执行如下命令：</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;"><img src="../../../../../assets/images/201911141573701018030080704.png" title="201911141573701018030080704.png" alt="23.png" width="100%"></span></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="text-indent: 42px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">cd 'C:\Program Files\winlogbeat'</span>
    </p>
    <p style="text-align:center">
        <span style="text-indent: 42px; font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="../../../../../assets/images/201911141573701028720093676.png" title="201911141573701028720093676.png" alt="24.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">.\install-service-winlogbeat.ps1</span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><img src="../../../../../assets/images/201911141573701035372017580.png" title="201911141573701035372017580.png" alt="25.png" width="100%"></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 3&nbsp;&nbsp;<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">修改配置文件C:\Program Files\winlogbea\winlogbeat.yml（配置信息与平台页面生成的配置文件一致即可）</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;"><img src="../../../../../assets/images/201911141573701045706097541.png" title="201911141573701045706097541.png" alt="26.png" width="100%"></span></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 4&nbsp;&nbsp;<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">启动服务，在powershell里面输入：Start-Service winlogbeat</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;"><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;"><img src="../../../../../assets/images/201911141573701054883031941.png" title="201911141573701054883031941.png" alt="27.png" width="100%"></span></span>
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, Microsoft YaHei;">步骤 5<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 9px; line-height: normal; font-family: Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">检查服务，win + R后输入“services.msc”,查看名称为“winlogbeat”的状态</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573701062326013945.png" title="201911141573701062326013945.png" alt="28.png" width="100%">
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573701074848022309.png" title="201911141573701074848022309.png" alt="29.png" width="100%">
    </p>
    <p style="margin: 11px 0px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">如果操作至与步骤5状态一致即完成安装。</span>
    </p>
    <p>
        <br style="text-align: left;">
    </p>
</div>

<!-- 常见问题 -->
<div *ngIf="question">
    <p style="text-align: left;">
		    
    </p>
    <p style="text-align: left;">
        
    </p>
    <p style="text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong>问题1：执行.\install-service-winlogbeat.ps1 报错</strong></span>
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">现象：</span></strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">在执行</span></span><a target="_blank" style="text-decoration: underline; color: rgb(84, 141, 212); font-size: 14px; word-break: break-word;"><span style="color: rgb(84, 141, 212); font-size: 14px;"><strong><span (click)="windows()" style="text-decoration: none; color: black; font-size: 14px; font-family: 微软雅黑, sans-serif;">安装windowsTool版工具</span></strong></span></a><span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;">中步骤二时，如提示错误，如下图：</span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573701273815067600.png" title="201911141573701273815067600.png" alt="30.png" width="100%">
    </p>
    <p style="margin: 11px 0px; text-align: left;">
        <span style="font-family: 微软雅黑, Microsoft YaHei; font-size: 14px;"><strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">解决方式：</span></strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">输入命令：Set-ExecutionPolicy -ExecutionPolicy RemoteSigned ，并输入“Y”</span></span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/201911141573701280264024312.png" title="201911141573701280264024312.png" alt="31.png" width="100%">
    </p>
    <p style="text-align: left;">
        <span style="font-size: 14px;"><strong><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">问题2：是否有linux和windowsTool系统的采集规则样例？</span></strong></span>
    </p>
    <p style="text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, sans-serif; color: rgb(23, 43, 77); background: rgb(244, 245, 247);">Linux路径样例：/var/log/messages</span>
    </p>
    <p style="text-align: left;">
        <span style="font-size: 14px; font-family: 微软雅黑, sans-serif; color: rgb(23, 43, 77); background: rgb(244, 245, 247);">windowsTool是个配置过程，具体如下：</span>
    </p>
    <p style="margin: 10px 0px 10px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 1<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">打开控制面板</span></span>
    </p>
    <p style="margin: 10px 0px 10px 47px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span>
    </p>
    <p style="text-align:center">
        <img src="../../../../../assets/images/202001101578639428184062067.png" title="202001101578639428184062067.png" alt="3.png" width="100%">
    </p>
    <p style="margin: 10px 0px 10px 47px; text-indent: 0px; line-height: normal; text-align: left;">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"></span><br>
    </p>
    <p style="margin: 10px 0px 10px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 2<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">点击系统和安全，在【系统和安全】面板中找到【管理工具】，点击下面的【查看事件日志】，就可以打开【事件查看器】。</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><img src="../../../../../assets/images/202001101578639438249004709.png" title="202001101578639438249004709.png" alt="4.png" width="100%"></span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑, sans-serif; font-size: 14px;"><img src="../../../../../assets/images/202001101578639450791017410.png" title="202001101578639450791017410.png" alt="5.png" width="100%"></span>
    </p>
    <p style="margin: 10px 0px 10px 47px; line-height: normal; text-align: left;">
        <span style="font-size: 14px;"><span style="font-size: 14px; font-family: Book Antiqua, serif;">步骤 3<span style="font: 9px Times New Roman;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span><span style="font-size: 14px; font-family: 微软雅黑, sans-serif;">页面提供筛选、清楚等操作，windowsTool系统的日志便可在此查看。</span></span>
    </p>
    <p style="text-align:center">
        <span style="font-family: 微软雅黑,sans-serif"><img src="../../../../../assets/images/202001101578639464226067176.png" title="202001101578639464226067176.png" alt="6.png" width="100%"></span>
    </p>
    <p style="margin: 10px 0px;">
        <span style="font-family: 微软雅黑,sans-serif">&nbsp;</span>
    </p>
</div>
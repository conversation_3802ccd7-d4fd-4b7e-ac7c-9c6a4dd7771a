import { Component, OnInit, EventEmitter, Output, Input } from '@angular/core';

@Component({
    selector: 'app-number-input',
    templateUrl: './number-input.component.html',
    styleUrls: ['./number-input.component.less']
})
export class NumberInputComponent implements OnInit {
    constructor() { }

    @Input() value: number = 0;
    @Output() valueChange = new EventEmitter<number>();

    @Input() max: number = 100;
    @Input() min: number = 0;
    @Input() step: number = 1;
    @Input() allowZero: boolean = false; // 当最小值不为0时，是否允许输入0。
    @Input() allowInput: boolean = true; // 是否允许输入值
    @Input() showRange: boolean = true;  // 是否显示范围popover

    @Input() errorHint: string = '';
    @Output() errorHintChange = new EventEmitter<string>();

    num: number;

    ngOnInit() {
        this.num = this.value;
    }

    getRangeText() {
        return `${this.min} ~ ${this.max}`;
    }

    canIncrease() {
        let num = +this.num;
        return num < this.max;
    }

    increase() {
        if (!this.canIncrease()) {
            return;
        }

        let num = +this.num;
        let val;
        if (num + this.step > this.max) {
            val = this.max;
        } else {
            if(num < this.min) {
                val = this.min;
            } else {
                val = num + this.step;
            }
        }

        this.validateInput(val);
    }

    canDecrease() {
        let num = +this.num;
        return (num > 0 && num <= this.min && this.allowZero) || num > this.min;
    }


    decrease() {
        if (!this.canDecrease()) {
            return;
        }
        let num = +this.num;
        let val;
        if (num > this.max) {
            val = this.max;
        } else {
            if (num - this.step < this.min) {
                if (this.allowZero && num <= this.min) {
                    val = 0;
                } else {
                    val = this.min;
                }
            } else {
                val = num - this.step;
            }
        }
        
        this.validateInput(val);
    }

    keyControl(e: KeyboardEvent) {
        if (e.keyCode == 40) {
            e.preventDefault();
            // arrow down
            if (this.num <= 0) {
                // do nothing
            } else {
                this.decrease();
            }
        }

        if (e.keyCode == 38) {
            e.preventDefault();
            // arrow up
            if (this.num >= this.max) {
                // do nothing
            } else {
                this.increase();
            }
        }
    }

    validateInput(val) {
        let error = '';
        if (val === 0) {
            if (this.allowZero) {
                error = '';
            } else {
                error = `输入值超出范围：${this.min} - ${this.max}`
            }
        } else if (!/^\d+$/.test(String(val))) {
            error = '请输入有效的数字';
        } else if (+(val) > this.max || (+(val) < this.min)) {
            error = `输入值超出范围：${this.min} - ${this.max}`
        }
        
        this.num = val;
        this.errorHintChange.emit(error);
        this.valueChange.emit(val);
    }
}

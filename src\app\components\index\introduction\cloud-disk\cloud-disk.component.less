.advantage-list .advantage_picone {
    background-image:url(src/assets/images/lhbs-default.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .advantage_picone {
    background-image:url(src/assets/images/lhbs-hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .advantage_pictwo {
    background-image:url(src/assets/images/server-two.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .advantage_pictwo {
    background-image:url(src/assets/images/server-two_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .advantage_picthree {
    background-image:url(src/assets/images/gtx-default.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .advantage_picthree {
    background-image:url(src/assets/images/gtx-hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .advantage_picfour {
    background-image:url(src/assets/images/gyy-default.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .advantage_picfour {
    background-image:url(src/assets/images/gyy-hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}

.scene-box-black a:hover{
    text-decoration: none;
}
.scene-box-black .scene-list .white-line {
    height:3px;
    background-color:#fff;
    width:30px;
    margin:15px auto;
}
.scene-box-black .scene-list:hover .white-line {
    height:3px;
    background-color:#0083ff;
    width:30px;
    margin:15px auto;
}
.scene-box-black {
    background:url(src/assets/images/balance-scene-bg-gg.jpg) no-repeat;
    width:100%;
    height:620px;
    overflow:hidden;
}
.scene-box-black .scene-title {
    text-align:center;
    padding:80px 0 50px 0;
    color:#fff;
}
.scene-box-black .scene-list {
    height: 315px;
    display: block;
    line-height: 1.6;
    background-color: rgba(35,35,35,0.7);
    padding: 22px;
}
.scene-box-black .scene-sc:nth-of-type(2) .scene-list .scene-icon{
    display:block;
    text-align: center;
    padding-top: 20px;

}
.scene-box-black .scene-sc:nth-of-type(2) .scene-list .list-caption h3{
    margin-top: 18px;
}
.scene-box-black .scene-icon{
    display:block;
    text-align: center;
    padding-top: 37px;
}
.scene-box-black .scene-list .list-caption h3{
    margin-top: 18px;
}
.scene-box-black .scene-list .list-caption h3 a{
    color: #fff;
}
.scene-box-black .scene-list .list-caption .scene-txt{
    margin: 20px 20px 0px 20px;
}
.scene-box-black .scene-list .list-caption .scene-txt p{
    color: #aaaaaa;
    font-size: 14px;
}

.scene-box-black .scene-list .scene-disk-one{
    background-image:url(src/assets/images/scene-disk-one.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black .scene-list:hover .scene-disk-one{
    background-image:url(src/assets/images/scene-disk-one_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black .scene-list .scene-disk-two{
    background-image:url(src/assets/images/scene-disk-two.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black .scene-list:hover .scene-disk-two{
    background-image:url(src/assets/images/scene-disk-two_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black .scene-list .scene-disk-three{
    background-image:url(src/assets/images/scene-disk-three.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black .scene-list:hover .scene-disk-three{
    background-image:url(src/assets/images/scene-disk-three_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}

/*价格begin*/
.price-box {
    background:url(src/assets/images/price-bg-gg.jpg) no-repeat right bottom #fff;
    width: 100%;
    height:660px;
    /*overflow:hidden;*/
}
.price-table th,.price-table td{
    text-align:center;
    /*padding:30px 0;*/
    border:solid 1px #d7d8d9;
    font-size:16px;
    background-color:#fff;
    height: 60px;
    line-height: 60px;
}
.price-table th{
    background-color:#f6f6f6;
}

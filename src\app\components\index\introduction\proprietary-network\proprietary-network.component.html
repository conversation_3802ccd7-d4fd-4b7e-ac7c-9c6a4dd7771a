<!--banner图-->
<div class="intro">
    <app-intro-banner [intro]="intro"></app-intro-banner>
    <!--锚点导航-->
    <div class="nav-choose">
        <div class="grid-1200">
            <a href="javascript:void(0)" id="box1" (click)="clickbox1()">产品优势</a>
            <a href="javascript:void(0)" id="box2" (click)="clickbox2()">应用场景</a>
        </div>
    </div>
    <!--产品优势-->
    <div class="advantage-box" id="advantage">
        <div class="grid-1200">
            <h2 class="advantage-title">产品优势</h2>
            <div nz-row nzGutter="30">
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy private_picone"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">高可用</a></h3>
                            <div class="blue-line"></div>
                            <p>采用网络虚拟化的技术，通过链路冗余等方式，保证网络高可用。</p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy private_pictwo"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">安全隔离</a></h3>
                            <div class="blue-line"></div>
                            <p>VPC网络之间逻辑上完全隔离，租户间相互无影响，满足网络安全需求。</p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy private_picthree"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">自定义网络</a></h3>
                            <div class="blue-line"></div>
                            <p>自定义为专有网络VPC配置IP 地址范围、网段和网关等。</p>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="6">
                    <div class="advantage-list updown"><a href="javascript:void(0)" class="lazy private_picfour"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">弹性扩展</a></h3>
                            <div class="blue-line"></div>
                            <p>专有网络支持弹性扩展，无需采购、安装、调试等繁杂环节，让网络资源随着您的业务规模变化而灵活扩展，助力您业务规模增长。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--应用场景-->
    <div class="scene-box-network" id="scene">
        <div class="grid-1200">
            <h2 class="scene-title">应用场景</h2>
            <div nz-row nzGutter="30">
                <div nz-col nzSpan="12" class="scene-col scene-col-special">
                    <div class="scene-list">
                        <a href="javascript:void(0)" class="scene-icon"><img src="./assets/images/pro-scene-icon1.jpg"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">互联互通</a></h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>通过对等连接的方式实现多个云计算资源之间连接打通，实现相互之间的容灾部署。</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div nz-col nzSpan="12" class="scene-col">
                    <div class="scene-list">
                        <a href="javascript:void(0)" class="scene-icon"><img src="./assets/images/pro-scene-icon2.jpg"></a>
                        <div class="list-caption">
                            <h3><a href="javascript:void(0)">WEB服务</a></h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>基于AIC构建的云上专有网络环境，通过弹性负载均衡对外提供服务，支撑海量用户访问。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

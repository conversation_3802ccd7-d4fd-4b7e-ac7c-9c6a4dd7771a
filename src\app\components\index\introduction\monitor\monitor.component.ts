import { Component, OnInit } from '@angular/core';
declare let $: any;

@Component({
  selector: 'app-monitor',
  templateUrl: './monitor.component.html',
  styleUrls: ['./monitor.component.less']
})
export class MonitorComponent implements OnInit {

constructor() { }
intro = {
  title: '云监控',
  enName: 'AIC Monitor',
  desc: '获取云主机的监控指标，探测服务可用性，以及针对指标设置警报。实时掌握资源及应用的运行状态，保证服务及应用稳定运行。',
  bgColor: '#1a284d',
  orderLink: '/console/monitor/market-monitor',
  type: 'monitorProduction',
}

ngOnInit() {
}
// 锚点导航
clickbox1() {
  $('html, body').animate({
    scrollTop: $('#advantage').offset().top - 50
  }, {
    duration: 500, easing: 'swing'
  });
  return false;
}

clickbox2() {
  $('html, body').animate({
    scrollTop: $('#scene').offset().top - 50
  }, {
    duration: 500, easing: 'swing'
  });
  return false;
}

clickbox3() {
  $('html, body').animate({
    scrollTop: $('#price').offset().top - 50
  }, {
    duration: 500, easing: 'swing'
  });
  return false;
}

clickbox4() {
  $('html, body').animate({
    scrollTop: $('#try').offset().top - 50
  }, {
    duration: 500, easing: 'swing'
  });
  return false;
}

}

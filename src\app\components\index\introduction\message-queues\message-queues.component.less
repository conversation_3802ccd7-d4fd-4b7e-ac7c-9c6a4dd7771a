.scene-box-network .scene-col-special{
    border-right: 1px solid rgba(10,9,9,0.6);
}

.advantage-list .pic-one {
    background-image:url(src/assets/images/private-two.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .pic-one {
    background-image:url(src/assets/images/private-two_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .pic-two {
    background-image:url(src/assets/images/four.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .pic-two {
    background-image:url(src/assets/images/four-on.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .pic-three {
    background-image:url(src/assets/images/gyy-default.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .pic-three {
    background-image:url(src/assets/images/gyy-hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .pic-four {
    background-image:url(src/assets/images/sygx-default.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .pic-four {
    background-image:url(src/assets/images/sygx-hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-box-black a:hover{
    text-decoration: none;
}
.scene-box-black .scene-list .white-line {
    height:3px;
    background-color:#fff;
    width:30px;
    margin:15px auto;
}
.scene-box-black .scene-list:hover .white-line {
    height:3px;
    background-color:#0083ff;
    width:30px;
    margin:15px auto;
}
.scene-box-black {
    background:url(src/assets/images/balance-scene-bg-gg.jpg) no-repeat;
    width:100%;
    height:620px;
    overflow:hidden;
}
.scene-box-black .scene-title {
    text-align:center;
    padding:80px 0 50px 0;
    color:#fff;
}
.scene-box-black .scene-list {
    height: 315px;
    display: block;
    line-height: 1.6;
    background-color: rgba(35,35,35,0.7);
    padding: 22px;
}
.scene-box-black .scene-sc:nth-of-type(2) .scene-list .scene-icon{
    display:block;
    text-align: center;
    padding-top: 20px;

}
.scene-box-black .scene-sc:nth-of-type(2) .scene-list .list-caption h3{
    margin-top: 18px;
}
.scene-box-black .scene-icon{
    display:block;
    text-align: center;
    padding-top: 37px;
}
.scene-box-black .scene-list .list-caption h3{
    margin-top: 18px;
}
.scene-box-black .scene-list .list-caption h3 a{
    color: #fff;
}
.scene-box-black .scene-list .list-caption .scene-txt{
    margin: 20px 20px 0px 20px;
}
.scene-box-black .scene-list .list-caption .scene-txt p{
    color: #aaaaaa;
    font-size: 14px;
}
// 应用场景
.scene-box-network a:hover{
    text-decoration: none;
}
.scene-box-network .scene-list .white-line {
    height:3px;
    background-color:#fff;
    width:30px;
    margin:15px auto;
}
.scene-box-network .scene-list:hover .white-line {
    height:3px;
    background-color:#0083ff;
    width:30px;
    margin:15px auto;
}
.scene-box-network {
    background:url(src/assets/images/balance-scene-bg.jpg) no-repeat;
    width:100%;
    height:628px;
    overflow:hidden;
}
.scene-box-network .scene-title {
    text-align:center;
    padding:80px 0 50px 0;
    color:#fff;
}
.scene-box-network .scene-col{
    background-color: rgba(35,35,35,0.7);
}
.scene-box-network .scene-col:hover{
    background-color: rgba(10,9,9,0.6);
}
.scene-box-network .scene-list {
    height: 368px;
    display: block;
    line-height: 1.6;
}
.scene-box-network .scene-icon{
    display:block;
    text-align: center;
    padding-top: 37px;
}
.scene-box-network .scene-list .list-caption h3{
    margin-top: 40px;
}
.scene-box-network .scene-list .list-caption h3 a{
    color: #fff;
}
.scene-box-network .scene-list .list-caption .scene-txt{
    margin: 40px 20px 0px 20px;
}
.scene-box-network .scene-list .list-caption .scene-txt p{
    color: #aaaaaa;
    font-size: 14px;
}


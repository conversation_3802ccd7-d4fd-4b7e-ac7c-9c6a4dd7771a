import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-terms',
    templateUrl: './terms.component.html',
    styleUrls: ['./terms.component.less']
})
export class TermsComponent implements OnInit {
    constructor() {}

    @Input() isVisible:boolean = false;
    @Input() contentStyle:object;
    
    @Output() agree = new EventEmitter<boolean>();
    @Output() reject = new EventEmitter<boolean>();

    ngOnInit() {}

    handleCancel() {
        this.reject.emit(true);
    }

    handleOk() {
        this.agree.emit(true);
    }
}

.CS_box .CSbox-title{
    float: left;
}
.CS_box .CSbox-top{
    padding: 10px 30px;
}
.CS_box .CSbox-top .CS-choose-net{
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
}
.CSbox-top button{
    float: right;
}
.CS_box .CSbox-body-special-1{
    padding: 10px 30px;
}
.CS_box .CSbox-body-special-2{
    padding: 20px 30px 0 30px
}
.tc-content h6{
    font-size: 14px;
    font-weight: 600;
    margin-left: 50px;
}
.tc-content .select-special-1{
    margin-left: 40px;
    margin-top: 10px;
}
.tc-content .select-special-2{
    margin-left: 40px;
}
.tc-content .dian-special{
    float: left;
    width: 1.25%;
    text-align: center;
}



.choose-network{
    border-bottom: 2px solid #0083FF;
}
.CS-choose-net div{
    cursor: pointer;
    width: 80px;
    float: left;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    color: #232323;
}

.CSbox-body .create{
    float: left;
    height: 30px;
    line-height: 30px;
    color: #ffffff;
    padding: 0 20px;
    font-size: 14px;
    background-color: #0083ff;
    float: right;
    margin: 0 0 10px 0;
}
.CSbox-body .create:hover{
    background-color: #0060ff;
    box-shadow: 2px 2px 8px rgba(0,96,255,0.2);
}
.choose-network-show{
    border-top: 2px solid #0083FF;
    color: #0083FF;
    background-color: #ffffff;
    border-right: 1px solid #e8e8e8;
    border-left: 1px solid #e8e8e8;
}
.choose-network-hide{
    background-color: #f6f6f6;
    border: 1px solid #e8e8e8;
}
.CS-choose-network-hidden{
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
}
.CS-choose-network-hidden div{
    cursor: pointer;
    float: left;
    width: 80px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
}

button.disabled {
    pointer-events: none;
    opacity: 0.5;
}

.custom-table td{padding: 4px;}
.scene-box .scene-hang{
    margin-top: 30px;
}
.scene-box .line{
    width: 100%;
    height: 1px;
    background-color: #aaaaaa;
}
.scene-box .line-line{
    width: 100%;
    height: 1px;
}

.advantage-list .server_picone {
    background-image:url(src/assets/images/one.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .server_picone {
    background-image:url(src/assets/images/one-on.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .server_pictwo {
    background-image:url(src/assets/images/server-two.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .server_pictwo {
    background-image:url(src/assets/images/server-two_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .server_picthree {
    background-image:url(src/assets/images/three.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .server_picthree {
    background-image:url(src/assets/images/three-on.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list .server_picfour {
    background-image:url(src/assets/images/four.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.advantage-list:hover .server_picfour {
    background-image:url(src/assets/images/four-on.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}

/*应用场景begin*/
.scene-box {
    background:url(src/assets/images/scene-bg.jpg) no-repeat;
    background-size: 100% 810px;
    width:100%;
    height:810px;
    /*overflow:hidden;*/
}
.scene-box a:hover{
    text-decoration: none;
}
.scene-title {
    text-align:center;
    padding:80px 0 50px 0;
    color:#fff;
}
.scene-box .scene-list {
    display: block;
    margin-bottom: 20px;
    line-height: 1.6;
    background-color: rgba(35,35,35,0.7);
    padding:22px;
}
.scene-list .hover-line{
    height:3px;
    background-color:#ffffff;
    width:30px;
    margin:15px auto;
}
.scene-list:hover .hover-line{
    height:3px;
    background-color:#0083ff;
    width:30px;
    margin:15px auto;
}
.scene-list .scene-one{
    background-image:url(src/assets/images/sceneyingyong.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-list:hover .scene-one{
    background-image:url(src/assets/images/sceneyingyong_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-list .scene-two{
    background-image:url(src/assets/images/scenebushu.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-list:hover .scene-two{
    background-image:url(src/assets/images/scenebushu_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-list .scene-three{
    background-image:url(src/assets/images/sceneceshi.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
.scene-list:hover .scene-three{
    background-image:url(src/assets/images/sceneceshi_hover.png);
    background-repeat: no-repeat;
    background-size: 76px 76px;
}
/*价格begin*/
.price-box {
    background:url(src/assets/images/price-bg-gg.jpg) no-repeat right bottom #fff;
    width: 100%;
    height:660px;
    /*overflow:hidden;*/
}
.price-table th,.price-table td{
    text-align:center;
    /*padding:30px 0;*/
    border:solid 1px #d7d8d9;
    font-size:16px;
    background-color:#fff;
    height: 60px;
    line-height: 60px;
}
.price-table th{
    background-color:#f6f6f6;
}

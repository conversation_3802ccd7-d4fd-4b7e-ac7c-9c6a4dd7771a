import { Component, OnInit, OnDestroy } from '@angular/core';
import { ObservableService } from 'src/app/service/common/observable/observable.service';
import { environment } from 'src/environments/environment';
import { DomSanitizer } from '@angular/platform-browser';

const subList = [];

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrls: ['./index.component.less']
})
export class IndexComponent implements OnInit, OnDestroy {
    constructor(
        private obsService: ObservableService,
        private sanitizer: DomSanitizer
    ) {}

    mainMenuFold: boolean;
    devopsUrl = this.sanitizer.bypassSecurityTrustResourceUrl(environment.devopsUrl);
    
    ngOnInit() {
        // 订阅全局主菜单折叠标志位
        let menuSub = this.obsService.mainMenuFold.subscribe(fold => {
            this.mainMenuFold = fold;
        });

        subList.push(menuSub);
    }

    ngOnDestroy() {
        while(subList.length) {
            let sub = subList.shift();
            if(typeof sub.unsubscribe === 'function') {
                sub.unsubscribe();
            }
        }
    }
}

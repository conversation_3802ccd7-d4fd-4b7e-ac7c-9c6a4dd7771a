<div class="table-content">
    <div class="on-panel">
        <div class="welcome-box">
            <div class="welcome-title">
                您好！
                欢迎使用{{title}}
            </div>
            <div class="description">专为数字化场景打造的云管平台，提供企业级云计算资源的一站式智能管理服务</div>
        </div>
        <div class="welcome-content">
            <div class="welcome-content-row">
                <div class="welcome-content-header">
                    <nz-tabset
                        nzType="line"
                        nzSize="default"
                        [nzSelectedIndex]="selectedTabIndex"
                        (nzSelectedIndexChange)="onTabChange($event)">
                        <nz-tab nzTitle="产品目录视图"></nz-tab>
                        <nz-tab nzTitle="资源列表视图"></nz-tab>
                    </nz-tabset>
                </div>
            </div>
            <!-- 产品目录视图内容 -->
            <div class="welcome-content-row" *ngIf="selectedTabIndex === 0">
                <div class="welcome-content-column" style="flex:7.3">
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card flex_3">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-gpu-compute.svg"/>
                                计算
                            </div>
                            <div class="content-column-items gap_4">
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/cloud-server/instance')">
                                    <div>云服务器 >></div><div>{{catalogInstanceCounts.ecs}} 虚拟机</div>
                                </div>
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/cloud-server/bare-metal')">
                                    <div>裸金属服务器 >></div><div>0 物理服务器</div>
                                </div>
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/cloud-disk/index')">
                                    <div>云盘 >></div><div>{{catalogInstanceCounts.evs}} Mount</div>
                                </div>
                            </div>
                        </div>
                        <div class="content-column welcome-card flex_1">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-vdi.png"/>
                                云桌面
                            </div>
                            <div class="content-column-items">
                                <div class="item full-item flex column mouse-pointer" (click)="forword('/console/vdi/index')">
                                    <div>桌面虚拟机 >></div><div>{{catalogInstanceCounts.vdi}} 虚拟机</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-network.png"/>
                                网络与安全
                            </div>
                            <div class="content-column-items gap_4">
                                <div class="item flex column flex_2 mouse-pointer" (click)="forword('/console/proprietary-network/index')">
                                    <div>专有网络VPC >></div>
                                    <div>{{catalogInstanceCounts.vpc}} 专有网络</div>
                                </div>
                                <div class="item flex column flex_4 mouse-pointer" (click)="forword('/console/proprietary-network/security-group')">
                                    <div>安全 >></div>
                                    <div class="flex">
                                        <span class="flex_1">{{catalogInstanceCounts.securityGroup}} 安全组</span>
                                        <span class="flex_1">0 ACL</span>
                                        <span class="flex_1">0 防火墙</span>
                                    </div>
                                </div>
                                <div class="item flex column flex_3 mouse-pointer" (click)="forword('/console/proprietary-network/elastic-pub-network-ip')">
                                    <div>弹性公网IP >></div>
                                    <div class="flex">
                                        <span class="flex_1">{{catalogInstanceCounts.elasticip}} 公网IP</span>
                                        <span class="flex_1">0 公网映射</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-gpu-compute.svg"/>
                                存储
                            </div>
                            <div class="content-column-items gap_4">
                                <div class="item flex column flex_1"><div>文件存储 >></div><div>总容量 0 GB</div></div>
                                <div class="item flex column flex_1">
                                    <div>对象存储 >></div>
                                    <div class="flex">
                                        <span class="flex_1">0 Bucket</span>
                                        <span class="flex_1">0 GB</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-column welcome-card mouse-pointer" (click)="forword('/console/container/kubernetes')">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-k8s.png"/>
                                容器
                            </div>
                            <div class="content-column-items">
                                <div class="item flex column flex_1"><div>集群 >></div><div>{{catalogInstanceCounts.k8s}} 集群</div></div>
                            </div>
                        </div>
                    </div>

                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-database.png"/>
                                云数据库
                            </div>
                            <div class="content-column-items">
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/database/redis')">
                                    <div>Redis: </div>
                                    <div>{{catalogInstanceCounts.redis}} 集群</div>
                                </div>
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/database/rds')">
                                    <div>RDS: </div>
                                    <div>{{catalogInstanceCounts.rds}} 集群</div>
                                </div>
                            </div>
                        </div>
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <img src="./assets/images/resource-middleware.png"/>
                                中间件
                            </div>
                            <div class="content-column-items">
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/message-queues/kafka')">
                                    <div>Kafka >></div>
                                    <div>{{catalogInstanceCounts.kafka}} 集群</div>
                                </div>
                                <div class="item flex column flex_1 mouse-pointer" (click)="forword('/console/message-queues/rabbitmq')">
                                    <div>RabbitMQ >></div>
                                    <div>{{catalogInstanceCounts.rabbitmq}} 集群</div>
                                </div>
                            </div>
                        </div>
                    </div>
<!--                    <div class="welcome-content-row">-->
<!--                        <div class="content-column welcome-card" style="flex:6.2">-->
<!--                            <div class="content-column-header">-->
<!--                                GPU云主机配额-->
<!--                            </div>-->
<!--                            <div class="content-column-items quota">-->
<!--                                <nz-empty nzNotFoundImage="simple"></nz-empty>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                        <div class="content-column welcome-card" style="flex:3.8">-->
<!--                            <div class="content-column-header">-->
<!--                                GPU裸金属配额-->
<!--                            </div>-->
<!--                            <div class="content-column-items quota">-->
<!--                                <nz-empty nzNotFoundImage="simple"></nz-empty>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card" style="flex:6.2">
                            <div class="content-column-header">
                                成本统计
                            </div>
                            <div class="content-column-items quota" id="table">
                                <div echarts [options]="costChartOption" [autoResize]="true" class="cost-chart"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="welcome-content-column top-item" style="flex:2.7;">
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <div>我的信息</div>
                                <span class="right-item">今天是{{ formattedDate }}</span>
                            </div>
                            <span class="welcome-content-row">
                                <i nz-icon nzType="user" nzTheme="outline"></i><span class="item">{{username}}</span>
                            </span>
                            <span class="welcome-content-row">
                                <i nz-icon nzType="mail" nzTheme="outline"></i><span class="item"></span>
                            </span>
                            <!--                            <span class="welcome-content-row">-->
                            <!--                                <i nz-icon nzType="info-circle" nzTheme="outline"></i><span class="item">aaa</span>-->
                            <!--                            </span>-->
                        </div>
                    </div>

                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <div>我的消息</div>
                            </div>
                            <div class="content-column-items" style="min-height: 50px;">
                                <span routerLink="/console/app-system/application" class="message item" *ngFor="let item of messageData" style="cursor: pointer">企业项目"{{item.appSystemName}}"正在申请{{item.serviceText}}{{item.resourceTypeText}}变更</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 资源列表视图内容 -->
            <div class="welcome-content-row" *ngIf="selectedTabIndex === 1">
                <div class="welcome-content-column" style="flex:7.3">
                    <div class="content-column" style="min-height: 400px; padding: 20px; padding:0">
                        <div class="content-column-header search-bar">
                            <div class="flex gap_16 mb_16">
                                <!-- 搜索框 -->
                                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton" style="width: 200px;">
                                    <input type="text"
                                           nz-input
                                           placeholder="请输入名称搜索"
                                           [(ngModel)]="searchFilters.searchText"
                                           (keyup)="onSearchTextChange()" />
                                </nz-input-group>
                                <ng-template #suffixIconButton>
                                    <button nz-button nzType="primary" nzSearch>
                                        <i nz-icon nzType="search"></i>
                                    </button>
                                </ng-template>

                                <!-- 服务类目下拉框 -->
                                <nz-select
                                    nzPlaceHolder="请选择服务类目"
                                    style="width: 150px;"
                                    [(ngModel)]="searchFilters.serviceCatalog"
                                    (ngModelChange)="onServiceCatalogChange($event)"
                                    nzAllowClear>
                                    <nz-option nzValue="" nzLabel="全部服务类目"></nz-option>
                                    <nz-option
                                        *ngFor="let item of enumDatas.serviceCatalogList"
                                        [nzValue]="item.key"
                                        [nzLabel]="item.value">
                                    </nz-option>
                                </nz-select>

                                <!-- 服务类型下拉框 -->
                                <nz-select
                                    nzPlaceHolder="请选择服务类型"
                                    style="width: 150px;"
                                    [(ngModel)]="searchFilters.serviceType"
                                    (ngModelChange)="onServiceTypeChange($event)"
                                    [nzDisabled]="!searchFilters.serviceCatalog"
                                    nzAllowClear>
                                    <nz-option nzValue="" nzLabel="全部资源类型"></nz-option>
                                    <nz-option
                                        *ngFor="let type of serviceTypeOptions"
                                        [nzValue]="type"
                                        [nzLabel]="enumDatas.serviceTypeMap[type] || type">
                                    </nz-option>
                                </nz-select>
                            </div>
                            <div class="flex gap_8 align_center" style="margin-left: auto; display: flex; align-items: center; gap: 8px;padding-right:10px;margin-bottom: 16px;">
                                <nz-switch
                                        [(ngModel)]="showFullDate"
                                        nzCheckedChildren="时分秒"
                                        nzUnCheckedChildren="日期">
                                </nz-switch>
                            </div>
                        </div>
                        <nz-table #tableList [nzLoading]="isLoading"
                                  style="overflow:hidden;overflow-x: auto;width: 100%"
                                  [nzLoadingDelay]="300"
                                  nzSize="small"
                                  nzTableLayout="fixed"
                                  [nzFrontPagination]="false"
                                  [nzTotal]="pager.total"
                                  [nzPageIndex]="pager.page"
                                  [nzPageSize]="pager.pageSize"
                                  [nzShowSizeChanger]="true"
                                  [nzPageSizeOptions]="[10, 20, 50, 100]"
                                  [nzShowQuickJumper]="true"
                                  (nzPageIndexChange)="pageChanged($event)"
                                  (nzPageSizeChange)="pageSizeChanged($event)"
                                  [nzScroll]="{ y: '500px' }"
                                  [nzData]="tableData">
                            <thead>
                            <tr>
                                <th nzWidth="40%" [nzSortFn]="nameSortFn">名称</th>
                                <th nzWidth="10%">服务类目</th>
                                <th nzWidth="10%">资源类型</th>
                                <th nzWidth="20%" [nzSortFn]="createDateSortFn">创建日期</th>
                                <th nzWidth="20%" [nzSortFn]="updateDateSortFn">更新日期</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let data of tableList.data">
                                <td class="name-column" [nz-tooltip]="data.name" nzTooltipPlacement="topLeft">
                                    {{data.name}}
                                </td>
                                <td>{{enumDatas.serviceCatalogMap[data.serviceCatalog] }}</td>
                                <td>{{enumDatas.serviceTypeMap[data.serviceType] }}</td>
                                <td>{{formatCreateDate(data.instanceCreateTm)}}</td>
                                <td>{{formatUpdateDate(data.instanceUpdateTm)}}</td>
                            </tr>
                            <tr [hidden]="tableList.data.length || !isLoading"
                                class="loading-placeholder">
                                <td colspan="100%"></td>
                            </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </div>

                <div class="welcome-content-column top-item" style="flex:2.7;">
                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <div>我的信息</div>
                                <span class="right-item">今天是{{ formattedDate }}</span>
                            </div>
                            <span class="welcome-content-row">
                                <i nz-icon nzType="user" nzTheme="outline"></i><span class="item">{{username}}</span>
                            </span>
                            <span class="welcome-content-row">
                                <i nz-icon nzType="mail" nzTheme="outline"></i><span class="item"></span>
                            </span>
                        </div>
                    </div>

                    <div class="welcome-content-row">
                        <div class="content-column welcome-card">
                            <div class="content-column-header">
                                <div>我的消息</div>
                            </div>
                            <div class="content-column-items" style="min-height: 50px;">
                                <span routerLink="/console/app-system/application" class="message item" *ngFor="let item of messageData" style="cursor: pointer">企业项目"{{item.appSystemName}}"正在申请{{item.serviceText}}{{item.resourceTypeText}}变更</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

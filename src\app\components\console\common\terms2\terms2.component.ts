import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';


@Component({
    selector: 'app-terms2',
    templateUrl: './terms2.component.html',
    styleUrls: ['./terms2.component.less']
})
export class Terms2Component implements OnInit {

    constructor() { }
    @Input() isVisible: boolean = false;
    @Input() contentStyle: object;

    @Output() agree = new EventEmitter<boolean>();
    @Output() reject = new EventEmitter<boolean>();

    ngOnInit() { }

    handleCancel() {
        this.reject.emit(true);
    }

    handleOk() {
        this.agree.emit(true);
    }
}

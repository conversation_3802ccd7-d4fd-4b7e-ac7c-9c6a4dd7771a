import { Component, OnInit } from '@angular/core';
 
declare let $: any;

@Component({
    selector: 'app-file-storage',
    templateUrl: './file-storage.component.html',
    styleUrls: ['./file-storage.component.less']
})
export class FileStorageComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '文件存储',
        enName: 'OnetNET File Storage',
        desc: 'OFS（AIC File Storage，AIC文件存储）是面向云主机的分布式文件系统服务，提供NFS文件访问协议，完全兼容现有传统应用， 具备容量弹性扩容、性能线性扩展、独立命名空间、多共享、高可靠和高可用等特性。',
        bgColor: '#1a284d',
        orderLink: '/console/file-store/index',
        type: 'fileStorageProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

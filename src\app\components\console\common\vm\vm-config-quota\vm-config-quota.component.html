<form [formGroup]="vmquota">
    <section class="field-section">
        <div class="field-title">
        选择订单
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">合同名称</span>
                    <nz-select formControlName="Quota" (ngModelChange)="selectQuota($event)"
                        nzPlaceHolder="请选择合同名称">
                        <nz-option *ngFor="let item of QuotaList" [nzValue]="item" [nzLabel]="item.name +'(' + item.code + ')'" nzCustomContent>
                            <span [title]="item.name +'(' + item.code + ')'">{{item.name +'(' + item.code + ')'}}</span>
                        </nz-option>
                    </nz-select>
                </label>
            </div>
            <div class="field-item required" *ngIf="vmquota.value.quotaName">
                <label for="">
                    <span class="label-text">订单项ID</span>
                    <nz-select formControlName="QuotaDetail" (ngModelChange)="selectQuotaDetail($event)"
                        nzPlaceHolder="请选择订单项ID">
                        <nz-option *ngFor="let item of QuotaDetailList" [nzValue]="item"
                        [nzLabel]="item.name +'(' + item.subCode + ')'" nzCustomContent>
                            <span [title]="item.name +'(' + item.subCode + ')'">{{item.name +'(' + item.subCode + ')'}}</span>
                        </nz-option>
                    </nz-select>
                </label>
            </div>
         </div>
    </section>

    <!-- 云服务器类型 -->
    <section class="field-section">
        <div class="field-title">
            类型
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">云服务器类型</span>
                    <nz-radio-group formControlName="serverType" 
                     (ngModelChange)="serverStandardTypeChange($event)">
                        <label nz-radio-button [nzValue]="item.serverConfigsKey"
                            *ngFor="let item of serverStandardTypeList">
                            {{ item.value }}
                        </label>
                    </nz-radio-group>
                </label>
            </div>
        </div>
    </section>

     <section class="field-section">
        <div class="field-title">
            配置
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">配置列表</span>
                    <ul class="server-config-list">
                        <li 
                        *ngFor="let item of (serverCon || [])"
                        [ngClass]="{'active': vmquota.value.serverConfigId.id === item.id}"
                            (click)="selectConfig(item)">
                            <p class="cpu">{{ item.cpuUnit }}核</p>
                            <p class="memory">{{ item.memoryUnit }}G</p>
                            <!-- <p class="charge"><span class="hl">{{ getServerPrice(item) }}</span>元/小时</p> -->
                        </li>
                    </ul>
                </label>
            </div>
        </div>
    </section>

    <section class="field-section">
        <div class="field-title">
            镜像选择
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">镜像列表</span>
                    <nz-select (ngModelChange)="selectImage($event)" formControlName="imageId"
                        nzPlaceHolder="请选择镜像">
                        <nz-option nzCustomContent *ngFor="let item of initData.images" [nzValue]="item" [nzLabel]="item.name">
                            <div class="image-item" [title]="item.name">{{ item.name }}</div>
                        </nz-option>
                    </nz-select>
                </label>
            </div>
        </div>
    </section>
    <section class="field-section">
        <div class="field-title">
            存储空间
        </div>
        <div class="field-group" formArrayName="cloudDiskList">
            <div class="field-item required" [formGroupName]="0">
                <label>
                    <span class="label-text">系统盘(GB)</span>
                    <input type="text" formControlName="diskGB" readonly (keydown.enter)="keyEnter()" />
<!--                    <span class="small tip" >容量会根据所选镜像自动分配</span>-->
                </label>
            </div>
            <div class="field-item" [formGroupName]="1">
                <label for="">
                    <!-- <span class="label-text">数据盘(GB)</span>
                    <nz-select formControlName="diskType" nzPlaceHolder="请选择云盘"
                        (ngModelChange)="onsele($event)" >  
                        <nz-option *ngFor="let item of diskStandardTypeList" [nzValue]="item.diskConfigsKey" [nzLabel]="item.value">
                        </nz-option>
                                 
                    </nz-select>
                        <span>&nbsp;&nbsp;</span>
                        <app-numberes-input
                            [min]="diskConfig.minValue"
                            [max]="diskConfig.maxValue"
                            [step]="diskConfig.step"
                            [allowZero]="true"
                            (valueChange)="diskChange($event)"
                            [errorHint]="diskErrorHint"
                            (errorHintChange)="setDiskError($event)"  
                            #numberesInputComponent (keydown.enter)="keyEnter()">
                        </app-numberes-input>
                        <span class="small tip form-hint error" style="padding: 0;"  *ngIf="diskErrorHint">{{ diskErrorHint }}</span>
                        <p class="label-padding" style="padding:200">
                            已选择{{ vmquota.value.cloudDiskList[1]?.diskGB || 0 }}GB存储空间，预计费用为
                            <span class="hl">{{ calcDiskCost() }}</span> 元/小时
                        </p> -->                
                </label>
            </div>
        </div>
    </section>

    

    <section class="field-section" style="margin-top: -50px;">
        <div class="field-title">
            网络
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label for="">
                    <span class="label-text">专有网络</span>
     
                    <nz-select formControlName="vpcId" nzPlaceHolder="请选择专有网络"
                        (ngModelChange)="selectVpc($event)">
                        <nz-option *ngFor="let item of initData.vpc" [nzValue]="item" [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                    <nz-select
                        *ngIf="vmquota.value.vpcId"
                        formControlName="networkId"
                        nzPlaceHolder="请选择子网">
                        <nz-option *ngFor="let item of vmquota.value.vpcId.network" [nzValue]="item.id"
                            [nzLabel]="item.name">
                        </nz-option>
                    </nz-select>
                </label>
                <p class="small tip label-padding">如需使用其它专有网络，请选择已有专有网络，也可以到
                    <a routerLink="/console/proprietary-network/index">VPC控制台</a>
                创建</p>
                <div class="form-hint error" *ngIf="isInvalid(vmquota.get('vpcId'))">
                    <div *ngIf="vmquota.get('vpcId').hasError('required')">
                        请选择一个专有网络
                    </div>
                </div>
                <div class="form-hint error" *ngIf="!vmquota.get('vpcId').hasError('required') && isInvalid(vmquota.get('networkId'))">
                    <div *ngIf="vmquota.get('networkId').hasError('required')">
                        请选择一个子网
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="field-section" *ngIf="options.publicNetworkIp">
        <div class="field-title">
            弹性公网IP
        </div>
        <div class="field-group">
            <div class="field-item">
                <label>
                    <span class="label-text">分配弹性公网IP</span>
                    <label nz-checkbox formControlName="isElasticIp"></label>
                </label>
                <span [hidden]="vmquota.value.isElasticIp" class="small tip">不为实例分配公网IP地址<span *ngIf="!options.hidePublicNetworkBindTip">，如需访问公网，请配置并
                        <a routerLink="/console/elastic-pub-network-ip/index">绑定弹性公网IP地址</a></span></span>
            </div>
            <div *ngIf="vmquota.value.isElasticIp" formArrayName="elasticIpList">
                <div [formGroupName]="0">
                    <div class="field-item">
                        <label>
                            <span class="label-text">付费方式</span>
                            <nz-radio-group formControlName="chargeType">
                                <label *ngFor="let item of (initData.elasticIpChargeType || {}) | keyvalue"
                                    nz-radio-button [nzValue]="item.key">{{ item.value }}</label>
                            </nz-radio-group>
                            <span class="small tip">
                                后付费模式，按使用流量（单位为GB）计费，每小时扣费，请保证余额充足
                            </span>
                        </label>
                    </div>
                    <div class="field-item">
                        <label>
                            <span class="label-text">峰值带宽(Mbps)</span>
                            <div class="bandwidth-picker">
                                <nz-slider [nzMarks]="bandwidthMarks" nzDots formControlName="bandwidth">
                                </nz-slider>
                            </div>
                            <p class="label-padding">
                                已选择{{ vmquota.value.elasticIpList[0].bandwidth }}Mbps带宽，预计费用为
                                <span class="hl">{{ calcNetworkCost() }}</span>元/GB
                            </p>
                            <p class="small tip label-padding">
                                按公网出方向的实际发生的网络流量进行收费，适用于业务场景对网络带宽需求变化较大的场景， 如平时带宽使用较低但间歇性的出现网络访问高峰的场景；
                                <br>为了防止突然爆发的流量产生较高的费用， 可以指定容许的最大网络带宽进行限制。</p>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="field-section">
        <div class="field-title">
            登录凭证
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">
                        登录方式
                    </span>
                    <nz-radio-group
                        *ngIf="options.allowedKeyType === 'all'"
                        formControlName="keyType">
                        <label *ngFor="let item of (initData.keyType || {}) | keyvalue" nz-radio-button
                            [nzValue]="item.key">{{ item.value }}</label>
                    </nz-radio-group>
                    <nz-radio-group
                        *ngIf="options.allowedKeyType === 'password'"
                        formControlName="keyType">
                        <label nz-radio-button nzValue="password">密码</label>
                    </nz-radio-group>
                    <nz-radio-group
                        *ngIf="options.allowedKeyType === 'keypair'"
                        formControlName="keyType">
                        <label nz-radio-button nzValue="keypair">密钥对</label>
                    </nz-radio-group>
                </label>
            </div>
            <div class="field-item required">
                <div class="password-input-container"
                    *ngIf="options.allowedKeyType !== 'keypair'"
                    [hidden]="!(vmquota.value.keyType === 'password')">
                    <div class="label-padding">
                        <p class="small tip">
                            登录帐号是 root(拥有sudo权限)，通过支持 ssh 的工具来访问您的云服务器
                        </p>
                        <div class="input-group-suffix">
                            <input [type]="passwordVisible ? 'text' : 'password'"
                                formControlName="password"
                                (focus)="checkPasswordStatus()"
                                (input)="validatePassword()"
                                nz-tooltip
                                nzTooltipTitle="密码丢失将无法找回，请谨慎保管！"
                                nzTooltipTrigger="focus"
                                placeholder="请输入密码" (keydown.enter)="keyEnter()" class="clspass">
                            <i class="suffix" [title]="passwordVisible ? '隐藏密码' : '查看密码'" nz-icon
                                [nzType]="passwordVisible ? 'eye-invisible' : 'eye'"
                                (click)="passwordVisible = !passwordVisible"></i>
                        </div>
                        <span class="small tip">
                            密码须包含大小写字母和数字，支持常用特殊字符，长度为8-20位
                        </span>
                        <div class="password-hint" [ngClass]="{'unfold': passwordStatus.focus}">
                            <ul>
                                <li [ngClass]="{'checked': passwordStatus.uppercase}">
                                    <i class="icon" nz-icon [nzType]="passwordStatus.uppercase ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                    至少包含一个大写字母
                                </li>
                                <li [ngClass]="{'checked': passwordStatus.lowercase}">
                                    <i class="icon" nz-icon [nzType]="passwordStatus.lowercase ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                    至少包含一个小写字母
                                </li>
                                <li [ngClass]="{'checked': passwordStatus.digital}">
                                    <i class="icon" nz-icon [nzType]="passwordStatus.digital ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                    至少包含一个数字
                                </li>
                                <li [ngClass]="{'checked': passwordStatus.length}">
                                    <i class="icon" nz-icon [nzType]="passwordStatus.length ? 'check-circle' : 'exclamation-circle'" nzTheme="fill"></i>
                                    长度在8-20位之间
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-hint error" *ngIf="isInvalid(vmquota.get('password'))">
                        <div *ngIf="vmquota.get('password').hasError('required')">
                            登录密码不能为空
                        </div>
                        <div *ngIf="vmquota.get('password').hasError('pwError')">
                            密码格式不符合要求
                        </div>
                    </div>
                </div>
                <div class="secret-key-input-container"
                    *ngIf="options.allowedKeyType !== 'password'"
                    [hidden]="!(vmquota.value.keyType === 'keypair')">
                    <label for="">
                        <span class="label-text">
                            密钥对
                        </span>
                        <nz-select style="margin-right: 10px" formControlName="keyId" nzPlaceHolder="请选择">
                            <nz-option *ngFor="let item of (initData.keyPairs || [])" [nzValue]="item" [nzLabel]="item.name">
                            </nz-option>
                        </nz-select>
                        <button type="button" nz-button nzType="default" (click)="keyPairModalVisible = true">
                            <i nz-icon nzType="plus" nzTheme="outline"></i>
                            创建密钥对
                        </button>
                    </label>
                    <div class="form-hint error" *ngIf="isInvalid(vmquota.get('keyId'))">
                        <div *ngIf="vmquota.get('keyId').hasError('required')">
                            请选择或创建一个密钥对
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="field-section">
        <div class="field-title">
            实例信息
        </div>
        <div class="field-group">
            <div class="field-item required">
                <label>
                    <span class="label-text">实例名称</span>
                    <input required maxlength="50" type="text" formControlName="name" placeholder="请输入实例名称" (keydown.enter)="keyEnter()" />
                    <span class="small tip">
                        实例名称不能以数字开头，只能包含英文字母，数字和横线
                    </span>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(vmquota.get('name'))">
                    <div *ngIf="vmquota.get('name').hasError('required')">
                        实例名称不能为空
                    </div>
                    <div *ngIf="vmquota.get('name').hasError('maxlength')">
                        实例名称长度不能超过{{ vmquota.get('name').errors.maxlength.requiredLength }}个字符
                    </div>
                    <div *ngIf="vmquota.get('name').hasError('pattern')">
                        实例名称不符合规范
                    </div>
                </div>
            </div>
            <div class="field-item required">
                <label>
                    <span class="label-text">主机名称</span>
                    <input required maxlength="50" type="text" formControlName="hostname"
                        placeholder="请输入主机名称" (keydown.enter)="keyEnter()" />
                    <span class="small tip">
                        主机名称不能以数字开头，只能包含英文字母，数字和横线
                    </span>
                </label>
                <div class="form-hint error" *ngIf="isInvalid(vmquota.get('hostname'))">
                    <div *ngIf="vmquota.get('hostname').hasError('required')">
                        主机名称不能为空
                    </div>
                    <div *ngIf="vmquota.get('hostname').hasError('maxlength')">
                        主机名称长度不能超过{{ vmquota.get('hostname').errors.maxlength.requiredLength }}个字符
                    </div>
                    <div *ngIf="vmquota.get('hostname').hasError('pattern')">
                        主机名称不符合规范
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- <section class="field-section" *ngIf="options.userScript">
        <div class="field-title">
            用户数据注入
        </div>
        <div class="field-group">
            <div class="field-item">
                <label for="">
                    <span class="label-text">上传脚本</span>
                    <nz-upload [nzDisabled]="isUploading" [nzCustomRequest]="uploadScript" nzAccept=".sh"
                        [nzBeforeUpload]="beforeUpload" [(nzFileList)]="fileList" [nzSize]="20 * 1024"
                        [nzRemove]="removeScript" (nzChange)="uploadChange($event)">
                        <button type="button" nz-button><i nz-icon nzType="upload"></i><span>
                                {{ isUploading ? '上传中' : '点击上传' }}
                            </span></button>
                    </nz-upload>

                </label>
                <span class="small tip vertical-top">
                    请上传.sh脚本，大小在20MB以内
                </span>
                <div class="form-hint error" *ngIf="fileError">
                    {{ fileError }}
                </div>
            </div>
        </div>
    </section> -->

    <section class="field-section" *ngIf="options.buyAmount" style = "display:none;">
        <div class="field-title">
            订单信息
        </div>
        <div class="field-group">
            <div class="field-item">
                <label for="">
                    <span class="label-text">购买数量</span>
                    <app-number-input
                        [value]="1"
                        [min]="1"
                        [max]="vmCount"
                        [step]="1"
                        [showRange]="false"
                        [allowInput]="false"
                        (valueChange)="amountChange($event)"
                        (keydown.enter)="keyEnter()">
                    </app-number-input>
                </label>
            </div>
        </div>
    </section>
</form>

<app-new-key
    [isVisible]="keyPairModalVisible"
    (close)="keyPairModalVisible = false"
    (submit)="reloadKeyPair()">
</app-new-key>

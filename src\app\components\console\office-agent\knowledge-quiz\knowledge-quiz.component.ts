import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute } from '@angular/router';
import { forkJoin } from 'rxjs';
import * as marked from 'marked';
import * as hljs from 'highlight.js';

interface APIModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  key: string;
  dir_key: string;
  search_type: string;
}

interface AIModel {
  id: string;
  name: string;
  description?: string;
}

interface ModelGroup {
  groupName: string;
  models: AIModel[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isMarkdown?: boolean;
  renderedContent?: string;
}

@Component({
  selector: 'app-knowledge-quiz',
  templateUrl: './knowledge-quiz.component.html',
  styleUrls: ['./knowledge-quiz.component.less']
})
export class KnowledgeQuizComponent implements OnInit {
  
  // AI模型相关
  aiModels: AIModel[] = [];
  modelGroups: ModelGroup[] = [];
  selectedModel: string = '';
  isLoadingModels = false;
  
  // 对话相关
  messages: ChatMessage[] = [];
  currentMessage = '';
  isSending = false;
  hasConversation = false; // 是否发生过对话

  // 移除输入框功能按钮状态（深度思考和联网搜索）
  
  constructor(
    private http: HttpClient,
    private message: NzMessageService,
    private route: ActivatedRoute
  ) {}

  /**
   * 配置marked选项
   */
  private configureMarked() {
    marked.setOptions({
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(lang, code).value;
          } catch (err) {
            console.warn('代码高亮失败:', err);
          }
        }
        return hljs.highlightAuto(code).value;
      },
      breaks: true,
      gfm: true
    });
  }

  ngOnInit() {
    this.configureMarked();
    this.loadAIModels();
    this.initWelcomeMessage();

    // 检查是否有传入的消息参数
    this.route.queryParams.subscribe(params => {
      if (params['message']) {
        this.currentMessage = params['message'];
        // 自动选择史学本地搜索模型并提交
        this.selectHistoryLocalSearchAndSubmit();
      }
    });
  }

  /**
   * 加载AI模型列表
   */
  loadAIModels() {
    this.isLoadingModels = true;

    // 并行请求两个模型接口
    const historyModels$ = this.http.get<{object: string, data: APIModel[]}>('http://history.chat.vesystem.godscode.com.cn/chat/v1/models');
    const jinyongModels$ = this.http.get<{object: string, data: APIModel[]}>('http://jinyong.chat.vesystem.godscode.com.cn/chat/v1/models');

    // 使用forkJoin并行请求
    forkJoin({
      history: historyModels$,
      jinyong: jinyongModels$
    }).subscribe({
      next: (results) => {
        this.processModelData(results);
        this.isLoadingModels = false;
      },
      error: (error) => {
        console.error('加载AI模型失败:', error);
        this.message.error('加载AI模型失败，请稍后重试');
        // 使用默认模型数据
        this.loadDefaultModels();
        this.isLoadingModels = false;
      }
    });
  }

  /**
   * 处理模型数据并分组
   */
  private processModelData(results: { history: any, jinyong: any }) {
    this.modelGroups = [];
    this.aiModels = [];

    // 处理史学模型
    if (results.history && results.history.data) {
      const historyModels: AIModel[] = results.history.data.map((model: APIModel) => ({
        id: model.id,
        name: model.id, // 使用id作为显示名称
        description: `${model.search_type || '史学模型'}`
      }));

      this.modelGroups.push({
        groupName: '史学模型',
        models: historyModels
      });

      this.aiModels.push(...historyModels);
    }

    // 处理金庸小说模型
    if (results.jinyong && results.jinyong.data) {
      const jinyongModels: AIModel[] = results.jinyong.data.map((model: APIModel) => ({
        id: model.id,
        name: model.id, // 使用id作为显示名称
        description: `${model.search_type || '金庸小说模型'}`
      }));

      this.modelGroups.push({
        groupName: '金庸小说模型',
        models: jinyongModels
      });

      this.aiModels.push(...jinyongModels);
    }

    // 设置默认选中的模型
    if (this.aiModels.length > 0) {
      this.selectedModel = this.aiModels[0].id;
    }
  }

  /**
   * 加载默认模型数据（当接口请求失败时使用）
   */
  private loadDefaultModels() {
    this.modelGroups = [
      {
        groupName: '默认模型',
        models: [
          { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速响应的对话模型' },
          { id: 'gpt-4', name: 'GPT-4', description: '更强大的推理能力' },
          { id: 'local-search', name: '本地搜索', description: '基于本地知识库的搜索' }
        ] as AIModel[]
      }
    ];

    this.aiModels = this.modelGroups[0].models;
    if (this.aiModels.length > 0) {
      this.selectedModel = this.aiModels[0].id;
    }
  }

  /**
   * 初始化欢迎消息
   */
  initWelcomeMessage() {
    this.messages = [{
      id: this.generateId(),
      content: '您好！我是您的AI助手，有什么问题可以随时问我。',
      isUser: false,
      timestamp: new Date()
    }];
  }

  /**
   * 发送消息
   */
  sendMessage() {
    if (!this.currentMessage.trim()) {
      return;
    }

    if (!this.selectedModel) {
      this.message.warning('请先选择AI模型');
      return;
    }

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: this.generateId(),
      content: this.currentMessage.trim(),
      isUser: true,
      timestamp: new Date()
    };
    this.messages.push(userMessage);

    // 保存当前消息内容
    const messageContent = this.currentMessage.trim();
    this.currentMessage = '';
    this.isSending = true;
    this.hasConversation = true; // 标记已发生对话

    // 添加AI消息占位符
    const aiMessage: ChatMessage = {
      id: this.generateId(),
      content: '',
      isUser: false,
      timestamp: new Date()
    };
    this.messages.push(aiMessage);

    // 滚动到底部
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);

    // 发起SSE请求
    this.sendSSERequest(messageContent, aiMessage);
  }

  /**
   * 发起SSE长链接请求
   */
  private sendSSERequest(userContent: string, aiMessage: ChatMessage) {
    const requestBody = {
      model: this.selectedModel,
      messages: [
        // {
        //   role: "system",
        //   content: "You are a helpful assistant."
        // },
        {
          role: "user",
          content: userContent
        }
      ]
    };

    // 使用fetch进行POST SSE请求
    fetch('http://history.chat.vesystem.godscode.com.cn/chat/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(requestBody)
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();

      const readStream = () => {
        reader.read().then(({ done, value }) => {
          if (done) {
            this.isSending = false;
            console.log('SSE流结束');
            return;
          }

          // 解码数据
          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                this.isSending = false;
                return;
              }

              try {
                const parsed = JSON.parse(data);

                // 处理流式响应数据
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                  aiMessage.content += parsed.choices[0].delta.content;

                  // 实时处理Markdown内容
                  this.processAIMessageContent(aiMessage);

                  // 滚动到底部
                  setTimeout(() => {
                    this.scrollToBottom();
                  }, 10);
                }

                // 检查是否完成
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].finish_reason) {
                  // 最终处理Markdown内容
                  this.processAIMessageContent(aiMessage);
                  this.isSending = false;
                  return;
                }
              } catch (error) {
                console.error('解析SSE数据失败:', error, 'data:', data);
              }
            }
          }

          // 继续读取下一块数据
          readStream();
        }).catch(error => {
          console.error('读取流数据失败:', error);
          this.isSending = false;
          aiMessage.content = '抱歉，连接出现问题，请稍后重试。';
          this.message.error('连接失败，请检查网络或稍后重试');
        });
      };

      // 开始读取流
      readStream();

    }).catch(error => {
      console.error('SSE请求失败:', error);
      this.isSending = false;
      aiMessage.content = '抱歉，连接出现问题，请稍后重试。';
      this.message.error('连接失败，请检查网络或稍后重试');
    });
  }

  /**
   * 处理Enter键发送
   */
  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }



  /**
   * 滚动到底部
   */
  private scrollToBottom() {
    try {
      const chatContainer = document.querySelector('.chat-messages');
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    } catch (err) {
      console.error('滚动失败:', err);
    }
  }

  /**
   * 检测文本是否为Markdown格式
   */
  private isMarkdownContent(text: string): boolean {
    if (!text || text.trim().length === 0) {
      return false;
    }

    // Markdown特征检测
    const markdownPatterns = [
      /^#{1,6}\s+.+$/m,           // 标题 # ## ###
      /^\*{1,2}[^*]+\*{1,2}/m,   // 粗体/斜体 *text* **text**
      /^_{1,2}[^_]+_{1,2}/m,     // 粗体/斜体 _text_ __text__
      /^\- .+$/m,                // 无序列表 - item
      /^\* .+$/m,                // 无序列表 * item
      /^\+ .+$/m,                // 无序列表 + item
      /^\d+\. .+$/m,             // 有序列表 1. item
      /^```[\s\S]*?```/m,        // 代码块 ```code```
      /`[^`]+`/,                 // 行内代码 `code`
      /^\[.+\]\(.+\)$/m,         // 链接 [text](url)
      /^!\[.*\]\(.+\)$/m,        // 图片 ![alt](url)
      /^\|.+\|$/m,               // 表格 |col1|col2|
      /^>.+$/m,                  // 引用 > text
      /^---+$/m,                 // 分割线 ---
      /^\s*\n\s*\n/              // 多个换行（段落分隔）
    ];

    // 检查是否匹配任何Markdown模式
    return markdownPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 渲染Markdown内容
   */
  private renderMarkdown(content: string): string {
    try {
      return marked.parse(content);
    } catch (error) {
      console.error('Markdown渲染失败:', error);
      return content; // 渲染失败时返回原始内容
    }
  }

  /**
   * 处理AI消息内容，检测并渲染Markdown
   */
  private processAIMessageContent(message: ChatMessage): void {
    if (!message.isUser && message.content) {
      const isMarkdown = this.isMarkdownContent(message.content);
      message.isMarkdown = isMarkdown;

      if (isMarkdown) {
        message.renderedContent = this.renderMarkdown(message.content);
      }
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 格式化时间
   */
  formatTime(date: Date): string {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * TrackBy函数用于优化ngFor性能
   */
  trackByMessageId(index: number, message: ChatMessage): string {
    return message.id;
  }

  /**
   * 获取选中模型的显示名称
   */
  getSelectedModelName(): string {
    if (!this.selectedModel) {
      return '选择模型';
    }

    const selectedModel = this.aiModels.find(model => model.id === this.selectedModel);
    return selectedModel ? selectedModel.name : this.selectedModel;
  }

  /**
   * 选择模型
   */
  selectModel(modelId: string) {
    this.selectedModel = modelId;
  }

  /**
   * 选择史学本地搜索模型并自动提交
   */
  private selectHistoryLocalSearchAndSubmit() {
    // 等待模型加载完成
    const checkModelsLoaded = () => {
      if (this.isLoadingModels) {
        // 如果还在加载，等待100ms后再检查
        setTimeout(checkModelsLoaded, 100);
        return;
      }

      // 查找史学模型中的本地搜索模型
      let historyLocalSearchModel = null;

      // 首先在史学模型组中查找包含"本地搜索"的模型
      const historyGroup = this.modelGroups.find(group => group.groupName === '史学模型');
      if (historyGroup) {
        historyLocalSearchModel = historyGroup.models.find(model =>
          model.description && model.description.includes('本地搜索')
        );
      }

      // 如果没找到，查找默认模型中的本地搜索
      if (!historyLocalSearchModel) {
        historyLocalSearchModel = this.aiModels.find(model =>
          model.id === 'local-search' ||
          (model.name && model.name.includes('本地搜索'))
        );
      }

      // 如果找到了史学本地搜索模型，选择它并提交
      if (historyLocalSearchModel) {
        this.selectedModel = historyLocalSearchModel.id;
        console.log('自动选择史学本地搜索模型:', historyLocalSearchModel);

        // 延迟一下再提交，确保UI更新完成
        setTimeout(() => {
          this.sendMessage();
        }, 200);
      } else {
        console.warn('未找到史学本地搜索模型，使用默认模型');
        // 如果没找到史学本地搜索，使用第一个可用模型
        if (this.aiModels.length > 0) {
          this.selectedModel = this.aiModels[0].id;
          setTimeout(() => {
            this.sendMessage();
          }, 200);
        }
      }
    };

    // 开始检查
    checkModelsLoaded();
  }
}

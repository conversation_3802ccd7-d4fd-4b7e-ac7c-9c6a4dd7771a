<header class="index-header">
    <div class="on-container">
        <nav>
            <div class="brand">
                <a href="" target="_blank" class="main-logo"></a>
                <span class="divider"></span>
                <a class="sub-logo" routerLink="/index">
                    <span class="app-logo"></span>
                    <span class="app-name">应用托管</span>
                </a>
                
            </div>
            <div class="nav-list-container">
                <ul class="nav-list" (mouseleave)="indicateActive()">
                    <li *ngFor="let item of navList"
                        class="nav-item"
                        routerLinkActive="active">
                        <div (mouseenter)="indicateItem($event, item);"
                            (mouseleave)="foldMenu(item)"
                            [ngClass]="{'on-dropdown': item.children, 'open': item.unfold}">
                            <a [routerLink]="item.routerLink">{{ item.text }}
                            </a>
                            <ul *ngIf="item.children" class="on-dropdown-menu" [ngClass]="{'extend': item.children.length > 6}">
                                <li *ngFor="let menu of item.children" class="on-dropdown-item" routerLinkActive="active">
                                    <a [routerLink]="menu.routerLink" (click)="foldMenu(item)">{{ menu.text }}</a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
                <div class="indicator" [ngStyle]="{'transform': 'translateX(' + indicator.left + 'px)', 'width': indicator.width + 'px'}"></div>
            </div>
            <ul class="nav-list nav-right">
                <li class="nav-item">
                    <a routerLink="/console">控制台</a>
                </li>
                <ng-container *ngIf="isLogin">
                    <li class="nav-item">
                        <a (click)="logout();">退出</a>
                    </li>
                    <li class="nav-item">
                        <nz-badge [nzCount]="msgCount">
                            <a class="username" routerLink="/console/user/message">{{ username }}</a>
                        </nz-badge>
                    </li>
                </ng-container>
                <ng-container *ngIf="!isLogin">
                    <li class="nav-item">
                        <a (click)="login();">登录</a>
                    </li>
                    <li class="nav-item register">
                        <a (click)="register();">注册</a>
                    </li>
                </ng-container>
            </ul>
        </nav>
    </div>
</header>

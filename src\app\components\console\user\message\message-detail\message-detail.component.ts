import { Component, OnInit } from '@angular/core';
import { MessageService } from 'src/app/service/console/user/message.service';
import { ActivatedRoute } from '@angular/router';

const HEAD_REGEXP = /\[.*?\]/g;
const COLORS = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae'];

@Component({
    selector: 'app-message-detail',
    templateUrl: './message-detail.component.html',
    styleUrls: ['./message-detail.component.less']
})
export class MessageDetailComponent implements OnInit {
    constructor(
        private msgService: MessageService,
        private route: ActivatedRoute
    ) {}
    
    message = {
        name: null,
        color: null,
        headText: null,
        messageTm: null,
        content: null,
    };

    ngOnInit() {
        let id = +this.route.snapshot.params.id;
        this.getDetail(id);
        this.setToRead(id);
    }

    getDetail(id) {
        this.msgService.getMsgDetail(id)
        .then(rs => {
            if (rs.success) {
                let message = rs.data;
                let marks = String(message.name).match(HEAD_REGEXP);
                if (marks && marks.length) {
                    message.headText = marks[0].replace(/\[|\]/g, '')[0].toUpperCase();
                } else {
                    message.headText = '站内';
                }

                message.color = this.getColor(message.headText);
                this.message = rs.data;
            }
        })
    }

    setToRead(id) {
        this.msgService.setToRead(id)
        .then(rs => {
            // console.log(rs);
            // if (rs.success) {
            //     // this.message = rs.data;
            // }
        })
    }

    getColor(mark) {
        let sum = 0;
        mark.split('').forEach(c => {
            sum += c.charCodeAt();
        });
        return COLORS[sum % 4];
    }
}

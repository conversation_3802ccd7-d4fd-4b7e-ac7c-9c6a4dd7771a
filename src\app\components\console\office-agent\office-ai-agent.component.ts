import { Component, OnInit} from '@angular/core';
import { registerLocaleData,formatDate } from '@angular/common';
import localeZh from '@angular/common/locales/zh';
import { EChartsOption } from 'echarts';
import * as echarts from 'echarts';
import { ApplicationService } from 'src/app/service/console/app-system/application.service';
import {Router} from "@angular/router";

registerLocaleData(localeZh);
interface CostData {
    month: string; // "202403"
    amount: number; // 12345.67
}
@Component({
    selector: 'app-office-ai-agent',
    templateUrl: './office-ai-agent.component.html',
    styleUrls: ['./office-ai-agent.component.less']
})

export class OfficeAiAgentComponent implements OnInit {
    formattedDate: string;
    costChartOption: EChartsOption;
    constructor(
        private applicationService: ApplicationService,
        private router: Router,
    ) {}

    bulb = false;
    search = false;
    isAdmin = window.localStorage.getItem('isAdmin')
    isArchiveUser = window.localStorage.getItem('isArchiveUser')
    isLoading: boolean = false;

    busyStatus = {};

    title = window.localStorage.getItem('title');
    username = window.localStorage.getItem('username');

    // 文本框内容
    inputMessage = '';

    private initCostChart() {
        // 生成过去12个月的时间标签
        const months = [];
        const now = new Date();
        for (let i = 11; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            months.push(
                `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}`
            );
        }

        // 生成更真实的成本数据
        const generateCostData = () => {
            const data = [];
            let baseValue = 15000; // 初始基准值
            for (let i = 0; i < 12; i++) {
                // 模拟业务波动
                const seasonFactor = i % 12 === 11 ? 1.3 : 1; // 年末高峰
                const randomFactor = 0.9 + Math.random() * 0.2; // ±10%波动
                const trendFactor = 1 + (i * 0.03); // 每月3%增长趋势

                const value = baseValue * seasonFactor * randomFactor * trendFactor;
                data.push(Number(value.toFixed(2)));

                baseValue = value * 0.98; // 轻微衰减
            }
            // return data;
            return 0;
        };

        // 生成配额数据示例
        const quotaData = {
            cpu: { used: 85, total: 100 }, // 单位：核
            memory: { used: 256, total: 512 }, // 单位：GB
            storage: { used: 2048, total: 4096 } // 单位：GB
        };

        // 模拟数据（需替换为真实数据）
        // const mockData = Array.from({length: 12}, () => Math.random() * 10000);
        const mockData = generateCostData();
        this.costChartOption = {
            xAxis: {
                type: 'category',
                data: months,
                axisLabel: { rotate: 0 }
            },
            yAxis: {
                type: 'value',
                name: '每月费用趋势（¥）',
                axisLabel: {
                    formatter: (value: number) => `¥${value.toFixed(0)}`
                }
            },
            grid: {
                containLabel: true,
                left: '3%',
                right: '4%',
                bottom: '12%'
            },
            series: [{
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2,
                    shadowColor: 'rgba(21,94,239,0.2)',
                    shadowBlur: 10,
                    shadowOffsetY: 5
                },
                emphasis: { // 高亮样式
                    itemStyle: {
                        color: '#ff9900',
                        borderColor: '#fff',
                        borderWidth: 2
                    }
                },
                data: mockData,
                type: 'line',
                smooth: true,
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#155eef' },
                            { offset: 1, color: 'rgba(21,94,239,0.1)' }
                        ])
                    }
                } as any, // 添加类型断言
                itemStyle: {
                    normal: {
                        color: '#155eef'
                    }
                } as any // 添加类型断言
            }] as any[],

            tooltip: {
                trigger: 'axis',
                formatter: (params: any[]) => {
                    const date = months[params[0].dataIndex];
                    const value = params[0].value;
                    return `
          ${date}<br/>
          <span style="color:#155eef">●</span> 当月费用：¥${Number(value).toFixed(2)}
        `;
                }
            },

        } as EChartsOption;
    }

    ngOnInit() {
        this.initCostChart();
        this.formattedDate = formatDate(new Date(), 'yyyy年MM月dd日 EEEE', 'zh-CN');
        this.getDataList();
    }
    tableData = [];
    getDataList() {
        let params = {pageSize: 3,pageNum: 1}
        this.isLoading = true;
        this.applicationService.query(params)
            .then(rs => {
                if (rs.success) {
                    if(rs.data){
                        this.tableData =  rs.data.dataList || [];
                    }else{
                        this.tableData =  [];
                    }
                } else {
                    // this.msg.error(`获取配额列表失败${ rs.message ? ': ' + rs.message : '' }`);
                }

                this.isLoading = false;
            })
            .catch(msg => {
                // this.msg.error('获取配额列表失败');
                this.isLoading = false;
            });
    }

    forword(url: string) {
        this.router.navigate([url], {replaceUrl: true });
        return undefined;
    }

    /**
     * 跳转到知识问答页面
     */
    goToKnowledgeQuiz() {
        this.router.navigate(['/console/office-agent/knowledge-quiz']);
    }

    /**
     * 处理文本框回车事件
     */
    onKeyDown(event: KeyboardEvent) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.submitMessage();
        }
    }

    /**
     * 提交消息并跳转到知识问答页面
     */
    submitMessage() {
        if (!this.inputMessage.trim()) {
            return;
        }

        // 跳转到知识问答页面并传递文本内容
        this.router.navigate(['/console/office-agent/knowledge-quiz'], {
            queryParams: { message: this.inputMessage.trim() }
        });
    }
}

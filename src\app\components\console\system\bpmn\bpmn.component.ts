import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 流程状态枚举
export const ProcessStatusMap = {
    'draft': '草稿',
    'published': '已发布',
    'suspended': '已暂停',
    'archived': '已归档'
};

@Component({
    selector: 'app-bpmn',
    templateUrl: './bpmn.component.html',
    styleUrls: ['./bpmn.component.less']
})
export class BpmnComponent implements OnInit {
    constructor(
        private router: Router,
        private msg: NzMessageService,
        private modal: NzModalService,
        private bpmnService: BpmnService
    ) {}

    isLoading: boolean = false;
    keyword: string = '';
    tableData = [];
    busyStatus = {};

    // 配置弹窗相关
    configModalVisible: boolean = false;
    editData: any = null;
    
    // 分页配置
    pager = {
        page: 1,
        pageSize: 10,
        total: 0
    };

    // 查询过滤条件
    filters = {
        pageNum: 0,
        pageSize: 10,
        orderBy1: '',
        orderName1: ''
    };

    orderBy1: '';
    orderName1: '';

    ngOnInit(): void {
        this.getDataList();
    }

    // 获取数据列表
    getDataList() {
        this.isLoading = true;
        const params = {
            ...this.filters,
        };

        this.bpmnService.query(params).then(res => {
            this.isLoading = false;
            if (res.success) {
                this.tableData = res.data.dataList || [];
                this.pager.total = res.data.total || 0;
            } else {
                this.msg.error(res.message || '获取流程列表失败');
            }
        }).catch(err => {
            this.isLoading = false;
            this.msg.error('获取流程列表失败');
            console.error(err);
        });
    }

    // 搜索
    search() {
        this.pager.page = 1;
        this.filters.pageNum = 0;
        this.getDataList();
    }

    // 分页变化
    pageChanged(page: number) {
        this.pager.page = page;
        this.filters.pageNum = page - 1;
        this.getDataList();
    }

    // 表格参数变化
    onParamsChange(params: NzTableQueryParams) {
        const { pageSize, pageIndex, sort } = params;
        const currentSort = sort.find(item => item.value !== null);
        
        this.pager.pageSize = pageSize;
        this.pager.page = pageIndex;
        this.filters.pageSize = pageSize;
        this.filters.pageNum = pageIndex - 1;
        
        if (currentSort) {
            this.filters.orderName1 = currentSort.key;
            this.filters.orderBy1 = currentSort.value === 'ascend' ? 'true' : 'false';
        } else {
            // this.filters.orderName1 = '';
            // this.filters.orderBy1 = '';
        }
        
        this.getDataList();
    }

    // 新增流程
    addProcess() {
        // 跳转到编辑器页面
        this.router.navigate(['/console/system/bpmn-editor']);
    }

    // 编辑流程
    editProcess(data: any) {
        // 跳转到编辑器页面，传递流程ID
        this.router.navigate(['/console/system/bpmn-editor', data.id]);
    }

    // 设计流程（新增方法）
    designProcess(data: any) {
        // 跳转到编辑器页面进行流程设计
        this.router.navigate(['/console/system/bpmn-editor', data.id]);
    }

    // 查看流程
    viewProcess(data: any) {
        // 跳转到查看页面
        this.router.navigate(['/console/system/bpmn-view', data.id]);
    }

    // 删除流程
    deleteProcess(data: any) {
        this.modal.confirm({
            nzTitle: '确认删除',
            nzContent: `确定要删除流程"${data.name}"吗？`,
            nzOnOk: () => {
                this.busyStatus[data.id] = true;
                return this.bpmnService.delete(data.id).then(res => {
                    this.busyStatus[data.id] = false;
                    if (res.success) {
                        this.msg.success('删除成功');
                        this.getDataList();
                    } else {
                        this.msg.error(res.message || '删除失败');
                    }
                }).catch(err => {
                    this.busyStatus[data.id] = false;
                    this.msg.error('删除失败');
                    console.error(err);
                });
            }
        });
    }

    // 发布流程
    publishProcess(data: any) {
        this.busyStatus[data.id] = true;
        this.bpmnService.publish(data.id).then(res => {
            this.busyStatus[data.id] = false;
            if (res.success) {
                this.msg.success('发布成功');
                this.getDataList();
            } else {
                this.msg.error(res.message || '发布失败');
            }
        }).catch(err => {
            this.busyStatus[data.id] = false;
            this.msg.error('发布失败');
            console.error(err);
        });
    }

    // 暂停流程
    suspendProcess(data: any) {
        this.busyStatus[data.id] = true;
        this.bpmnService.suspend(data.id).then(res => {
            this.busyStatus[data.id] = false;
            if (res.success) {
                this.msg.success('暂停成功');
                this.getDataList();
            } else {
                this.msg.error(res.message || '暂停失败');
            }
        }).catch(err => {
            this.busyStatus[data.id] = false;
            this.msg.error('暂停失败');
            console.error(err);
        });
    }

    // 获取流程状态显示文本
    getProcessStatus(status: string): string {
        return ProcessStatusMap[status] || status;
    }

    // 格式化日期
    formatDate(date: string): string {
        if (!date) return '-';
        return new Date(date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 跟踪函数
    trackById(index: number, item: any): any {
        return item.id;
    }

    // 配置保存回调
    onConfigSave() {
        this.getDataList();
    }
}

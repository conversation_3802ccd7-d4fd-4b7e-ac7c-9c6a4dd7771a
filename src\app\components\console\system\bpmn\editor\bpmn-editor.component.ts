import { Component, OnInit, On<PERSON><PERSON><PERSON>, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js相关模块
import BpmnModeler from 'bpmn-js/lib/Modeler';
// 暂时注释掉属性面板和翻译模块，先确保基本功能正常
// import { BpmnPropertiesPanelModule, CamundaPlatformPropertiesProviderModule } from 'bpmn-js-properties-panel';
// import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda.json';
// import TranslateModule from 'src/app/utils/bpmn-translate';

@Component({
    selector: 'app-bpmn-editor',
    templateUrl: './bpmn-editor.component.html',
    styleUrls: ['./bpmn-editor.component.less']
})
export class BpmnEditorComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;
    // 暂时注释掉属性面板引用
    // @ViewChild('propertiesPanel', { static: false }) propertiesPanel!: ElementRef;

    private bpmnModeler: any;
    public processId: string | null = null;
    public processData: any = null;
    public loading = false;
    public saving = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.processId = this.route.snapshot.paramMap.get('id');

        if (this.processId) {
            this.loadProcessData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initBpmnModeler();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.bpmnModeler) {
            this.bpmnModeler.destroy();
        }
    }

    // 加载流程数据
    private async loadProcessData(): Promise<void> {
        if (!this.processId) return;

        try {
            this.loading = true;
            const response = await this.bpmnService.getById(Number(this.processId));

            if (response.success) {
                this.processData = response.data;

                // 如果BPMN建模器已初始化，导入XML
                if (this.bpmnModeler) {
                    this.importBpmnXml(this.processData.bpmnXml || this.defaultBpmnXml);
                }
            } else {
                this.msg.error('加载流程数据失败');
            }
        } catch (error) {
            console.error('加载流程数据失败:', error);
            this.msg.error('加载流程数据失败');
        } finally {
            this.loading = false;
        }
    }

    // 初始化BPMN建模器
    private initBpmnModeler(): void {
        if (!this.bpmnContainer) {
            console.error('BPMN容器未找到');
            return;
        }

        try {
            // 创建BPMN建模器实例 - 使用最基本的配置
            this.bpmnModeler = new BpmnModeler({
                container: this.bpmnContainer.nativeElement
                // 暂时移除所有额外配置，确保基本功能正常
                // propertiesPanel: {
                //     parent: this.propertiesPanel.nativeElement
                // },
                // additionalModules: [
                //     BpmnPropertiesPanelModule,
                //     CamundaPlatformPropertiesProviderModule
                // ],
                // moddleExtensions: {
                //     camunda: camundaModdleDescriptor
                // }
            });

            // 导入BPMN XML
            const xmlToImport = this.processData?.bpmnXml || this.defaultBpmnXml;
            this.importBpmnXml(xmlToImport);

            // 监听建模器事件
            this.setupEventListeners();

        } catch (error) {
            console.error('初始化BPMN建模器失败:', error);
            this.msg.error('初始化BPMN建模器失败');
        }
    }

    // 设置事件监听器
    private setupEventListeners(): void {
        if (!this.bpmnModeler) return;

        // 监听元素变化事件
        this.bpmnModeler.on('commandStack.changed', () => {
            // 可以在这里添加自动保存逻辑
        });
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        try {
            await this.bpmnModeler.importXML(xml);

            // 自适应画布大小
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败');
        }
    }

    // 保存流程
    async save(): Promise<void> {
        try {
            this.saving = true;

            // 获取BPMN XML
            const result = await this.bpmnModeler.saveXML({ format: true });
            const bpmnXml = result.xml;

            // 获取SVG图像
            const svgResult = await this.bpmnModeler.saveSVG();
            const svgImage = svgResult.svg;

            // 准备保存数据
            const saveData = {
                id: this.processId,
                bpmnXml: bpmnXml,
                svgImage: svgImage
            };

            // 调用服务保存
            const response = await this.bpmnService.saveProcess(saveData);

            if (response.success) {
                this.msg.success('保存成功');
            } else {
                this.msg.error(response.message || '保存失败');
            }

        } catch (error) {
            console.error('保存流程失败:', error);
            this.msg.error('保存流程失败');
        } finally {
            this.saving = false;
        }
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/bpmn']);
    }

    // 缩放到适合视口
    zoomToFit(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');
        }
    }

    // 重置缩放
    zoomReset(): void {
        if (this.bpmnModeler) {
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom(1);
        }
    }
}
// floating box style

@size: 38px;
@cubeBorderColor: rgba(24, 183, 244, 0.85);
@borderWidth: 2px;

.banner-sub-img {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 560px;
    width: 100vw * (560 / 1903);
    background-image: url(src/assets/images/banner-pic1.jpg);
    background-position: center center;
    background-size: 100% auto;
    background-repeat: no-repeat;
}

.cube-box {
    height: @size;
    position: absolute;
    margin: 30px 29px;
    width: @size;
    color: red;
    transform-style: preserve-3d;
    animation: floating 2s ease-in-out 0s infinite;

    &:before {
        content: '';
        background-image: linear-gradient(rgba(7,110,249, 0.5), rgba(0, 0, 0, 0));
        position: absolute;
        top: 100%;
        left: -6px;
        right: -6px;
        height: 70px;
        transform: rotateY(-45deg);
        animation: scaling 2s ease-in-out 0s infinite;
    }
}

.box-1 {
    left: 26px;
    top: 58px;
}

.box-2 {
    right: -5px;
    top: 115px;
    animation: floating2 2.5s ease-in-out 0s infinite;
}

.box-3 {
    left: -15px;
    bottom: 85px;
    animation: floating3 1.8s ease-in-out 0s infinite;
    &:before {
        height: 100px;
        animation: scaling3 2s ease-in-out 0s infinite;
    }
}

.fade {
    .face {
        border-color: rgb(15,121,178);
    }
}

.face {
    background-color: rgba(26, 40, 77, 0.5);
    border: @borderWidth solid @cubeBorderColor;
    border-radius: 6px;
    height: @size;
    position: absolute;
    width: @size;
}

.cube-box .one  {
    transform: rotateX(90deg) translateZ(@size/2 - 2px);
    border-left: none;
    border-bottom: none;
    line-height: @size / 3 - 1px;
    text-align: center;
    padding-top: 2px;
    padding-right: 1px;
}
  
.cube-box .two {
    transform: translateZ(@size/2 - 2px);
    border-left: none;
    border-top: none;
}
  
.cube-box .three {
    transform: rotateY(90deg) translateZ(@size/2 - 2px);
    border-right: none;
    border-bottom: none;
}
  
.cube-box .four {
    transform: rotateY(180deg) translateZ(@size/2 - 2px);
    border-left: none;
    border-bottom: none;
}
  
.cube-box .five {
    transform: rotateY(-90deg) translateZ(@size/2 - 2px);
    border-top: none;
    border-right: none;
}
  
.cube-box .six {
    transform: rotateX(-90deg) translateZ(@size/2 - 2px) rotate(180deg);
    border-top: none;
    border-left: none;
    background-color: rgba(6, 31, 207, 0.92);
}

.cube-box .seven {
    transform: rotateX(-90deg)  rotate(180deg);
    border-top: none;
    border-left: none;
    border-radius: 4px;
    background-image: linear-gradient(-45deg, rgba(6, 31, 207, 0.92), #1A284D);
}

.circle {
    display: inline-block;
    border: @borderWidth solid @cubeBorderColor;
    border-radius: 50%;
    width: floor(@size / 3) - 1px;
    height:  floor(@size / 3) - 1px;
    margin: 2px;
}

@keyframes scaling {
    0% {
        height: 70px;
    }

    50% {
        height: 50px;
    }

    100% {
        height: 70px;

    }
}

@keyframes scaling3 {
    0% {
        height: 50px;
    }

    50% {
        height: 100px;
    }

    100% {
        height: 50px;
    }
}

@keyframes floating {
    0% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(0px);
    }

    50% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(5px);
    }

    100% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(0px);
    }
}

@keyframes floating2 {
    0% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(0px);
    }

    50% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(8px);
    }

    100% {
        transform: rotateX(-37deg) rotateY(45deg) translateY(0px);
    }
}

@keyframes floating3 {
    0% {
        transform: scale(0.85) rotateX(-37deg) rotateY(45deg) translateY(0px);
    }

    50% {
        transform: scale(0.85) rotateX(-37deg) rotateY(45deg) translateY(-5px);
    }

    100% {
        transform: scale(0.85) rotateX(-37deg) rotateY(45deg) translateY(0px);
    }
}
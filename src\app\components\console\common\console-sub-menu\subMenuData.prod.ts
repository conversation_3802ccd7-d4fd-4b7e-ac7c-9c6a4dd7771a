export const subMenuData = {
    cloudServer: [
        {
            name: 'instance',
            title: '实例',
            routerLink: 'instance',
        },
        // 20200520 子menu暂时隐藏
        // {
        //     name: 'storage',
        //     title: '存储',
        //     children: [
        //         {
        //             name: 'cloudDisk',
        //             title: '云盘',
        //             routerLink: 'cloud-disk',
        //         }
        //     ]
        // },
        {
            name: 'snapshoot',
            title: '快照',
            routerLink: 'snapshot',
        },
        // {
        //     name: 'backup',
        //     title: '备份',
        //     routerLink: 'backup',
        // }
        // ,
        {
            name: 'monitor',
            title: '监控',
            routerLink: 'monitor',
        }
        // 20200520 子menu暂时隐藏
        ,
        {
            name: 'mirror',
            title: '自定义镜像',
            routerLink: 'mirror',
        }
        ,
        {
            name: 'secretKeyPair',
            title: '密钥对',
            routerLink: 'secret-key-pair'
        }
        ,
        {
            name: 'recover',
            title: '回收站',
            routerLink: 'recover'
        },
        {
            name: 'bareMetal',
            title: '裸金属',
            routerLink: 'bare-metal'
        },
        //     name: 'networkSecurity',
        //     title: '网络安全',
        //     children: [
        //         {
        //             name: 'secretKeyPair',
        //             title: '密钥对',
        //             routerLink: 'secret-key-pair'
        //         },
        //         {
        //             name: 'securityGroup',
        //             title: '安全组',
        //             routerLink: 'security-group'
        //         },
        //     ],
        // }
    ],
    clouddisk: [
        {
            name: 'index',
            title: '云盘',
            routerLink: 'index',
        },
    ],
    elasticPubNetworkIp: [],
    loadBalancing: [],
    proprietaryNetwork: [
        {
            name: 'index',
            title: '专有网络',
            routerLink: 'index',
        },
        {
            name: 'securityGrouop',
            title: '安全组',
            routerLink: 'security-group',
        },
        {
            name: 'elasticPubNetworkIp',
            title: '弹性公网IP',
            routerLink: 'elastic-pub-network-ip',
        },
        // {
        //     name: 'firewall',
        //     title: '防火墙',
        //     routerLink: 'firewall',
        // },
    ],
    // officeAgent: [],
    redis: [],
    security: [
        // {
        //     name: 'securityType',
        //     title: '安全组件类型',
        //     children: [
        // {
        //     name: 'cloudcssp',
        //     title: '云安全管理平台',
        //     routerLink: 'index/CLOUD_CSSP'
        // },
        // {
        //     name: 'cloudantitampering',
        //     title: '网页防篡改',
        //     routerLink: 'index/CLOUD_ANTITAMPERING'
        // },
        {
            name: 'cloudwebfirewall',
            title: 'WEB应用防火墙',
            routerLink: 'CLOUD_WEB_FIREWALL'
        },
        {
            name: 'cloudfirewall',
            title: '云下一代防火墙',
            routerLink: 'CLOUD_FIREWALL'
        },
        {
            name: 'cloudserversecurity',
            title: '云主机安全',
            routerLink: 'CLOUD_SERVER_SECURITY'
        },
        {
            name: 'cloudsslvpn',
            title: 'SSLVPN',
            routerLink: 'CLOUD_SSLVPN'
        },
        {
            name: 'cloudlogaudit',
            title: '日志审计',
            routerLink: 'CLOUD_LOG_AUDIT'
        },
        {
            name: 'cloudvulnerabilityscan',
            title: '漏洞扫描',
            routerLink: 'CLOUD_VULNERABILITY_SCAN'
        },
        {
            name: 'cloudbastionhost',
            title: '云堡垒机',
            routerLink: 'CLOUD_BASTION_HOST'
        },
        {
            name: 'clouddatabaseaudit',
            title: '数据库审计',
            routerLink: 'CLOUD_DATABASE_AUDIT'
        },
        // {
        //     name: 'cloudintrusionprevention',
        //     title: '入侵防御系统',
        //     routerLink: 'index/CLOUD_INTRUSION_PREVENTION'
        // },
        // {
        //     name: 'situationawareness',
        //     title: '态势感知',
        //     routerLink: 'index/SITUATION_AWARENESS'
        // },
        {
            name: 'cloudDDoS',
            title: '抗DDoS',
            routerLink: 'CLOUD_DDOS'
        },
        //     ],
        // }
    ],
    database: [
        {
            name: 'redis',
            title: 'Redis',
            routerLink: 'redis',
        },
        {
            name: 'rds',
            title: 'RDS',
            routerLink: 'rds',
        },
    ],
    dataSecurity: [],
    securityPolicy: [],
    disasterRecovery: [],
    objectStorage: [],
    fileStorage: [],
    rds: [],
    messageQueues: [
        {
            name: 'Kafka',
            title: 'Kafka',
            routerLink: 'kafka'
        },
        {
            name: 'RabbitMQ',
            title: 'RabbitMQ',
            routerLink: 'rabbitmq'
        },
    ],
    container: [
        {
            name: 'workload',
            title: 'Workload',
            routerLink: 'workload'
        },
        {
            name: 'kubernetes',
            title: '集群管理',
            routerLink: 'kubernetes'
        },
        {
            name: 'harbor',
            title: 'Harbor(私有)',
            routerLink: 'harbor'
        },
        {
            name: 'harborPub',
            title: 'Harbor(公有)',
            routerLink: 'harbor_pub'
        }
    ],
    elasticScaling: [
        {
            name: 'scalingGroups',
            title: '弹性伸缩组',
            routerLink: 'scaling-groups'
        }
    ],
    monitor: [
        {
            name: 'marketMonitor',
            title: '监控大盘',
            routerLink: 'market-monitor'
        },
        {
            name: 'marketManage',
            title: '告警管理',
            routerLink: 'monitor-manage'
        },
        {
            name: 'marketMessage',
            title: '告警通知',
            routerLink: 'monitor-message'
        }
    ],
    user: [
        {
            name: 'message',
            title: '消息通知',
            routerLink: 'message'
        },
        // {
        //     name: 'expenseCenter',
        //     title: '费用中心',
        //     children: [
        //         {
        //             name: 'accountOverview',
        //             title: '账户总览',
        //             routerLink: 'account-overview',
        //         },
        //         {
        //             name: 'consumeRecord',
        //             title: '消费记录',
        //             routerLink: 'consume-record',
        //         }
        //     ]
        // },
        {
            name: 'workOrder',
            title: '工单管理',
            children: [
                {
                    name: 'workOrders',
                    title: '我的工单',
                    routerLink: 'work-orders'
                },
                {
                    name: 'submitWorkOrder',
                    title: '发起工单',
                    routerLink: 'submit-work-order'
                }
            ]
        }
    ],
    devops: [],
    vpn: [],
    cloudLog: [
        {
            name: 'install',
            title: '安装部署引导',
            routerLink: 'install'
        },
        {
            name: 'admin',
            title: '日志管理',
            routerLink: 'admin'
        },
        {
            name: 'warning',
            title: '警告日志',
            routerLink: 'warning'
        }
    ],
    // monitorManager: [
    //     {
    //         name: 'cloudAudit',
    //         title: '操作日志',
    //         routerLink: 'cloud-audit'
    //     },
    /*{
        name: 'cloudLog',
        title: '云日志',
        children: [
            {
                name: 'install',
                title: '安装部署引导',
                routerLink: 'cloud-log/install'
            },
            {
                name: 'admin',
                title: '日志管理',
                routerLink: 'cloud-log/admin'
            },
            {
                name: 'warning',
                title: '警告日志',
                routerLink: 'cloud-log/warning'
            }
        ],
    }*/
    // ],
    // 物理机
    // bareMetal: [],

    // 云专线
    cloudProvider: [],
    // 备份
    vmbackup: [
        {
            name: 'policymanage',
            title: '备份策略',
            routerLink: 'policymanage',
        },
        {
            name: 'restorelist',
            title: '备份恢复记录',
            routerLink: 'restorelist',
        }
    ],
    // VPC云内互联
    vpcConnect: [],
    // 专属云
    exclusiveServer: [],
    system: [
        {
            name: 'management',
            title: '服务器配置',
            routerLink: 'index',
        },
        {
            name: 'cloudAudit',
            title: '操作日志',
            routerLink: 'cloud-audit'
        },
        {
            name: 'account',
            title: '账户管理',
            routerLink: 'account',
        },
        {
            name: 'order',
            title: '任务管理',
            routerLink: 'order',
        },
        {
            name: 'rule',
            title: '权限管理',
            routerLink: 'permission',
            // children: [
            //     {
            //         name: 'permission',
            //         title: '默认权限',
            //         routerLink: 'permission'
            //     },
            //     {
            //         name: 'submitWorkOrder',
            //         title: '用户权限',
            //         routerLink: 'submit-work-order'
            //     }
            // ]
        },
        {
            name: 'quota',
            title: '配额管理',
            // routerLink: 'quota',
            children: [
                {
                    name: 'quota',
                    title: '资源总览',
                    routerLink: 'quota'
                },
                {
                    name: 'user-template',
                    title: '默认配额',
                    routerLink: 'user-template',
                },
            ]
        },
        {
            name: 'bpmnManagement',
            title: '流程管理',
            routerLink: 'bpmn',
        },
    ],
    appSystem: [
        {
            name: 'index',
            title: '项目管理',
            routerLink: 'index',
        },
        {
            name: 'application',
            title: '额度申请',
            routerLink: 'application',
        }
    ],

    modelServicePlatform: [
        {
            name: 'model-market',
            title: '模型集市',
            routerLink: 'model-market',
        },
        {
            name: 'model-management',
            title: '模型管理',
            routerLink: 'model-management',
        }
    ],


    cloudMonitor: [
        {
            name: 'monitor-list',
            title: '应用监控',
            routerLink: 'monitor-list',
        },
        {
            name: 'huawei',
            title: '华为云',
            routerLink: 'monitor-brand/huawei',
        },
        {
            name: 'aliyun',
            title: '阿里云',
            routerLink: 'monitor-brand/aliyun',
        },
    ],
    cost: [
        {
            name: 'cost',
            title: '成本中心',
            routerLink: 'cost-center',
        },
        {
            name: 'usage-record',
            title: '明细账单',
            routerLink: 'usage-record',
        }
    ],
};

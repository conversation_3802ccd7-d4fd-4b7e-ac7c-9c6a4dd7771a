@import '../../../../style/common/_variable.less';
@import 'highlight.js/styles/github.css';

ul,ul li, ul li ul, ul li ul li {
    line-height: 15px !important;
    list-style-type: disc;
}
.content-body-item {
    display: inline-block;
    width: 100%;
    min-width: 1600px;
}
.page-box {
    text-align: right;
}
.panel-container {
    height: 100px;
}
.panel-images {
    width: 100px;
    height: 100%;
    vertical-align: top;
    // border: 1px solid red;
    display: inline-block;
}
.panel-mid {
    width: 75%;
    height: 100%;
    display: inline-block;   
    vertical-align: top;
    // border: 1px solid blue;
}
.panel-mid-item {
    height: 50%;
    // background: yellow;
    span {
        width: 95%;;
        font-size: 16px;
        display: inline-block;
        margin-left: 8px;
        margin-top: 8px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
    }
    .content {
        font-size: 14px;
        color: @gray;
    }
}
.panel-right {
    width: 18%;
    height: 100%;
    display: inline-block;    
    // border: 1px solid black;
    position: relative;
    color: @gray;
    .panel-right-top {
        position: absolute;
        top: 0px;
        right: 0px;
        &:hover {
            color: @red;
        }
        // background: red;
        span {
            display: inline-block;
            margin-left: 10px;
        }
        i {
            font-size: 16px;
        }
    }
    .panel-right-bottom {
        position: absolute;
        bottom: 0px;
        right: 0px;
        p:nth-child(1) {
            margin: 0;
            text-align: right;
            i {
                margin-top: -2px;
            }
        }
        i {
            margin-right: 5px;
        } 
    }
}
.expand-container {
    .expand-header {
        width: 160px;
    }
    .expand-des {
        width: 90%;
        // white-space: pre-line;
    }
}
.tag-des {
    display: inline-block;
    width: 300px;
    margin-top: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    label {
        display: inline-block;
        width: 280px;
        margin: 0;
        overflow: hidden;
        vertical-align: middle;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    i {
        // margin-bottom: 30px;
        cursor: pointer;
        margin-left: 5px;
        &:hover {
            color: @blue;
        }
    }
    &:nth-child(1) {
        vertical-align: middle;
        margin-right: 40px;
    }
    &:nth-child(2) {
        vertical-align: top;
    }
}
.tag-right {
    float: right;
    text-align: right;
    i {
        cursor: pointer;
        &:hover {
            color: @red;
        }
    }
}
.select-tips {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 20px;
    width: 100px;
    color: #666;
}
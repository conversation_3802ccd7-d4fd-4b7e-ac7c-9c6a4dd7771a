<div class="table-content">
    <div class="on-panel">
        <div class="on-panel-header">
            <form nz-form nzLayout="inline" (ngSubmit)="search()">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" name="keyword"
                           autocomplete="off"
                           [(ngModel)]="keyword" nz-input
                           placeholder="请输入表单名称" />
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch>
                        <i nz-icon nzType="search"></i>
                    </button>
                </ng-template>
            </form>
            <div class="right-button-group">
                <div class="pull-right">
                    <a nz-button nzType="primary" class="primary" (click)="addForm()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        新增表单
                    </a>
                </div>
            </div>
        </div>
        <div class="on-panel-body">
            <div class="action-bar clearfix">
                <span class="title">表单设计</span>
            </div>
            <nz-table #tableList
                      [nzItemRender]="renderItemTemplate"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="tableData"
                      [nzLoading]="isLoading">
                <thead>
                    <tr>
                        <th nzSortKey="name" [nzSortFn]="true">表单名称</th>
                        <th nzSortKey="formKey" [nzSortFn]="true">表单标识</th>
                        <th nzSortKey="version" [nzSortFn]="true">版本</th>
                        <th nzSortKey="status" [nzSortFn]="true">状态</th>
                        <th nzSortKey="createTime" [nzSortFn]="true">创建时间</th>
                        <th nzSortKey="updateTime" [nzSortFn]="true">更新时间</th>
                        <th>描述</th>
                        <th width="20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of tableList.data; trackBy: trackById">
                        <td>{{data.name}}</td>
                        <td>{{data.formKey || '-'}}</td>
                        <td>{{data.version || '1.0'}}</td>
                        <td>
                            <nz-tag [nzColor]="data.status === 'published' ? 'green' : 
                                              data.status === 'suspended' ? 'orange' : 
                                              data.status === 'archived' ? 'red' : 'default'">
                                {{getFormStatus(data.status)}}
                            </nz-tag>
                        </td>
                        <td>{{formatDate(data.createTime)}}</td>
                        <td>{{formatDate(data.updateTime)}}</td>
                        <td>{{data.description || '-'}}</td>
                        <td>
                            <div class="on-table-actions" [hidden]="busyStatus[data.id]">
                                <div class="on-table-action-item" (click)="viewForm(data)">
                                    <i nzTooltipTitle="查看表单"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-chakan"
                                       style="color: #52c41a;">
                                    </i>
                                </div>
                                <div class="on-table-action-item" (click)="designForm(data)">
                                    <i nzTooltipTitle="设计表单"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-bianji"
                                       style="color: #1890ff;">
                                    </i>
                                </div>
                                <div class="on-table-action-item" (click)="editForm(data)">
                                    <i nzTooltipTitle="编辑信息"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont iconfontBianji3 icon-bianji3">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     *ngIf="data.status === 'draft'"
                                     (click)="publishForm(data)">
                                    <i nzTooltipTitle="发布"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-fabu">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     *ngIf="data.status === 'published'"
                                     (click)="suspendForm(data)">
                                    <i nzTooltipTitle="暂停"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont icon-zanting">
                                    </i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipPlacement="top"
                                     nzPopconfirmTitle="确定要删除该表单吗？"
                                     (nzOnConfirm)="deleteForm(data)">
                                    <i nzTooltipTitle="删除"
                                       nzTooltipPlacement="bottom"
                                       nz-tooltip
                                       class="icon iconfont iconfontShanchu3 icon-shanchu3">
                                    </i>
                                </div>
                            </div>
                            <div class="on-table-actions" [hidden]="!busyStatus[data.id]">
                                <i nz-icon nzType="loading" nzTheme="outline"></i>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
            <ng-template #renderItemTemplate let-type let-page="page">
                <a *ngIf="type === 'prev'">« 上一页</a>
                <a *ngIf="type === 'next'">下一页 »</a>
                <a *ngIf="type === 'page'">{{ page }}</a>
            </ng-template>

        </div>
    </div>
</div>

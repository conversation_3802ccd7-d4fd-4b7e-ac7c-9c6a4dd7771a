import { Component, OnInit } from '@angular/core';

declare let $: any;

@Component({
    selector: 'app-object-storage',
    templateUrl: './object-storage.component.html',
    styleUrls: ['./object-storage.component.less']
})
export class ObjectStorageComponent implements OnInit {
    constructor(
    ) {}

    intro = {
        title: '对象存储',
        enName: 'AIC Object Storage',
        desc: 'OOS（AIC Object Storage，AIC对象存储）是面向企业和开发者的具有高安全、高可靠、大容量、低成本等特点的对象存储产品，用于存储图片、音视频、文档等非结构化数据，并实现在线管理数据。存储空间可以根据业务系统承载情况按需使用、灵活伸缩。支持高并发访问，具有完备的API及SDK接口，帮助用户数据快速上云。',
        bgColor: '#1a284d',
        orderLink: '/console/object-storage/index',
        type: 'objectStorageProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

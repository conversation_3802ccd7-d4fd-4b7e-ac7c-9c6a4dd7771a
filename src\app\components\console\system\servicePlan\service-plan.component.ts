import { Component, OnInit } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { ServicePlanService } from 'src/app/service/console/system/service-plan.service';

// 服务计划类型枚举
export const ServicePlanTypeMap = {
    'ecs': '云服务器',
    'evs': '存储',
    'k8s': 'K8S',
    'vdi': '云桌面',
    'rds': 'RDS',
    'redis': 'REDIS',
    'msg': 'MSG'
};

@Component({
    selector: 'app-service-plan',
    templateUrl: './service-plan.component.html',
    styleUrls: ['./service-plan.component.less']
})
export class ServicePlanComponent implements OnInit {
    constructor(
        private msg: NzMessageService,
        private modal: NzModalService,
        private servicePlanService: ServicePlanService
    ) {}

    isLoading: boolean = false;
    keyword: string = '';
    tableData = [];
    busyStatus = {};

    // 配置弹窗相关
    configModalVisible: boolean = false;
    editData: any = null;
    
    // 分页配置
    pager = {
        page: 1,
        pageSize: 10,
        total: 0
    };

    // 查询过滤条件
    filters = {
        pageNum: 0,
        pageSize: 10,
        sortName: '',
        sortOrder: ''
    };

    // 排序配置
    sortName = '';
    sortValue = '';

    ngOnInit(): void {
        this.getDataList();
    }

    // 获取数据列表
    getDataList(filters?) {
        filters = filters || this.filters;
        let keyword = this.keyword.trim();
        let params = Object.assign({}, filters);

        // 初始化bean对象
        params.bean = params.bean || {};

        // 添加名称搜索条件
        if (keyword) {
            params.bean.name = keyword;
        }

        this.isLoading = true;
        this.servicePlanService.query(params)
        .then(rs => {
            if (rs.success) {
                this.tableData = rs.data.dataList || [];
                console.log(this.tableData)
                this.pager = {
                    page: rs.data.pageNum + 1,
                    pageSize: rs.data.pageSize,
                    total: rs.data.recordCount,
                };
            } else {
                this.msg.error(`获取服务计划列表失败${ rs.message ? ': ' + rs.message : '' }`);
            }

            this.isLoading = false;
        })
        .catch(err => {
            this.msg.error('获取服务计划列表失败');
            this.isLoading = false;
        });
    }

    // 搜索
    search() {
        this.filters.pageNum = 0;
        this.getDataList();
    }

    // 分页变化
    pageChanged(pageNum) {
        this.filters.pageNum = pageNum - 1;
        this.getDataList();
    }

    // 表格查询参数变化
    onParamsChange(params: NzTableQueryParams): void {
        const { pageSize, pageIndex, sort } = params;
        const currentSort = sort.find(item => item.value !== null);
        
        this.filters.pageNum = pageIndex - 1;
        this.filters.pageSize = pageSize;
        
        if (currentSort) {
            this.filters.sortName = currentSort.key;
            this.filters.sortOrder = currentSort.value === 'ascend' ? 'asc' : 'desc';
        } else {
            this.filters.sortName = '';
            this.filters.sortOrder = '';
        }
        
        this.getDataList();
    }

    // 获取服务计划类型显示文本
    getServicePlanType(type: string): string {
        return ServicePlanTypeMap[type] || type;
    }

    // 格式化日期
    formatDate(dateStr: string): string {
        if (!dateStr) return '-';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN');
    }

    // 格式化Region显示
    formatRegions(regions: any[]): string {
        if (!regions || regions.length === 0) {
            return '-';
        }
        
        if (regions.length <= 3) {
            return regions.map(r => r.regionName).join('、');
        } else {
            return regions.slice(0, 3).map(r => r.regionName).join('、') + '...';
        }
    }

    // 新增服务计划
    addServicePlan() {
        this.editData = null;
        this.configModalVisible = true;
    }

    // 编辑服务计划
    editServicePlan(item) {
        this.editData = item;
        this.configModalVisible = true;
    }

    // 配置弹窗保存回调
    onConfigSave() {
        this.getDataList();
    }

    // 删除服务计划
    deleteServicePlan(item) {
        this.busyStatus[item.id] = 'delete';
        this.servicePlanService.delete(item.id)
        .then(rs => {
            if (rs.success) {
                this.msg.success('删除成功');
                this.getDataList();
            } else {
                this.msg.error(`删除失败${ rs.message ? ': ' + rs.message : '' }`);
            }
            this.busyStatus[item.id] = '';
        })
        .catch(err => {
            this.msg.error('删除失败');
            this.busyStatus[item.id] = '';
        });
    }

    // 跟踪函数
    trackById(index: number, item: any): any {
        return item.id;
    }
}

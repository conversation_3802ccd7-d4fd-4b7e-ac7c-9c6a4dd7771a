/*弹出框*/
.popup-window {
    z-index: 100000;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    transition: opacity 0.3s 0s, visibility 0s 0.3s;
}
.popup-window.is-visible-window {
    opacity: 1;
    visibility: visible;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
    transition: opacity 0.3s 0s, visibility 0s 0s;
}
.popup-container-window {
    position: relative;
    width: 350px;
    margin: 8em auto;
    height: 180px;
    /*padding: 20px;*/
    background: #FFF;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
    -webkit-backface-visibility: hidden;
    -webkit-transition-property: -webkit-transform;
    -moz-transition-property: -moz-transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -ms-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}
#changeNameWindow>.popup-container-window, #resetPassWindow>.popup-container-window {
    width: 400px;
    height: 300px;
}
#changeCharacterWindow>.popup-container-window {
    width: 400px;
    height: 250px;
}
.popup-close-window {
    position: absolute;
    right: 1em;
    top: 0.8em;
    z-index: 10;
    font-size: 20px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: #E01B2F;
}
.cd-popup-close-middle {
    cursor: pointer;
}
.is-visible-window .popup-container-window {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform:scale(1);
    -o-transform:scale(1);
    transform:scale(1);
}
.pupup-content-title {
    font-size: 20px;
}
.form-label-tips {
    font-size: 12px;
    color: #999;
    margin-bottom: 20px;
}
.radio-container {
    margin: 20px 0;
    /*padding: 20px;*/
}
.input-container {
    padding: 20px;
}
.radio-inline {
    font-size: 14px;
    margin-right: 10px;
    margin-left: 0;
}
.btn-container {
    position: absolute;
    bottom: 20px;
    padding: 0 20px;
}
.btn-container button {
    margin-right: 15px;
    outline: none;
}
/*二级菜单*/
.operation-container:hover .dropdown-menu {
    display: block;
}
.operation-submenu-item {
    height: 30px;
    line-height: 30px;
    margin: 3px 0;
    padding: 0 10px;
    cursor: pointer;
    color: #666;
}
.operation-submenu-item:hover {
    background: #F5F5F5;
    color: #2D77EE;
}
.operation-submenu-item-icon {
    font-size: 16px;
    margin: 0 8px;
    cursor: pointer;
}
.operation-submenu-item-icon:hover {
    color: #2D77EE;
}
/*创建账号*/
.form-item {
    margin-top: 30px;
    margin-bottom: 20px;
}
.choose-btn {
    margin-right: 10px;
    outline: none;
}
.action-btn {
    background: #007BFF;
    color: #fff;
}
.form-label-tips {
    font-size: 12px;
    color: #999;
    margin-bottom: 20px;
}
.add-operation-btn {
    width: 100px;
    height: 30px;
    margin-top: 20px;
    outline: none;
    border-radius: 5px;
    margin-right: 30px;
    cursor: pointer;
}

.action-bar {
    button {
        width: 120px;
    }
    
    button + button {
        margin-left: 10px;
    }
}
.submit-btn {
    border: 1px solid #3c73b9;
    background: #2D77EE;
    color: #fff;
}
.cancel-btn {
    border: 1px solid #3c73b9;
    background: #FFF;
    color: #2D77EE;
}
/*穿梭框样式*/
.database-accredit-container {
    width: 100%;
    min-width: 1200px;
    height: 250px;
    /*background: red;*/
}
.larger-container {
    display: inline-block;
    width: 50%;
    min-width: 400px;
    height: 100%;
    margin-right: 2%;
    // border-radius: 5px;
    border: 1px solid #ccc;
    vertical-align: middle;
}
.smaller-container {
    display: inline-block;
    width: 25%;
    min-width: 400px;
    height: 100%;
    // border-radius: 5px;
    border: 1px solid #ccc;
    vertical-align: middle;
}
.database-accredit-header {
    display: inline-block;
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding: 0 20px;
    font-size: 12px;
    background: #F2F3F5;
}
.header-l1 {
    color: #999;
}
/*下拉选框*/
.database-accredit-select {
    float: right;
    border: none;
    background: #F2F3F5;
    outline: none;
    line-height: 30px;
    font-size: 14px;
    margin-top: 5px;
}
.database-accredit-body {
    padding: 10px 5px;
}
.database-accredit-body-list-item {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
}
.database-accredit-body-list-item-title {
    width: 70%;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
}
.database-accredit-body-list-item-operation-container {
    font-weight: lighter;
}
.database-accredit-body-list {
    list-style-type: none;
    color: #666;
    height: 200px;
    overflow-y: scroll;
    font-size: 12px;
    margin: 0;
    padding: 0 10px;
}
.database-radio {
    /*background: red;*/
    vertical-align: middle;
    margin: 0 5px;
    cursor: pointer;
}
.database-radio>input {
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    margin-top: -2px;
}
.database-accredit-body-list-item-operation-container>.fa-times-circle-o {
    /*color: #DBE3EE;*/
    font-size: 16px;
    vertical-align: middle;
    cursor: pointer;
}
.database-accredit-body-list-item-hover {
    cursor: pointer;
}
.database-accredit-body-list-item-hover:hover {
    color: #2D77EE;
}
/*修改权限弹出框*/
#changeJurisdictionWindow>.popup-container-window {
    width: 1100px;
    height: 500px;
    /*background: red;*/
}
#changeJurisdictionWindow>.popup-container-window>.form-item {
    padding: 20px;
}
/*弹出框*/
.popup-window2 {
    z-index: 100000;
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0.3s;
    transition: opacity 0.3s 0s, visibility 0s 0.3s;
}
.popup-window2.is-visible-window {
    opacity: 1;
    visibility: visible;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
    transition: opacity 0.3s 0s, visibility 0s 0s;
}
.popup-container-window2 {
    position: relative;
    width: 80px;
    margin: 20em auto;
    height: 80px;
    /*padding: 20px;*/
    /*background: #FFF;*/
    -webkit-transform: scale(0.8);
    -moz-transform: scale(0.8);
    -ms-transform: scale(0.8);
    -o-transform: scale(0.8);
    transform: scale(0.8);
    -webkit-backface-visibility: hidden;
    -webkit-transition-property: -webkit-transform;
    -moz-transition-property: -moz-transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    -moz-transition-duration: 0.3s;
    -ms-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
}
.popup-container-window2 .fa {
    color: #fff;
    font-size: 65px;
}
.nav-link {
    cursor: pointer;
}
.warning-text {
    color: #E01B2F;
}
.disabled-btn:hover {
    color: #aaa;
}
.disabled-btn a{
    color: #aaa;
    cursor: not-allowed;
}
//弹出框
.select-tips {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 20px;
    width: 100px;
    color: #666;
}
.warning-tips {
    width: 350px;
    margin-left: 115px;
    margin-top: 5px;
    font-size: 12px;
    color: #f5222d;
}

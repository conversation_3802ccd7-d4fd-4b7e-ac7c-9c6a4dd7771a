import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { dr2cFailover } from 'src/app/service/common/URL';
import { AccountService } from 'src/app/service/console/system/account.service';

@Component({
    selector: 'app-system-account-edit',
    templateUrl: './edit.component.html',
    styleUrls: ['./edit.component.less']
})
export class AccountEditComponent implements OnInit {
    constructor(
        private fb: FormBuilder,
        private msg: NzMessageService,
        private accountService: AccountService,
    ) { }

    @Output() refreshParent = new EventEmitter<void>();
    @Input() isVisible: boolean = false;
    @Input() type: string = 'add';
    @Input() bean = {
        id: '',
        name: '',
        password: '',
        tenantAdmin: false
    };

    @Output() close = new EventEmitter<boolean>();
    @Output() submit = new EventEmitter<boolean>();

    user: FormGroup;
    roleList: any;

    isCreating: boolean = false;
    formSubmitAttempt: boolean = false;

    ngOnInit() {
        this.user = this.fb.group({
            name: ['', {
                validators: [
                    Validators.required,
                    Validators.maxLength(50),
                ]
            }],
            password: ['', {
                validators: [
                    Validators.required,
                    Validators.maxLength(50),
                ]
            }],
            tenantAdmin: ['', {
                validators: [
                    Validators.required,
                ]
            }],
            displayName: [''],
            email: ['', {
                validators: [
                    Validators.email,
                    Validators.maxLength(100),
                ]
            }],
        });
        this.roleList =  [{key: true, value: '管理员'}, {key: false, value: '用户'}];
    }

    modalOpened() {
    }

    handleCancel() {
        this.formSubmitAttempt = false;
        this.user.reset();
        this.close.emit(true);
    }

    addOrUpdate() {
        if (this.user.valid) {
            let data = this.user.value;
            this.accountService.addOrUpdate(data)
                .then(rs => {
                    if (rs.success) {
                        this.msg.success(`账户保存成功`);

                        this.submit.emit(true);
                        this.handleCancel();
                        this.isCreating = false;
                        this.refreshParent.emit();
                    } else {
                        this.msg.error(`账户保存失败${ rs.message ? ': ' + rs.message : '' }`);
                        this.isCreating = false;
                    }
                })
                .catch(err => {
                    this.msg.error(`账户保存失败`);
                    this.isCreating = false;
                });
        }else{
            this.user.markAllAsTouched();
        }

    }

    isInvalid(fc: AbstractControl): boolean {
        if(fc == null){
            return false;
        }
        return (!fc.valid && fc.touched) ||
            (fc.untouched && this.formSubmitAttempt);
    }

}

// BPMN查看器样式
.bpmn-viewer-embedded {
  height: calc(100vh - 20px);
  min-height: 600px;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;

  // 顶部工具栏
  .bpmn-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;
    height: 56px;

    .toolbar-left {
      display: flex;
      align-items: center;
      flex: 1;

      .back-btn {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background: #f0f0f0;
        }

        i {
          margin-right: 4px;
        }
      }

      .divider {
        margin: 0 12px;
        color: #d9d9d9;
      }

      .process-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
    }

    .toolbar-center {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;

      .view-mode-indicator {
        display: flex;
        align-items: center;
        padding: 4px 12px;
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        border-radius: 4px;
        color: #1890ff;
        font-size: 12px;

        i {
          margin-right: 4px;
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      justify-content: flex-end;

      nz-button-group {
        button {
          border-radius: 0;

          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }

          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
        }
      }
    }
  }

  // 查看器容器
  .bpmn-viewer-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 500px;

    &.loading {
      pointer-events: none;
    }

    .bpmn-main-content {
      display: flex;
      width: 100%;
      height: 100%;

      .bpmn-canvas {
        flex: 1;
        height: 100%;
        position: relative;
        cursor: grab;
        user-select: none;

        &:active {
          cursor: grabbing;
        }

        // bpmn-js 样式覆盖
        :global(.djs-container) {
          background: #fff;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          cursor: inherit;

          // 确保画布可以接收鼠标事件
          pointer-events: auto;

          // 确保画布可以被拖拽
          svg {
            cursor: inherit;
            pointer-events: auto;
          }
        }

        // 隐藏工具面板（查看模式不需要）
        :global(.djs-palette) {
          display: none;
        }

        // 隐藏上下文菜单（查看模式不需要）
        :global(.djs-context-pad) {
          display: none;
        }

        // 选中元素样式
        :global(.djs-shape.selected) {
          .djs-outline {
            stroke: #1890ff;
            stroke-width: 2px;
            stroke-dasharray: none;
          }
        }

        :global(.djs-connection.selected) {
          .djs-outline {
            stroke: #1890ff;
            stroke-width: 2px;
          }
        }

        // 悬停样式
        :global(.djs-shape:hover) {
          .djs-outline {
            stroke: #40a9ff;
            stroke-width: 1px;
          }
        }

        // 缩放控件样式
        :global(.djs-zoom-controls) {
          display: none; // 隐藏默认缩放控件，使用自定义的
        }

        // 画布拖拽相关样式
        :global(.djs-drag-active) {
          cursor: grabbing !important;
        }

        // 滚轮缩放提示
        &::before {
          content: '';
          position: absolute;
          top: 10px;
          right: 10px;
          width: 0;
          height: 0;
          z-index: 10;
          pointer-events: none;
        }

        // 添加拖拽提示（可选）
        &::after {
          content: '拖拽移动画布 • 滚轮缩放 • Ctrl+滚轮精确缩放';
          position: absolute;
          bottom: 10px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          opacity: 0;
          transition: opacity 0.3s;
          pointer-events: none;
          z-index: 10;
        }

        &:hover::after {
          opacity: 1;
        }
      }
    }

    // 加载遮罩
    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
  }

  // 底部状态栏
  .bpmn-statusbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;
    height: 40px;
    font-size: 12px;
    color: #666;

    .statusbar-left {
      display: flex;
      align-items: center;

      .status-item {
        display: flex;
        align-items: center;
        margin-right: 16px;

        i {
          margin-right: 4px;
          color: #1890ff;
        }
      }
    }

    .statusbar-right {
      display: flex;
      align-items: center;

      .status-item {
        margin-left: 16px;
        color: #999;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bpmn-viewer-embedded {
    .bpmn-toolbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .toolbar-left,
      .toolbar-center,
      .toolbar-right {
        margin: 4px 0;
        flex: none;
        width: 100%;
      }

      .toolbar-center {
        order: 3;
        justify-content: flex-start;
      }

      .toolbar-right {
        justify-content: flex-start;
      }
    }

    .bpmn-statusbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .statusbar-left,
      .statusbar-right {
        width: 100%;
        justify-content: center;
        margin: 2px 0;
      }

      .statusbar-right {
        .status-item {
          margin: 0 8px;
        }
      }
    }
  }
}
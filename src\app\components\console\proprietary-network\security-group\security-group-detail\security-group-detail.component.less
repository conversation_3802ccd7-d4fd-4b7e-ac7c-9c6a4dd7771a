.pagination {
    margin: 10px 0;
}

.pagination li > a {
    border: solid 1px #ddd;
    height: 32px;
}
.CS_box .CSbox-title{
    float: left;
}
.CSbox-top .CS-choose{
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #e8e8e8;
}

.CS-choose div{
    cursor: pointer;
    float: left;
    width: 80px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
}
.choose_active{
    border-top: 2px solid #0083FF;
    color: #0083FF;
    background-color: #ffffff;
    border-right: 1px solid #e8e8e8;
    border-left: 1px solid #e8e8e8;
}

.choose_active2{
    background-color: #f6f6f6;
    border: 1px solid #e8e8e8;
}

.tc-content .col-special-1{
    margin-left: 20px;
    margin-top: 1px;
}


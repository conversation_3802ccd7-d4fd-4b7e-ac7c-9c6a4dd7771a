import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormService } from 'src/app/service/console/system/form.service';

// 导入form-js相关模块
import { FormEditor } from '@bpmn-io/form-js-editor';

// 导入中文翻译模块
import { translateFormEditor, cleanupTranslation, retranslate, debugTranslatableTexts } from './zh-cn';
import { FormJSTranslator } from './form-js-translator';

@Component({
    selector: 'app-form-editor',
    templateUrl: './form-editor.component.html',
    styleUrls: ['./form-editor.component.less']
})
export class FormEditorComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('formContainer', { static: false }) formContainer!: ElementRef;
    @ViewChild('paletteContainer', { static: false }) paletteContainer!: ElementRef;
    @ViewChild('propertiesPanel', { static: false }) propertiesPanel!: ElementRef;

    private formEditor: any;
    public formId: string | null = null;
    public formData: any = null;
    public loading = false;
    public saving = false;

    // 表单名称
    public formName: string = '新建表单';

    // 保存原始表单数据（用于编辑模式下的重置功能）
    private originalFormData: any = null;

    // 翻译器实例
    private translator: FormJSTranslator | null = null;

    // 默认表单schema - 新增模式下为空白表单
    private defaultFormSchema = {
        type: 'default',
        id: 'NewForm',
        components: [], // 移除默认的文本字段，让用户从空白表单开始设计
        schemaVersion: 12
    };

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private formService: FormService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.formId = this.route.snapshot.paramMap.get('id');

        if (this.formId) {
            this.loadFormData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initFormEditor();
        }, 100);
    }

    ngOnDestroy(): void {
        // 清理翻译器
        if (this.translator) {
            this.translator.destroy();
            this.translator = null;
        }

        // 清理翻译观察器
        if (this.formContainer) {
            cleanupTranslation(this.formContainer.nativeElement);
        }

        if (this.formEditor) {
            this.formEditor.destroy();
        }
    }

    // 加载表单数据
    private async loadFormData(): Promise<void> {
        try {
            this.loading = true;

            // 调用服务加载表单数据
            const response = await this.formService.get(Number(this.formId));

            if (response.success) {
                this.formData = response.data;
                this.formName = this.formData.name || '新建表单';
                // 保存原始数据的深拷贝，用于重置功能
                this.originalFormData = JSON.parse(JSON.stringify(response.data));
            } else {
                this.msg.error(response.message || '加载表单数据失败');
                // 使用默认数据
                this.formData = {
                    id: this.formId,
                    name: '新建表单',
                    schema: this.defaultFormSchema
                };
                this.formName = '新建表单';
            }
        } catch (error) {
            console.error('加载表单数据失败:', error);
            this.msg.error('加载表单数据失败');
            // 使用默认数据
            this.formData = {
                id: this.formId,
                name: '新建表单',
                schema: this.defaultFormSchema
            };
            this.formName = '新建表单';
        } finally {
            this.loading = false;
        }
    }

    // 初始化表单编辑器
    private initFormEditor(): void {
        if (!this.formContainer) {
            return;
        }

        try {
            // 创建表单编辑器实例
            this.formEditor = new FormEditor({
                container: this.formContainer.nativeElement,
                // form-js会自动创建palette和properties panel
                // 我们只需要提供主容器
                // 可以添加其他配置选项
                exporter: {
                    name: 'Form Editor',
                    version: '1.0.0'
                }
            });

            // 导入表单schema
            const schemaToImport = this.formData?.schema || this.defaultFormSchema;
            this.importFormSchema(schemaToImport);

            // 监听编辑器事件
            this.setupEventListeners();

            // 启动汉化功能 - 等待form-js完全加载
            setTimeout(() => {
                this.initializeTranslation();
            }, 2000); // 增加延迟确保form-js完全渲染

        } catch (error) {
            console.error('初始化表单编辑器失败:', error);
            this.msg.error('初始化表单编辑器失败');
        }
    }

    // 设置事件监听器
    private setupEventListeners(): void {
        if (!this.formEditor) return;

        // 监听表单变化事件
        this.formEditor.on('selection.changed', (event: any) => {
            // 选择变化时重新翻译，确保新显示的内容被汉化
            setTimeout(() => {
                this.retranslateContent();
            }, 100);
        });

        // 监听属性面板事件
        this.formEditor.on('propertiesPanel.updated', (event: any) => {
            // 属性更新时重新翻译
            setTimeout(() => {
                this.retranslateContent();
            }, 100);
        });

        // 监听编辑器导入完成事件
        this.formEditor.on('import.done', () => {
            setTimeout(() => {
                this.retranslateContent();
            }, 200);
        });

        // 监听编辑器渲染完成事件
        this.formEditor.on('rendered', () => {
            setTimeout(() => {
                this.retranslateContent();
            }, 100);
        });

        // 监听元素选择事件
        this.formEditor.on('element.click', () => {
            setTimeout(() => {
                this.retranslateContent();
            }, 50);
        });
    }

    // 初始化翻译功能
    private initializeTranslation(): void {
        if (this.formContainer) {
            // 使用新的翻译器
            this.translator = new FormJSTranslator(this.formContainer.nativeElement);

            // 调试：打印所有可翻译的文本
            setTimeout(() => {
                debugTranslatableTexts(this.formContainer.nativeElement);
            }, 1000);

            // 保留原有翻译方法作为备用
            translateFormEditor(this.formContainer.nativeElement);
        }
    }

    // 重新翻译内容
    private retranslateContent(): void {
        if (this.formContainer) {
            retranslate(this.formContainer.nativeElement);
        }
    }

    // 手动触发翻译（用于调试）
    manualTranslate(): void {
        if (this.formContainer) {
            // 使用新翻译器
            if (this.translator) {
                this.translator.forceTranslate();
            }

            // 调试信息
            debugTranslatableTexts(this.formContainer.nativeElement);
            retranslate(this.formContainer.nativeElement);
            this.msg.success('翻译已执行，请查看控制台输出');
        }
    }

    // 导入表单schema
    private async importFormSchema(schema: any): Promise<void> {
        try {
            await this.formEditor.importSchema(schema);
        } catch (error) {
            console.error('导入表单schema失败:', error);
            this.msg.error('导入表单schema失败');
        }
    }

    // 保存表单
    async save(): Promise<void> {
        try {
            this.saving = true;

            // 获取表单schema
            const schema = this.formEditor.saveSchema();

            // 准备保存数据
            const saveData = {
                id: this.formId,
                name: this.formName || '新建表单',
                description: this.formData?.description || '',
                schema: schema
            };

            // 调用服务保存
            const response = await this.formService.saveForm(saveData);

            if (response.success) {
                this.msg.success('保存成功');
                // 如果是新建表单，更新formId
                if (!this.formId && response.data?.id) {
                    this.formId = response.data.id.toString();
                    this.formData.id = response.data.id;
                }
                // 更新formData中的name字段
                if (this.formData) {
                    this.formData.name = this.formName;
                }

                // 保存成功后返回列表页面
                setTimeout(() => {
                    this.router.navigate(['/console/system/form']);
                }, 1000); // 延迟1秒让用户看到成功提示
            } else {
                this.msg.error(response.message || '保存失败');
            }

        } catch (error) {
            console.error('保存表单失败:', error);
            this.msg.error('保存表单失败');
        } finally {
            this.saving = false;
        }
    }

    // 表单名称失去焦点时的处理
    onFormNameBlur(): void {
        this.validateFormName();
    }

    // 表单名称按回车键时的处理
    onFormNameEnter(event: any): void {
        event.target.blur(); // 失去焦点，触发验证
    }

    // 验证表单名称
    private validateFormName(): void {
        if (!this.formName || this.formName.trim() === '') {
            this.formName = '新建表单';
        } else {
            this.formName = this.formName.trim();
        }

        // 更新formData中的name字段
        if (this.formData) {
            this.formData.name = this.formName;
        }
    }

    // 下载表单JSON
    async downloadJson(): Promise<void> {
        try {
            const schema = this.formEditor.saveSchema();
            const json = JSON.stringify(schema, null, 2);

            const blob = new Blob([json], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.formData?.name || 'form'}.json`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载JSON文件失败:', error);
            this.msg.error('下载JSON文件失败');
        }
    }

    // 预览表单
    async preview(): Promise<void> {
        try {
            const schema = this.formEditor.saveSchema();

            // 在新窗口中打开预览
            const previewData = {
                schema: schema,
                data: {} // 可以提供测试数据
            };

            // 将数据存储到sessionStorage中
            sessionStorage.setItem('formPreviewData', JSON.stringify(previewData));
            const left = (window.screen.availWidth - 800) / 2 + window.screenX;
            const top = (window.screen.availHeight - 800) / 2 + window.screenY;
            // 打开预览窗口（独立页面）
            const previewUrl = `/form-preview/${this.formId || 'new'}`;
            window.open(previewUrl, '_blank', 'width=800,height=800,left='+left+',top='+top);

        } catch (error) {
            console.error('预览表单失败:', error);
            this.msg.error('预览表单失败');
        }
    }

    // 重置表单
    reset(): void {
        try {
            if (this.originalFormData && this.originalFormData.schema) {
                // 编辑模式：恢复到原始数据
                this.importFormSchema(this.originalFormData.schema);
                this.formName = this.originalFormData.name || '新建表单';
                this.msg.success('表单已恢复到编辑前状态');
            } else {
                // 新建模式：清空画布，使用默认空白schema
                this.importFormSchema(this.defaultFormSchema);
                this.formName = '新建表单';
                this.msg.success('表单已重置为空白状态');
            }
        } catch (error) {
            console.error('重置表单失败:', error);
            this.msg.error('重置表单失败');
        }
    }

    // 导入表单JSON
    importJson(): void {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (event: any) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e: any) => {
                    try {
                        const schema = JSON.parse(e.target.result);
                        this.importFormSchema(schema);
                        this.msg.success('导入成功');
                    } catch (error) {
                        console.error('导入JSON失败:', error);
                        this.msg.error('导入JSON失败，请检查文件格式');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/form']);
    }
}

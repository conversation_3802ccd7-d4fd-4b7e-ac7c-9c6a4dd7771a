import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-home-page',
    templateUrl: './home-page.component.html',
    styleUrls: ['./home-page.component.less']
})
export class HomePageComponent implements OnInit {
    constructor() { }

    banner = {
        title: 'AIC应用托管平台全新上线',
        desc: '全新的应用托管服务，助力物联网客户快速上云',
        bgColor: '#1a284d',
        subImg: './assets/images/banner-pic1.jpg',
    }

    productList = [
        {
            icon: 'yfwq-bg',
            name: '云服务器',
            desc: '安全可靠，性价比高，帮助用户打造灵活高效的、适用各类应用场景的弹性云服务器',
            routerLink: '/introduction/cloud-server'
        },
        {
            icon: 'yp-bg',
            name: '云盘',
            desc: '持久化、大容量、高可靠的块级数据存储服务，支持动态挂载、扩容、快照、回滚等功能，满足云服务器的数据存储需求',
            routerLink: '/introduction/cloud-disk'
        },
        {
            icon: 'txgw-bg',
            name: '弹性公网IP',
            desc: '独立的公网IP，可动态绑定和解绑，实现服务对外部网络可用，满足客户向公网客户提供服务的需求',
            routerLink: '/introduction/elastic-public-network-ip'
        },
        {
            icon: 'zywl-bg',
            name: '专有网络',
            desc: '安全隔离、灵活、易扩展的网络环境，满足服务之间安全风险隔离的需求',
            routerLink: '/introduction/proprietary-network'
        },
        {
            icon: 'fzjh-bg',
            name: '负载均衡',
            desc: '对多台云服务器进行流量分发，扩展对外服务能力，消除单点故障，提升应用系统的可用性',
            routerLink: '/introduction/load-balance'
        },
        {
            icon: 'rds-bg',
            name: 'RDS',
            desc: '高可靠、高可用的MySQL、MariaDB关系型数据库服务，提供了双机热备、故障恢复、业务监控，安全隔离等特性',
            routerLink: '/introduction/rds'
        },
        {
            icon: 'dxcc-bg',
            name: '对象存储',
            desc: '高安全、高可靠、大容量、低成本等特点的对象存储产品，用于存储图片、音视频、文档等非结构化数据，并实现在线管理数据',
            routerLink: '/introduction/object-storage'
        },
        {
            icon: 'txss-bg',
            name: '弹性伸缩',
            desc: '基于伸缩策略的弹性伸缩功能，帮助用户按需使用，降低成本，实现自动化运维。',
            routerLink: '/introduction/elastic-scaling'
        },
        {
            icon: 'wjcc-bg',
            name: '文件存储',
            desc: '通用可靠、灵活扩展、性价比高，满足客户实现文件共享、大文件存储、数据汇总的业务需求。',
            routerLink: '/introduction/file-storage'
        },
        {
            icon: 'xxdl-bg',
            name: '消息队列',
            desc: '100%兼容开源 Kafka API（2.0.X及以上），帮助用户快速尚云。作为消息中间件，帮助用户实现消息生产者与消费者之间的解耦，无需彼此等待',
            routerLink: '/introduction/message-queues'
        },
        {
            icon: 'rqfw-bg',
            name: '容器服务',
            desc: '提供高性能容器化管理能力的基于原生Kubernetes的容器管理服务及存储、分发Docker镜像，一键部署，开箱即用的私有镜像仓库服务(Harbor)',
            routerLink: '/introduction/container'
        },
        {
            icon: 'xnpt-bg',
            name: '云效平台',
            desc: '一站式企业研发平台，为企业用户提供从“需求->开发->测试->发布->运维”端到端的协同服务和研发工具支撑。',
            routerLink: '/introduction/devops'
        },
        // {
        //     icon: 'yjk-bg',
        //     name: '云监控',
        //     desc: '获取云主机的监控指标，探测服务可用性，以及针对指标设置警报。实时掌握资源及应用的运行状态，保证服务及应用稳定运行。',
        //     routerLink: '/introduction/monitor'
        // }
    ]

    ngOnInit() {}

    isLastRow(index) {
        return index >= Math.floor(this.productList.length / 5) * 5;
    }

    isLastOne(index) {
        return index === this.productList.length - 1 && this.productList.length % 5 !== 0;
    }
}

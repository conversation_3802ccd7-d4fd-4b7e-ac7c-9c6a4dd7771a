<div class="table-content">
    <!-- 虚拟机池列表视图 -->
    <div class="on-panel" *ngIf="currentView === 'pool'">
        <div class="on-panel-header">
            <div class="on-panel-title">
                <span>虚拟机池</span>
            </div>
            <div class="on-panel-actions">
                <div class="action-group">
                    <button nz-button nzType="default" (click)="refresh()">
                        <i nz-icon nzType="reload" nzTheme="outline"></i>
                        刷新
                    </button>
                    <button nz-button nzType="primary" (click)="showCreateModal()">
                        <i nz-icon nzType="plus" nzTheme="outline"></i>
                        创建虚拟机池
                    </button>
                </div>
            </div>
        </div>

        <div class="on-panel-body">
            <!-- 搜索栏 -->
            <div class="search-bar">
                <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
                    <input type="text" nz-input placeholder="请输入虚拟机池名称" [(ngModel)]="keyword" (keyup.enter)="search()"/>
                </nz-input-group>
                <ng-template #suffixIconButton>
                    <button nz-button nzType="primary" nzSearch (click)="search()">
                        <i nz-icon nzType="search"></i>
                    </button>
                </ng-template>
            </div>

            <!-- 表格 -->
            <nz-table #vdiPoolTable
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="isLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="pager.total"
                      [nzPageIndex]="pager.page"
                      [nzPageSize]="pager.pageSize"
                      (nzPageIndexChange)="pageChanged($event)"
                      (nzQueryParams)="onParamsChange($event)"
                      [nzData]="vdiPoolList">
                <thead>
                    <tr>
                        <th nzSortKey="name">名称</th>
                        <th>虚拟机配置</th>
                        <th nzSortKey="imageName">镜像</th>
                        <th nzSortKey="vpcName">网络</th>
                        <th nzSortKey="vmCount">虚拟机数量</th>
                        <th>用户组</th>
                        <th nzSortKey="createTm">创建时间</th>
                        <th nzSortKey="architecture">架构名</th>
                        <th width="200px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let data of vdiPoolTable.data">
                        <td>
                            <a (click)="viewVdiList(data)" style="color: #1890ff; cursor: pointer;">
                                {{ data.name }}
                            </a>
                        </td>
                        <td>{{ data.servicePlanName }}</td>
                        <td>{{ data.imageName }}</td>
                        <td>{{ data.vpcName }}</td>
                        <td>{{ data.vmCount }}</td>
                        <td>{{ data.userGroup || '-' }}</td>
                        <td>{{ data.createTm | date:'yyyy-MM-dd HH:mm:ss' }}</td>
                        <td>{{ data.architecture }}</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item" (click)="bindUserGroup(data);">
                                    <i nzTooltipTitle="绑定用户组"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" (click)="unbindUserGroup(data);">
                                    <i nzTooltipTitle="解绑用户组"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="user-delete" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     [nzPopconfirmTitle]="'确定要删除该虚拟机池吗？'"
                                     (nzOnConfirm)="deleteVdiPool(data);"
                                >
                                    <i nzTooltipTitle="删除"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="delete" nzTheme="outline"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>

            <!-- 分页模板 -->
            <ng-template #renderItemTemplate let-type let-page="page">
                <ng-container [ngSwitch]="type">
                    <a *ngSwitchCase="'page'" class="ant-pagination-item-link">{{ page }}</a>
                </ng-container>
            </ng-template>
        </div>
    </div>

    <!-- VDI列表视图 -->
    <div class="on-panel" *ngIf="currentView === 'vdi'">
        <div class="on-panel-header">
            <div class="on-panel-title">
                <span>桌面虚拟机</span>
                <span *ngIf="currentPool" style="color: #666; font-size: 14px; margin-left: 10px;">
                    （虚拟机池：{{ currentPool.name }}）
                </span>
            </div>
            <div class="on-panel-actions">
                <div class="action-group">
                    <button nz-button nzType="default" (click)="backToPoolList()">
                        <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
                        返回虚拟机池列表
                    </button>
                    <button nz-button nzType="default" (click)="refreshVdi()">
                        <i nz-icon nzType="reload" nzTheme="outline"></i>
                        刷新
                    </button>
                </div>
            </div>
        </div>

        <div class="on-panel-body">
            <!-- VDI搜索栏 -->
            <div class="search-bar">
                <nz-input-group nzSearch [nzAddOnAfter]="vdiSuffixIconButton">
                    <input type="text" nz-input placeholder="请输入桌面虚拟机名称" [(ngModel)]="vdiKeyword" (keyup.enter)="searchVdi()"/>
                </nz-input-group>
                <ng-template #vdiSuffixIconButton>
                    <button nz-button nzType="primary" nzSearch (click)="searchVdi()">
                        <i nz-icon nzType="search"></i>
                    </button>
                </ng-template>
            </div>

            <!-- VDI表格 -->
            <nz-table #vdiTable
                      [nzItemRender]="renderItemTemplate"
                      [nzLoading]="vdiLoading"
                      [nzLoadingDelay]="300"
                      [nzFrontPagination]="false"
                      [nzTotal]="vdiPager.total"
                      [nzPageIndex]="vdiPager.page"
                      [nzPageSize]="vdiPager.pageSize"
                      (nzPageIndexChange)="vdiPageChanged($event)"
                      [nzData]="vdiList"
                      [nzScroll]="{ x: '1800px' }">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>镜像</th>
                        <th>用户名</th>
                        <th>IP地址</th>
                        <th>配置</th>
                        <th>数据盘</th>
                        <th>桌面状态</th>
                        <th>任务</th>
                        <th>电源</th>
                        <th>重启还原</th>
                        <th>GPU</th>
                        <th>共享</th>
                        <th>VIP</th>
                        <th>锁定</th>
                        <th>主机</th>
                        <th>组</th>
                        <th width="200px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of vdiTable.data">
                        <td>{{ item.name }}</td>
                        <td>{{ item.imageName || '-' }}</td>
                        <td>{{ item.account || '-' }}</td>
                        <td>{{ item.ipAddress || '-' }}</td>
                        <td>{{ item.cpu || '-' }} / {{ item.memory || '-' }}</td>
                        <td>{{ item.disk || '-' }}</td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-green': item.vdiStatus === 'ACTIVE', 'dot-gray': item.vdiStatus !== 'ACTIVE'}">
                                {{ getVdiStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.vdiTask || '-' }}</td>
                        <td>
                            <span class="dot"
                                  [ngClass]="{'dot-green': item.powerStatus === 'ACTIVE', 'dot-gray': item.powerStatus !== 'ACTIVE'}">
                                {{ getPowerStatusText(item) }}
                            </span>
                        </td>
                        <td>{{ item.resetOnReboot === true ? '是' : '否' }}</td>
                        <td>{{ item.gpuSupport === true ? '是' : '否' }}</td>
                        <td>{{ item.share === true ? '是' : '否' }}</td>
                        <td>{{ item.vip === true ? '是' : '否' }}</td>
                        <td>{{ item.locked === true ? '是' : '否' }}</td>
                        <td>{{ item.host || '-' }}</td>
                        <td>{{ item.group || '-' }}</td>
                        <td>
                            <div class="on-table-actions">
                                <div class="on-table-action-item" (click)="powerOnVdi(item);"
                                     *ngIf="item.powerStatus !== 'ACTIVE'">
                                    <i nzTooltipTitle="开机"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="play-circle" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" (click)="powerOffVdi(item);"
                                     *ngIf="item.powerStatus === 'ACTIVE'">
                                    <i nzTooltipTitle="关机"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="poweroff" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" (click)="rebootVdi(item);"
                                     *ngIf="item.powerStatus === 'ACTIVE'">
                                    <i nzTooltipTitle="重启"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="reload" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item" (click)="remoteDesktop(item);"
                                     *ngIf="item.powerStatus === 'ACTIVE'">
                                    <i nzTooltipTitle="远程桌面"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="desktop" nzTheme="outline"></i>
                                </div>
                                <div class="on-table-action-item"
                                     nz-popconfirm
                                     nzTooltipContent="top"
                                     [nzPopconfirmTitle]="'确定要删除该虚拟机吗？'"
                                     (nzOnConfirm)="deleteVdi(item);">
                                    <i nzTooltipTitle="删除"
                                       nzTooltipContent="bottom"
                                       nz-tooltip
                                       class="icon"
                                       nz-icon nzType="delete" nzTheme="outline"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </nz-table>
        </div>
    </div>
</div>

<!-- 创建虚拟机池弹框 -->
<nz-modal [(nzVisible)]="createModalVisible"
          nzTitle="创建虚拟机池"
          nzWidth="500px"
          (nzOnCancel)="cancelCreate()"
          (nzOnOk)="submitCreate()">
    <ng-container *nzModalContent>
        <form nz-form [formGroup]="createForm" nzLayout="vertical">
            <nz-form-item>
                <nz-form-label nzRequired>名称</nz-form-label>
                <nz-form-control nzErrorTip="请输入虚拟机池名称">
                    <input nz-input formControlName="name" placeholder="请输入虚拟机池名称" />
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label nzRequired>虚拟机配置</nz-form-label>
                <nz-form-control nzErrorTip="请选择虚拟机配置">
                    <nz-select formControlName="servicePlanId" nzPlaceHolder="请选择虚拟机配置">
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option *ngFor="let plan of initData.servicePlans"
                                   [nzValue]="plan.id"
                                   [nzLabel]="plan.name + ' (' + plan.servicePlanItems[0].servicePlanItemTypeTxt + ':' + plan.servicePlanItems[0].amount + ' ' + plan.servicePlanItems[1].servicePlanItemTypeTxt + ':' + plan.servicePlanItems[1].amount + ') ¥' + plan.price ">
                        </nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label nzRequired>镜像</nz-form-label>
                <nz-form-control nzErrorTip="请选择镜像">
                    <nz-select formControlName="imageId" nzPlaceHolder="请选择镜像">
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option *ngFor="let image of imageList" 
                                   [nzValue]="image.id" 
                                   [nzLabel]="image.name">
                        </nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label nzRequired>专有网络</nz-form-label>
                <nz-form-control nzErrorTip="请选择专有网络">
                    <nz-select formControlName="vpcId" 
                               nzPlaceHolder="请选择专有网络"
                               (ngModelChange)="selectVpc($event)">
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option *ngFor="let vpc of initData.vpc" 
                                   [nzValue]="vpc" 
                                   [nzLabel]="vpc.name">
                        </nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>

            <nz-form-item>
                <nz-form-label nzRequired>子网</nz-form-label>
                <nz-form-control nzErrorTip="请选择子网">
                    <nz-select formControlName="networkId"
                               nzPlaceHolder="请选择子网"
                               [nzDisabled]="!createForm.get('vpcId')?.value">
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option *ngFor="let network of createForm.get('vpcId')?.value?.network || []"
                                   [nzValue]="network.id"
                                   [nzLabel]="network.name">
                        </nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
        </form>
    </ng-container>
</nz-modal>

<!-- 绑定用户组弹框 -->
<nz-modal [(nzVisible)]="bindUserGroupModalVisible"
          nzTitle="绑定用户组"
          nzWidth="600px"
          (nzOnCancel)="cancelBindUserGroup()"
          (nzOnOk)="submitBindUserGroup()">
    <ng-container *nzModalContent>
        <div style="margin-bottom: 16px;">
            <strong>虚拟机池：</strong>{{ currentVdiPool?.name }}
        </div>
        <form nz-form [formGroup]="bindUserGroupForm" nzLayout="vertical">
            <nz-form-item>
                <nz-form-label nzRequired>选择用户组</nz-form-label>
                <nz-form-control nzErrorTip="请选择要绑定的用户组">
                    <nz-select formControlName="userGroupIds"
                               nzMode="multiple"
                               nzPlaceHolder="请选择用户组"
                               nzShowSearch
                               nzAllowClear
                               [nzMaxTagCount]="3"
                               nzMaxTagPlaceholder="...">
                        <nz-option *ngFor="let group of userGroupList"
                                   [nzValue]="group.id"
                                   [nzLabel]="getUserGroupDisplayName(group)">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>{{ group.name }}</span>
                                <span style="color: #999; font-size: 12px;">{{ group.memberCount }}人</span>
                            </div>
                        </nz-option>
                    </nz-select>
                    <div style="margin-top: 8px; color: #666; font-size: 12px;">
                        可以选择多个用户组，选中的用户组成员将可以访问此虚拟机池
                    </div>
                </nz-form-control>
            </nz-form-item>
        </form>
    </ng-container>
</nz-modal>

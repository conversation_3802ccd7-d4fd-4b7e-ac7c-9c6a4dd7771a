import { Component, OnInit, OnChanges, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, FormControl, AbstractControl } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ServicePlanService } from 'src/app/service/console/system/service-plan.service';
import { CloudServerService } from 'src/app/service/console/cloud-server/cloud-server.service';
import * as URL from 'src/app/service/common/URL';
import { RequestService } from 'src/app/service/common/request/request.service';

// 服务计划类型枚举
export const ServicePlanTypeMap = {
    'ecs': '云服务器',
    'evs': '存储',
    'k8s': 'K8S',
    'vdi': '云桌面',
    'rds': 'RDS',
    'redis': 'REDIS',
    'msg': 'MSG'
};

// 服务组件类型枚举
export const ServicePlanItemTypeMap = {
    'CPU': 'CPU',
    'DISK_GB': '磁盘(GB)',
    'MEMORY_GB': '内存(GB)',
    'BANDWIDTH_MB': '带宽(MB)',
    // 'IMAGE': '镜像'
};

// 服务组件子类型枚举
export const ServicePlanItemSubTypeMap = {
    'HDD': 'HDD',
    'SSD': 'SSD'
};

@Component({
    selector: 'app-service-plan-config',
    templateUrl: './service-plan-config.component.html',
    styleUrls: ['./service-plan-config.component.less']
})
export class ServicePlanConfigComponent implements OnInit, OnChanges {
    @Input() visible: boolean = false;
    @Input() editData: any = null;
    @Output() visibleChange = new EventEmitter<boolean>();
    @Output() onSave = new EventEmitter<void>();

    servicePlanForm: FormGroup;
    isLoading: boolean = false;
    regionList = [];
    imageList = [];

    // 枚举选项
    servicePlanTypeOptions = Object.keys(ServicePlanTypeMap).map(key => ({
        value: key,
        label: ServicePlanTypeMap[key]
    }));

    servicePlanItemTypeOptions = Object.keys(ServicePlanItemTypeMap).map(key => ({
        value: key,
        label: ServicePlanItemTypeMap[key]
    }));

    servicePlanItemSubTypeOptions = Object.keys(ServicePlanItemSubTypeMap).map(key => ({
        value: key,
        label: ServicePlanItemSubTypeMap[key]
    }));

    constructor(
        private fb: FormBuilder,
        private msg: NzMessageService,
        private servicePlanService: ServicePlanService,
        private cloudServerService: CloudServerService,
        private req: RequestService
    ) {
        this.initForm();
    }

    ngOnInit(): void {
        // 组件初始化时就加载数据，避免弹窗打开时的延迟
        this.loadRegionList();
        this.loadImageList();
    }

    ngOnChanges(): void {
        if (this.visible) {
            // 数据在ngOnInit时已经开始加载，这里直接处理表单
            if (this.editData) {
                // 如果数据还在加载中，等待加载完成
                this.waitForDataAndLoadForm();
            } else {
                this.resetForm();
            }
        }
    }

    // 等待数据加载完成后加载表单
    private waitForDataAndLoadForm() {
        const checkDataLoaded = () => {
            // 检查关键数据是否已加载（Region数据是必需的，镜像数据可以异步加载）
            if (this.regionList.length > 0) {
                this.loadFormData();
            } else {
                // 如果数据还没加载完，100ms后再检查
                setTimeout(checkDataLoaded, 100);
            }
        };
        checkDataLoaded();
    }

    // 初始化表单
    initForm() {
        this.servicePlanForm = this.fb.group({
            id: [null],
            name: ['', [Validators.required]],
            servicePlanType: ['', [Validators.required]],
            autoEffectiveDate: [null],
            autoExpiryDate: [null],
            price: [null, [Validators.required, Validators.min(0)]],
            remark: [''],
            servicePlanRegionRelations: [[]],
            servicePlanItems: this.fb.array([this.createServicePlanItem()])
        });
    }

    // 创建服务组件表单项
    createServicePlanItem(): FormGroup {
        const formGroup = this.fb.group({
            servicePlanItemType: ['', [Validators.required]],
            servicePlanItemSubType: [{value: '', disabled: true}],
            imageId: [{value: '', disabled: true}],
            amount: [1, [Validators.required, Validators.min(1)]]
        });

        return formGroup;
    }

    // 获取服务组件表单数组
    get servicePlanItems(): FormArray {
        return this.servicePlanForm.get('servicePlanItems') as FormArray;
    }

    // 添加服务组件
    addServicePlanItem() {
        this.servicePlanItems.push(this.createServicePlanItem());
    }

    // 检查是否可以添加更多组件
    canAddMoreItems(): boolean {
        return this.servicePlanItems.length < this.servicePlanItemTypeOptions.length;
    }

    // 删除服务组件
    removeServicePlanItem(index: number) {
        if (this.servicePlanItems.length > 1) {
            this.servicePlanItems.removeAt(index);
        }
    }

    // 设置服务组件字段状态（不改变值）
    setServicePlanItemFieldStates(formGroup: FormGroup, type: string) {
        // 如果类型为空，禁用所有相关字段
        if (!type) {
            formGroup.get('servicePlanItemSubType').disable();
            formGroup.get('servicePlanItemSubType').clearValidators();

            formGroup.get('imageId').disable();
            formGroup.get('imageId').clearValidators();

            formGroup.get('amount').enable();
            formGroup.get('amount').setValidators([Validators.required, Validators.min(1)]);
        } else {
            // 处理servicePlanItemSubType字段
            if (type === 'DISK_GB') {
                formGroup.get('servicePlanItemSubType').enable();
                formGroup.get('servicePlanItemSubType').setValidators([Validators.required]);
            } else {
                formGroup.get('servicePlanItemSubType').disable();
                formGroup.get('servicePlanItemSubType').clearValidators();
            }

            // 处理imageId字段
            if (type === 'IMAGE') {
                formGroup.get('imageId').enable();
                formGroup.get('imageId').setValidators([Validators.required]);
                // IMAGE类型时，amount禁用
                formGroup.get('amount').disable();
                formGroup.get('amount').clearValidators();
            } else {
                formGroup.get('imageId').disable();
                formGroup.get('imageId').clearValidators();
                // 非IMAGE类型时，amount启用
                formGroup.get('amount').enable();
                formGroup.get('amount').setValidators([Validators.required, Validators.min(1)]);
            }
        }

        formGroup.get('servicePlanItemSubType').updateValueAndValidity();
        formGroup.get('imageId').updateValueAndValidity();
        formGroup.get('amount').updateValueAndValidity();
    }

    // 服务组件类型变化处理
    onServicePlanItemTypeChange(index: number, type: string) {
        const item = this.servicePlanItems.at(index) as FormGroup;

        // 设置字段状态
        this.setServicePlanItemFieldStates(item, type);

        // 根据类型设置默认值
        if (!type) {
            item.get('servicePlanItemSubType').setValue('');
            item.get('imageId').setValue('');
            item.get('amount').setValue(1);
        } else {
            if (type !== 'DISK_GB') {
                item.get('servicePlanItemSubType').setValue('');
            }
            if (type !== 'IMAGE') {
                item.get('imageId').setValue('');
                item.get('amount').setValue(1);
            } else {
                // IMAGE类型时，清空amount
                item.get('amount').setValue('');
            }
        }
    }

    // 检查服务组件类型是否重复
    isServicePlanItemTypeUsed(currentIndex: number, type: string): boolean {
        const items = this.servicePlanItems.value;
        return items.some((item, index) => 
            index !== currentIndex && item.servicePlanItemType === type
        );
    }

    // 加载Region列表
    loadRegionList(): Promise<any> {
        // 如果已经在加载中或已加载，直接返回
        if (this.regionList.length > 0) {
            return Promise.resolve({ success: true });
        }

        const params = {};
        return this.req.post(URL.REGION_URL + '/query', params)
            .then(rs => {
                if (rs.success) {
                    this.regionList = rs.data.dataList || rs.data || [];
                    console.log('Region列表', this.regionList);
                }
                return rs;
            })
            .catch(err => {
                console.error('加载Region列表失败', err);
                return { success: false };
            });
    }

    // 加载镜像列表
    loadImageList(): Promise<any> {
        // 如果已经在加载中或已加载，直接返回
        if (this.imageList.length > 0) {
            return Promise.resolve({ success: true });
        }

        const params = {
            bean: {
                description: 'vdi'
            }
        };
        return this.cloudServerService.getImage(params)
            .then(rs => {
                if (rs.success) {
                    this.imageList = rs.data || [];
                }
                return rs;
            })
            .catch(err => {
                console.error('加载镜像列表失败', err);
                return { success: false };
            });
    }

    // 加载表单数据（编辑模式）
    loadFormData() {
        if (this.editData) {
            // 处理服务组件数据
            const servicePlanItems = this.editData.servicePlanItems || [];
            this.servicePlanItems.clear();

            if (servicePlanItems.length === 0) {
                this.servicePlanItems.push(this.createServicePlanItem());
            } else {
                servicePlanItems.forEach((item, index) => {
                    const formItem = this.createServicePlanItem();

                    // 先设置字段的启用/禁用状态
                    this.setServicePlanItemFieldStates(formItem, item.servicePlanItemType);

                    // 设置所有字段的值
                    const formData: any = {
                        servicePlanItemType: item.servicePlanItemType,
                        servicePlanItemSubType: item.servicePlanItemSubType || '',
                        amount: item.amount || (item.servicePlanItemType === 'IMAGE' ? '' : 1)
                    };

                    // 如果是IMAGE类型且有imageId，直接设置
                    if (item.servicePlanItemType === 'IMAGE' && item.imageId) {
                        formData.imageId = item.imageId;
                        console.log('设置imageId:', item.imageId, '镜像列表长度:', this.imageList.length);
                        console.log('镜像选项:', this.imageList.map(img => ({id: img.id, name: img.name, type: typeof img.id})));
                        console.log('要设置的imageId类型:', typeof item.imageId);
                    } else {
                        formData.imageId = '';
                    }

                    formItem.patchValue(formData);

                    this.servicePlanItems.push(formItem);

                    // 验证并重新设置imageId
                    if (item.servicePlanItemType === 'IMAGE' && item.imageId) {
                        setTimeout(() => {
                            const imageControl = formItem.get('imageId');
                            const currentValue = imageControl.value;
                            console.log(`组件${index} imageId验证:`, {
                                期望值: item.imageId,
                                实际值: currentValue,
                                是否匹配: currentValue === item.imageId,
                                字段状态: imageControl.enabled ? '启用' : '禁用'
                            });

                            // 如果值不匹配，强制重新设置
                            if (currentValue !== item.imageId && imageControl.enabled) {
                                console.log('强制重新设置imageId:', item.imageId);
                                imageControl.setValue(item.imageId);
                                imageControl.markAsDirty();
                                imageControl.updateValueAndValidity();
                            }
                        }, 500);
                    }
                });
            }

            // 处理Region关系数据
            const regionIds = (this.editData.servicePlanRegionRelations || [])
                .map(relation => relation.regionId);

            // console.log('编辑数据中的Region关系:', this.editData.servicePlanRegionRelations);
            // console.log('提取的regionIds:', regionIds);
            // console.log('当前Region列表:', this.regionList);

            this.servicePlanForm.patchValue({
                ...this.editData,
                servicePlanRegionRelations: regionIds,
                autoEffectiveDate: this.editData.autoEffectiveDate ? new Date(this.editData.autoEffectiveDate) : null,
                autoExpiryDate: this.editData.autoExpiryDate ? new Date(this.editData.autoExpiryDate) : null
            });

            // 验证Region字段是否正确设置（可选的调试代码）
            // setTimeout(() => {
            //     const currentRegionValue = this.servicePlanForm.get('servicePlanRegionRelations').value;
            //     console.log('Region字段当前值:', currentRegionValue);
            // }, 100);
        }
    }

    // 重置表单
    resetForm() {
        this.servicePlanForm.reset();
        this.servicePlanItems.clear();
        this.servicePlanItems.push(this.createServicePlanItem());
    }

    // 检查表单字段是否被修改过
    isDirty(fc: AbstractControl) {
        return fc.dirty && fc.invalid;
    }

    // 提交时手动触发一次表单校验
    validateAllFormFields(formGroup: FormGroup | FormArray) {
        if (formGroup instanceof FormGroup) {
            Object.keys(formGroup.controls).forEach(field => {
                const control = formGroup.get(field);
                if (control instanceof FormControl) {
                    control.markAsDirty({ onlySelf: true });
                } else if (control instanceof FormGroup || control instanceof FormArray) {
                    this.validateAllFormFields(control);
                }
            });
        } else if (formGroup instanceof FormArray) {
            formGroup.controls.forEach(control => {
                if (control instanceof FormGroup || control instanceof FormArray) {
                    this.validateAllFormFields(control);
                } else {
                    control.markAsDirty({ onlySelf: true });
                }
            });
        }
    }

    // 提交表单
    submit() {
        if (this.servicePlanForm.invalid) {
            this.validateAllFormFields(this.servicePlanForm);
            return;
        }

        // 检查服务组件类型是否重复
        const itemTypes = this.servicePlanItems.value.map(item => item.servicePlanItemType);
        const uniqueTypes = [...new Set(itemTypes)];
        if (itemTypes.length !== uniqueTypes.length) {
            this.msg.error('服务组件类型不能重复');
            return;
        }

        this.isLoading = true;
        const formValue = this.servicePlanForm.value;
        
        // 构造提交数据
        const submitData = {
            ...formValue,
            servicePlanRegionRelations: (formValue.servicePlanRegionRelations || [])
                .map(regionId => ({ regionId })),
            servicePlanItems: this.servicePlanItems.controls.map((control, index) => {
                const item = control.value;
                const formGroup = control as FormGroup;

                const servicePlanItem: any = {
                    servicePlanItemType: item.servicePlanItemType,
                    servicePlanItemSubType: formGroup.get('servicePlanItemSubType').enabled ?
                        (item.servicePlanItemSubType || null) : null,
                    imageId: formGroup.get('imageId').enabled ?
                        (item.imageId || null) : null
                };

                // 只有当amount字段启用时才包含amount值
                if (formGroup.get('amount').enabled) {
                    servicePlanItem.amount = item.amount;
                }

                return servicePlanItem;
            })
        };

        const apiCall = this.editData ? 
            this.servicePlanService.update(submitData) : 
            this.servicePlanService.add(submitData);

        apiCall.then(rs => {
            if (rs.success) {
                this.msg.success(this.editData ? '修改成功' : '新增成功');
                this.handleCancel();
                this.onSave.emit();
            } else {
                this.msg.error(`${this.editData ? '修改' : '新增'}失败${rs.message ? ': ' + rs.message : ''}`);
            }
            this.isLoading = false;
        })
        .catch(err => {
            this.msg.error(`${this.editData ? '修改' : '新增'}失败`);
            this.isLoading = false;
        });
    }

    // 取消操作
    handleCancel() {
        this.visible = false;
        this.visibleChange.emit(false);
        this.resetForm();
    }

    // 获取弹窗标题
    get modalTitle(): string {
        return this.editData ? '编辑服务计划' : '新增服务计划';
    }
}
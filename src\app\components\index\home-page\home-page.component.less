// .main-margin {
//     .row {
//         margin: 0;
//     }
// }
// .icon {
//     display: inline-block;
//     width: 50px;
//     height: 60px;
//     background-repeat: no-repeat;
//     background-size: 50px 50px;
// }
// .yfwq-bg {
//     background-image:url(src/assets/images/home-yfwq.png);
// }
// .yfwq-box:hover .yfwq-bg {
//     background-image:url(src/assets/images/home-yfwq-hover.png);
// }
// .yp-bg {
//     background-image:url(src/assets/images/home-yp.png);
// }
// .yp-box:hover .yp-bg {
//     background-image:url(src/assets/images/home-yp-hover.png);
// }
// .txgw-bg {
//     background-image:url(src/assets/images/home-txgw.png);
// }
// .txgw-box:hover .txgw-bg {
//     background-image:url(src/assets/images/home-txgw-hover.png);
// }
// .zywl-bg {
//     background-image:url(src/assets/images/home-zywl.png);
// }
// .zywl-box:hover .zywl-bg {
//     background-image:url(src/assets/images/home-zywl-hover.png);
// }
// .fzjh-bg {
//     background-image:url(src/assets/images/home-fzjh.png);
// }
// .fzjh-box:hover .fzjh-bg {
//     background-image:url(src/assets/images/home-fzjh-hover.png);
// }
// .dxcc-bg {
//     background-image:url(src/assets/images/home-dxcc.png);
// }
// .dxcc-box:hover .dxcc-bg {
//     background-image:url(src/assets/images/home-dxcc-hover.png);
// }
// .rds-bg {
//     background-image:url(src/assets/images/home-rds.png);
// }
// .rds-box:hover .rds-bg {
//     background-image:url(src/assets/images/home-rds-hover.png);
// }
// .txss-bg {
//     background-image:url(src/assets/images/home-txss.png);
// }
// .txss-box:hover .txss-bg {
//     background-image:url(src/assets/images/home-txss-hover.png);
// }
// .wjcc-bg {
//     background-image:url(src/assets/images/home-wjcc.png);
// }
// .wjcc-box:hover .wjcc-bg {
//     background-image:url(src/assets/images/home-wjcc-hover.png);
// }
// .xxdl-bg {
//     background-image:url(src/assets/images/home-xxdl.png);
// }
// .xxdl-box:hover .xxdl-bg {
//     background-image:url(src/assets/images/home-xxdl-hover.png);
// }
// .rqfw-bg {
//     background-image:url(src/assets/images/home-rqfw.png);
// }
// .rqfw-box:hover .rqfw-bg {
//     background-image:url(src/assets/images/home-rqfw-hover.png);
// }
// .xnpt-bg {
//     background-image:url(src/assets/images/home-xnpt.png);
// }
// .xnpt-box:hover .xnpt-bg {
//     background-image:url(src/assets/images/home-xnpt-hover.png);
// }
// .main-f2 .main-f2-box .main-f2-col:hover .icon_index {
//     color: #0083FF;
// }

@import "../../../style/common/_variable.less";

@bannerHeight: 100vw * (500 / 1903);

.banner {
    .banner-item {
        height: 500px;
        height: @bannerHeight;
        color: #fff;
        // text-align: center;

        .on-container {
            height: 100%;
            position: relative;
            padding-top: 1px;
        }

        .banner-text {
            position: absolute;
            left: 0;
            top: 50%;
            height: 120px;
            margin-top: -120px / 2;
        }

        .banner-title {
            font-size: 40px;
            letter-spacing: 3px;
            color: #fff;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
            user-select: none;
        }

        .banner-desc {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.9);
            letter-spacing: 1px;
            user-select: none;
        }

    }
}

.feature {
    background-color: #F6F6F6;

    .on-container {
        border-left: 1px solid darken(@light-gray, 2%);
        border-right: 1px solid darken(@light-gray, 2%);
    }

    .feature-item {
        padding: 20px 30px;
        margin: 0;
        position: relative;
        background-color: #f6f6f6;
        transition: all .3s ease 0s;

        & + .feature-item {
            border-left: 1px solid darken(@light-gray, 2%);
        }

        &:hover {
            z-index: 2;
            background-color: #fff;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
            
            .feature-icon {
                background-image: url(src/assets/images/index-icon1_hover.png);
            }
        }

        &.low-latency {
            .feature-icon {
                background-image: url(src/assets/images/index-icon2.png);
            }
            &:hover {
                .feature-icon {
                    background-image: url(src/assets/images/index-icon2_hover.png);
                }
            }
        }

        &.low-threshold {
            .feature-icon {
                background-image: url(src/assets/images/index-icon3.png);
            }
            &:hover {
                .feature-icon {
                    background-image: url(src/assets/images/index-icon3_hover.png);
                }
            }
        }

        .feature-icon {
            position: absolute;
            width: 50px;
            height: 50px;
            top: 50%;
            left: 30px;
            margin-top: -50px / 2;
            background: url(src/assets/images/index-icon1.png) center center no-repeat;   
        }

        .feature-content {
            margin-left: 65px;

            .feature-name {
                font-size: 20px;
                position: relative;
                margin-bottom: 10px;
                padding-bottom: 10px;
                color: #333;

                &:after {
                    content: '';
                    position: absolute;
                    top: 100%;
                    left: 0;
                    width: 30px;
                    border-top: 2px solid @primary;
                }
            }
        }
    }
}

.products {
    text-align: center;
    background-color: #fff;
    padding: 90px 0 40px;
    position: relative;
    z-index: 3;

    h3 {
        font-size: 32px;
    }

    .sub-title {
        font-size: 16px;
        color: #999;
    }

    .product-list-container {
        margin: 50px 0;
        border: 1px solid @light-gray;
        border-left: none;
    }

    .product-list {
        font-size: 0;
        text-align: left;

        .product-item {
            position: relative;
            text-align: center;
            font-size: 14px;
            display: inline-block;
            width: 100% / 5;
            height: 380px;
            vertical-align: top;
            border-left: 1px solid @light-gray;
            border-bottom: 1px solid @light-gray;
            transition: box-shadow .3s ease 0s;

            &.last-row {
                border-bottom: none;
            }

            &.last-one {
                &:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    right: -1px;
                    border-right: 1px solid @light-gray;
                }
            }

            &:hover {
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

                .check-detail {
                    background-color: @primary;
                    color: #fff;
                }
            }

            a {
                display: block;
                height: 100%;
                padding: 30px;
                position: relative;
            }

            .product-name {
                font-size: 18px;
                margin-bottom: 20px;
            }

            .product-desc {
                font-size: 14px;
                color: #666;
                min-height: 144px;
            }

            .check-detail {
                display: inline-block;
                width: 100%;
                border: 1px solid @primary;
                padding: 6px 12px;
                color: @primary;
                font-size: 12px;
                transition: all .3s ease 0s;
            }
        }
    }
}

.icon {
    display: inline-block;
    width: 50px;
    height: 60px;
    background-repeat: no-repeat;
    background-size: 50px 50px;
}
.yfwq-bg {
	background-image:url(src/assets/images/home-yfwq.png);
}
.yp-bg {
	background-image:url(src/assets/images/home-yp.png);
}
.txgw-bg {
	background-image:url(src/assets/images/home-txgw.png);
}
.zywl-bg {
	background-image:url(src/assets/images/home-zywl.png);
}
.fzjh-bg {
	background-image:url(src/assets/images/home-fzjh.png);
}
.dxcc-bg {
	background-image:url(src/assets/images/home-dxcc.png);
}
.rds-bg {
	background-image:url(src/assets/images/home-rds.png);
}
.txss-bg {
	background-image:url(src/assets/images/home-txss.png);
}
.wjcc-bg {
	background-image:url(src/assets/images/home-wjcc.png);
}
.xxdl-bg {
	background-image:url(src/assets/images/home-xxdl.png);
}
.rqfw-bg {
	background-image:url(src/assets/images/home-rqfw.png);
}
.xnpt-bg {
	background-image:url(src/assets/images/home-xnpt.png);
}
.yjk-bg {
	background-image:url(src/assets/images/home-yjk.png);
}
.product-item :hover {
    .yfwq-bg {
        background-image:url(src/assets/images/home-yfwq-hover.png);
    }
    
    .yp-bg {
        background-image:url(src/assets/images/home-yp-hover.png);
    }

    .txgw-bg {
        background-image:url(src/assets/images/home-txgw-hover.png);
    }

    .zywl-bg {
        background-image:url(src/assets/images/home-zywl-hover.png);
    }

    .fzjh-bg {
        background-image:url(src/assets/images/home-fzjh-hover.png);
    }

    .dxcc-bg {
        background-image:url(src/assets/images/home-dxcc-hover.png);
    }

    .rds-bg {
        background-image:url(src/assets/images/home-rds-hover.png);
    }

    .txss-bg {
        background-image:url(src/assets/images/home-txss-hover.png);
    }
    
    .wjcc-bg {
        background-image:url(src/assets/images/home-wjcc-hover.png);
    }

    .xxdl-bg {
        background-image:url(src/assets/images/home-xxdl-hover.png);
    }

    .rqfw-bg {
        background-image:url(src/assets/images/home-rqfw-hover.png);
    }

    .xnpt-bg {
        background-image:url(src/assets/images/home-xnpt-hover.png);
    }

    .yjk-bg {
        background-image:url(src/assets/images/home-yjk-hover.png);
    }
}

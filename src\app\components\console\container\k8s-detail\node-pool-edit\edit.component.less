// .network-form {
//     input[type=text],
//     input[type=password],
//     input[type=number] {
//         width: 276px!important;
//     }
// }

.ip-item {
    width: 47px!important;
}

.divider {
    display: inline-block;
    text-align: center;
    width: 8px;
}
.ant-input{
    margin-bottom: 0 !important;
}
.ng-star-inserted{
    margin-bottom: 15px;
}
.element::-webkit-scrollbar {
    display: none;
}
对于Firefox，可以使用scrollbar-width属性：

.element {
    scrollbar-width: none; /* Firefox */
}
对于IE和Edge，可以使用ms-overflow-style属性：

.element {
    -ms-overflow-style: none;  /* IE 和 Edge */
}
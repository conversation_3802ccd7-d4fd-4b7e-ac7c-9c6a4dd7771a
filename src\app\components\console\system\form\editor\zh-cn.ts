// Form-js 中文翻译语言包
// 由于form-js官方暂不支持i18n，我们使用DOM操作的方式实现汉化
export const translations: Record<string, string> = {
  // ===== 左侧工具面板 (Palette) =====
  'Components': '组件',
  'Search components': '搜索组件',
  'Input': '输入',
  'Text field': '文本框',
  'Text area': '多行文本',
  'Number': '数字',
  'Date time': '日期时间',
  'Expression': '表达式',
  'File picker': '文件选择',
  'Selection': '选择',
  'Checkbox': '复选框',
  'Checkbox group': '复选框组',
  'Radio group': '单选框组',
  'Select': '下拉选择',
  'Tag list': '标签列表',
  'Presentation': '展示',
  'Text view': '文本展示',
  'Image view': '图片展示',
  'Table': '表格',
  'HTML view': 'HTML展示',
  'Document preview': '文档预览',
  'Spacer': '间隔符',
  'Separator': '分隔线',

  // ===== 容器组件 =====
  'Containers': '容器',
  'Group': '分组',
  'Dynamic list': '动态列表',
  'iFrame': '内嵌框架',

  // ===== 操作组件 =====
  'Action': '操作',
  'Button': '按钮',

  // ===== 右侧属性面板 (Properties Panel) =====
  'FORM': '表单',
  'NewForm': '新建表单',
  'General': '常规',
  'ID': 'ID',
  'Label': '标签',
  'Key': '键名',
  'Description': '描述',
  'Validation': '校验',
  'Appearance': '外观',
  'Conditional': '条件',
  'Layout': '布局',
  'Version tag': '版本标签',

  // 常规属性
  'Required': '必填',
  'Readonly': '只读',
  'Disabled': '禁用',
  'Default value': '默认值',
  'Placeholder': '占位符',
  'Values': '选项值',
  'Value': '值',
  'Text': '文本',

  // 校验相关
  'Minimum length': '最小长度',
  'Maximum length': '最大长度',
  'Minimum value': '最小值',
  'Maximum value': '最大值',
  'Pattern': '正则模式',
  'Custom validation': '自定义校验',

  // 外观相关
  'Columns': '列数',
  'Height': '高度',
  'Width': '宽度',
  'Prefix adorner': '前缀装饰',
  'Suffix adorner': '后缀装饰',

  // 条件相关
  'Hide if': '隐藏条件',
  'Disable if': '禁用条件',

  // 布局相关
  'Columns (layout)': '列数 (布局)',
  'Row': '行',

  // ===== 属性面板详细选项 =====
  'Binds to a form variable': '绑定到表单变量',
  'Read only': '只读',
  'Condition': '条件',
  'Condition under which the field is hidden': '字段隐藏的条件',
  'Custom properties': '自定义属性',
  'Auto': '自动',

  // 表单字段属性
  'Path': '路径',
  'Type': '类型',
  'Source': '源',
  'Alt': '替代文本',

  // ===== 通用操作文本 =====
  'Create': '创建',
  'Remove': '移除',
  'Delete': '删除',
  'Append': '追加',
  'Duplicate': '复制',
  'Move up': '上移',
  'Move down': '下移',
  'Edit': '编辑',
  'Save': '保存',
  'Cancel': '取消',
  'Add': '添加',
  'Clear': '清空',
  'Reset': '重置',
  'Search': '搜索',
  'Filter': '筛选',

  // ===== 画布提示文本 =====
  'Build your form': '构建您的表单',
  'Drag and drop components here to start designing.': '拖拽组件到这里开始设计。',
  'Use the preview window to test your form.': '使用预览窗口测试您的表单。',

  // ===== 属性面板状态 =====
  'No applicable properties': '无可用属性',
  'Select a component to edit its properties': '选择一个组件来编辑其属性',
  'No component selected': '未选择组件',

  // ===== 错误和警告信息 =====
  'Invalid value': '无效值',
  'Required field': '必填字段',
  'Field is required': '此字段为必填项',
  'Invalid format': '格式无效',
  'Value too short': '值太短',
  'Value too long': '值太长',
  'Value too small': '值太小',
  'Value too large': '值太大',

  // ===== 表单组件特定文本 =====
  'Options': '选项',
  'Option': '选项',
  'Add option': '添加选项',
  'Remove option': '移除选项',
  'Multiple selection': '多选',
  'Allow multiple': '允许多选',
  'Increment': '步长',
  'Decimal places': '小数位数',
  'Date format': '日期格式',
  'Time format': '时间格式',
  'Button text': '按钮文本',
  'Button type': '按钮类型',
  'Submit': '提交',
  'Custom': '自定义',
  'HTML content': 'HTML内容',
  'Image source': '图片源',
  'Image URL': '图片URL',
  'Alt text': '替代文本',

  // ===== 序列化和输出相关 =====
  'Serialization': '序列化',
  'Output as string': '输出为字符串',
  'Allows arbitrary precision values': '允许任意精度值',

  // ===== 验证相关 =====
  'Validation pattern': '验证模式',
  'Custom regular expression': '自定义正则表达式',

  // ===== 文件相关 =====
  'Field label': '字段标签',
  'Field description': '字段描述',
  'Supported file formats': '支持的文件格式',
  'A comma separated list of file type specifiers': '逗号分隔的文件类型说明符列表',
  'file type specifiers': '文件类型说明符',
  'Upload multiple files': '上传多个文件',
  'Must not be empty': '不能为空',

  // ===== 选项数据源相关 =====
  'Options source': '选项数据源',
  'Static': '静态',
  'Static options': '静态选项',
  'Input data': '输入数据',
  'Options expression': '选项表达式',
  'Define an expression to populate the options from.': '定义一个表达式来填充选项。',
  'Dynamic options': '动态选项',
  'Input values key': '输入值键',
  'Must not be empty.': '不能为空。',
  'Define which input property to populate the values from': '定义从哪个输入属性填充值',

  // ===== 日期时间相关 =====
  'Date': '日期',
  'Time': '时间',
  'Date & Time': '日期时间',
  'Date label': '日期标签',
  'Subtype': '子类型',
  'Constraints': '约束',
  'Disallow past dates': '禁止过去日期',

  // ===== 表达式和计算相关 =====
  'Expression is empty': '表达式为空',
  'Target value': '目标值',
  'Define an expression to calculate the value of this field': '定义一个表达式来计算此字段的值',
  'Compute on': '计算时机',
  'Value changes': '值变化时',
  'Form submission': '表单提交时',
  'Deactivate if': '停用条件',
  'Condition under which the field is deactivated': '字段被停用的条件',

  // ===== 数字相关 =====
  'Decimal digits': '小数位数',
  'Prefix': '前缀',
  'Suffix': '后缀',

  // ===== 下拉选择相关 =====
  'Searchable': '可搜索',
  'select.dhgqd': '选择.dhgqd',
  'Alternative text': '替代文本',

  // ===== 表单验证和限制 =====
  'Maximum 4 fields per row are allowed': '每行最多允许4个字段',

  // ===== 搜索和查找 =====
  'No components found.': '未找到组件。',

  // ===== 选择框和下拉选项 =====
  '<none>': '<无>',
  'Not checked': '未选中',
  'Checked': '已选中',
  'Expression or static value (link,data URI)': '表达式或静态值（链接，数据URI）',

  // ===== 文本相关 =====
  'Supports markdown and templating.': '支持Markdown和模板语法。',
  'Learn more': '了解更多',

  // ===== 容器和布局相关 =====
  'Title': '标题',
  'URL': '网址',
  'Height of the container in pixels.': '容器高度（像素）。',
  'A number is required.': '需要输入数字。',
  'Security attributes': '安全属性',
  'These options can incur security risks, especially if used in combination with dynamic links. Ensure that you are only enabling what your use case requires.': '这些选项可能带来安全风险，特别是与动态链接结合使用时。请确保只启用您的用例所需的功能。',
  'Script execution': '脚本执行',
  'Allow same origin': '允许同源',
  'Opens in fullscreen': '全屏打开',
  'Geolocation': '地理位置',
  'Camera access': '摄像头访问',
  'Microphone access': '麦克风访问',
  'Forms submission': '表单提交',
  'Open modal windows': '打开模态窗口',
  'Open pop-ups': '打开弹出窗口',
  'Top level navigation': '顶级导航',
  'Storage access by user': '用户存储访问',

  // ===== 分组和重复组件相关 =====
  'Group label': '分组标签',
  'Where the child variables of this component are pathed to.': '此组件的子变量路径位置。',
  'Default number of items': '默认项目数量',
  'Allow add/delete items': '允许添加/删除项目',
  'Disable collapse': '禁用折叠',
  'Number of non-collapsing items': '不折叠项目数量',
  'Repeatable': '可重复',
  'Drag and drop components here.': '将组件拖放到此处。',

  // ===== 文档和文件相关 =====
  'Document reference': '文档引用',
  'The document data source is required.': '文档数据源是必需的。',

  // ===== HTML组件相关 =====
  'Content': '内容',
  'Supports HTML, styling, and templating. Styles are automatically scoped to the HTML component.': '支持HTML、样式和模板语法。样式会自动限定在HTML组件范围内。',

  // ===== 表格相关 =====
  'Table label': '表格标签',
  'Data source': '数据源',
  'Specify the source from which to populate the table': '指定填充表格的数据源',
  'Pagination': '分页',
  'Number of rows per page': '每页行数',
  'Headers source': '表头来源',
  'List of items': '项目列表',
  'Header Items': '表头项目',

  // ===== 文件上传相关 =====
  'Browse': '浏览',
  'No files selected': '未选择文件',
  'A comma-separated list of': '逗号分隔的列表：',
  'supported file types': '支持的文件类型',

  // ===== 图片和媒体相关 =====

  // ===== 文档预览相关 =====
  'Max height of preview container': '预览容器最大高度',
  'Documents with height that exceeds the defined value will be vertically scrollable': '超出定义高度的文档将可垂直滚动',

  // ===== 布局和对齐相关 =====
  'Show outline': '显示轮廓',
  'Vertical alignment': '垂直对齐',
  'Top': '顶部',
  'Center': '居中',
  'Bottom': '底部',

  // ===== 表格表头相关 =====

  // ===== 数学和数字相关 =====
  'Minimum': '最小值',
  'Maximum': '最大值',
  'fx': 'fx',

  // ===== 图片和文件相关 =====

  // ===== 数字和尺寸相关 =====
  'Min': '最小值',
  'Max': '最大值',
  'Step': '步长',
  'Precision': '精度',
  'Unit': '单位',
  'Size': '大小',
  'Small': '小',
  'Medium': '中',
  'Large': '大',

  // ===== 状态和行为 =====
  'Enabled': '启用',
  'Visible': '可见',
  'Active': '激活',
  'Selected': '已选择',
  'Unchecked': '未选中',
  'Expanded': '展开',
  'Collapsed': '折叠',

  // ===== 表单验证消息 =====
  'This field is required': '此字段为必填项',
  'Please enter a valid value': '请输入有效值',
  'Please select an option': '请选择一个选项',

  // ===== 工具提示和说明 =====
  'Drag to reorder': '拖拽重新排序',
  'Click to select': '点击选择',
  'Double click to edit': '双击编辑',

  // ===== 其他常用文本 =====
  'Form': '表单',
  'Component': '组件',
  'Element': '元素',
  'Properties': '属性',
  'Settings': '设置',
  'Configuration': '配置',

  // ===== 属性面板中的英文标签 =====
  'Fx': 'Fx',
  'Variable': '变量',
  'Condition expression': '条件表达式',
  'Hide': '隐藏',
  'Show': '显示',
  'Enable': '启用',
  'Disable': '禁用',
  'Optional': '可选',
  'Validation rule': '验证规则',
  'Error message': '错误消息',
  'Help text': '帮助文本',
  'Tooltip': '工具提示',
  'Placeholder text': '占位符文本',
  'Default': '默认',
  'None': '无',
  'True': '真',
  'False': '假',
  'Yes': '是',
  'No': '否',
  'On': '开',
  'Off': '关',
  'Open': '打开',
  'Close': '关闭',
  'Expand': '展开',
  'Collapse': '折叠',
  'Next': '下一个',
  'Previous': '上一个',
  'First': '第一个',
  'Last': '最后一个',
  'Apply': '应用',
  'OK': '确定',
  'Confirm': '确认',
  'Submit form': '提交表单',
  'Reset form': '重置表单',
  'Clear form': '清空表单'
};

// DOM汉化函数 - 通过查找和替换DOM中的文本来实现汉化
export function translateFormEditor(container: HTMLElement): void {
  if (!container) return;

  // 使用MutationObserver监听DOM变化，实时汉化新添加的元素
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            setTimeout(() => {
              translateElement(node as HTMLElement);
            }, 50); // 延迟执行确保元素完全渲染
          }
        });
      }
    });
  });

  // 开始观察
  observer.observe(container, {
    childList: true,
    subtree: true,
    attributes: false,
    characterData: false
  });

  // 定时翻译 - 确保form-js加载完成后能够翻译
  const translateInterval = setInterval(() => {
    translateElement(container);
  }, 500); // 更频繁的翻译检查

  // 10秒后停止定时翻译
  setTimeout(() => {
    clearInterval(translateInterval);
  }, 10000);

  // 初始翻译
  setTimeout(() => {
    translateElement(container);
  }, 100);

  // 返回observer以便后续清理
  (container as any)._translationObserver = observer;
  (container as any)._translateInterval = translateInterval;
}

// 翻译单个元素
function translateElement(element: HTMLElement): void {
  // 特定的form-js元素翻译
  translateFormJSSpecificElements(element);

  // 翻译文本节点
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_TEXT,
    (node) => {
      // 跳过script和style标签
      const parent = node.parentElement;
      if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
        return NodeFilter.FILTER_REJECT;
      }
      return NodeFilter.FILTER_ACCEPT;
    }
  );

  const textNodes: Text[] = [];
  let node;
  while (node = walker.nextNode()) {
    textNodes.push(node as Text);
  }

  textNodes.forEach(textNode => {
    const text = textNode.textContent?.trim();
    if (text && translations[text]) {
      textNode.textContent = translations[text];
    }
  });

  // 翻译属性
  translateAttributes(element);

  // 翻译子元素
  element.querySelectorAll('*').forEach(child => {
    translateAttributes(child as HTMLElement);
  });
}

// 翻译form-js特定元素
function translateFormJSSpecificElements(element: HTMLElement): void {
  // 翻译所有包含特定类名的元素
  const selectors = [
    '.fjs-palette-group-header',
    '.fjs-palette-entry',
    '.fjs-properties-panel-header',
    '.fjs-form-field-label',
    '.fjs-palette-group-title',
    '.fjs-palette-entry-label',
    '.fjs-properties-panel-group-header',
    '.fjs-properties-panel-entry',
    '.fjs-input-label',
    '.fjs-checkbox-label',
    '.fjs-radio-label',
    '.fjs-select-label',
    'label',
    '.bio-properties-panel-group-header',
    '.bio-properties-panel-entry'
  ];

  selectors.forEach(selector => {
    const elements = element.querySelectorAll(selector);
    // if (elements.length > 0) {
    //   console.log(`找到 ${elements.length} 个 ${selector} 元素`);
    // }

    elements.forEach((el, index) => {
      const text = el.textContent?.trim();

      if (text && translations[text]) {
        el.textContent = translations[text];
      }
    });
  });

  // 翻译所有label元素
  element.querySelectorAll('label').forEach(label => {
    const text = label.textContent?.trim();
    if (text && translations[text]) {
      label.textContent = translations[text];
    }
  });

  // 翻译所有span元素（很多属性面板文本在span中）
  element.querySelectorAll('span').forEach(span => {
    // 只翻译没有子元素的span
    if (span.children.length === 0) {
      const text = span.textContent?.trim();
      if (text && translations[text] && text.length > 1) {
        span.textContent = translations[text];
      }
    }
  });

  // 翻译所有div元素中的纯文本
  element.querySelectorAll('div').forEach(div => {
    // 只翻译没有子元素的div
    if (div.children.length === 0) {
      const text = div.textContent?.trim();
      if (text && translations[text] && text.length > 1) {
        div.textContent = translations[text];
      }
    }
  });

  // 通用翻译：查找所有叶子节点
  const allElements = element.querySelectorAll('*');

  allElements.forEach((el) => {
    if (el.children.length === 0 && el.textContent) { // 叶子节点
      const text = el.textContent.trim();
      if (text && translations[text] && text.length > 1) {
        el.textContent = translations[text];
      }
    }
  });
}

// 翻译元素属性
function translateAttributes(element: HTMLElement): void {
  // 翻译placeholder属性
  const placeholder = element.getAttribute('placeholder');
  if (placeholder && translations[placeholder]) {
    element.setAttribute('placeholder', translations[placeholder]);
  }
  // 翻译title属性
  const title = element.getAttribute('title');
  if (title && translations[title]) {
    element.setAttribute('title', translations[title]);
  }
  // 翻译aria-label属性
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel && translations[ariaLabel]) {
    element.setAttribute('aria-label', translations[ariaLabel]);
  }
}

// 清理翻译观察器
export function cleanupTranslation(container: HTMLElement): void {
  const observer = (container as any)._translationObserver;
  if (observer) {
    observer.disconnect();
    delete (container as any)._translationObserver;
  }
  const interval = (container as any)._translateInterval;
  if (interval) {
    clearInterval(interval);
    delete (container as any)._translateInterval;
  }
}

// 手动触发翻译（用于动态内容）
export function retranslate(container: HTMLElement): void {
  // 强制翻译所有可能的文本内容
  forceTranslateAllText(container);
  translateElement(container);
}

// 强制翻译所有文本内容
function forceTranslateAllText(container: HTMLElement): void {
  // 获取所有文本节点
  const walker = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    null
  );
  const textNodes: Text[] = [];
  let node;
  while (node = walker.nextNode()) {
    textNodes.push(node as Text);
  }
  textNodes.forEach((textNode, index) => {
    const text = textNode.textContent?.trim();
    if (text && text.length > 0) {
      if (translations[text]) {
        textNode.textContent = translations[text];
      }
    }
  });
}

// 调试函数：打印所有可翻译的文本
export function debugTranslatableTexts(container: HTMLElement): void {
  const walker = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    (node) => {
      const parent = node.parentElement;
      if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
        return NodeFilter.FILTER_REJECT;
      }
      return NodeFilter.FILTER_ACCEPT;
    }
  );

  // let node;
  // while (node = walker.nextNode()) {
  //   const text = node.textContent?.trim();
  //   if (text && text.length > 0) {
  //     console.log(`文本: "${text}" | 父元素: ${node.parentElement?.tagName} | 类名: ${node.parentElement?.className}`);
  //   }
  // }
}

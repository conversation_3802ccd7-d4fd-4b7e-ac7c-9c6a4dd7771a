@import '../../../../style/common/_variable.less';

.folder-tips {
    margin-left: 110px;
    li {
      list-style: decimal;
    }
  }
.upload-tips {
    font-size: 12px;
    margin: 10px 0;
    color: @placeholder-color;
}
.folder-tips-error {
    li {
        color: @red;
    }
}
// 增加内容样式
.choosable-add-text {
    cursor: pointer;
    width: 120px;
    line-height: 30px;
    margin-left: 10px;
    i {
        display: inline-block;
        margin-right: 8px
    }
}
.choosable-delete-icon {
    cursor: pointer;
    margin-left: 10px;
}
.select-container {
    margin: 15px 0;
}
.select-tips {
    display: inline-block;
    margin-bottom: 20px;
    width: 100px;
    color: #666;
}
textarea.ant-input {
    vertical-align: top;
}
.ip-input {
    width: 300px; margin: 5px 0
}
.error-input {
    border-color: @red;
    &:focus {
        background-color: #fff;
    }
}
.select-tips-info {
    display: inline-block;
    word-break: break-word;
    width: 60%;
}
.modal-title {
    display: inline-block;
    width: 95%;
}
.dilatation {
    padding-right: 10px;
    float: right;
    color: white;
}

.pathSpan1 {
    word-break: break-all;
    float: left;
    text-align: left;
    align-items: center;
    width: 85%;
}

.pathSpan2 {
    float: right;
    width: 12%;
    line-height: 1.5715;
    text-align: center;
    vertical-align: middle;
}

.select-value {
    display: inline-block;
    margin-left: 35px;
}
.sub-title {
    display: inline-block;
    margin-left: 30px;
    font-size: 14px;
    color: #666666;
    margin-bottom: 5px;
}

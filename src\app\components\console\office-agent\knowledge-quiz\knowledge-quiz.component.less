// 使用办公智能体的样式
.office-content {
  //display: flex;
  //flex-direction: column;
  //height: 100vh;
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0 0;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
}

.title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
}

.subtitle {
  text-align: center;
  color: #666;
  margin: 20px 0 30px;
}

.main-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
  min-height: calc(100vh - 400px);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 20px);
  overflow: hidden;

  // 当有对话时，调整容器高度
  &.has-conversation {
    min-height: calc(100vh - 150px);

    .chat-container {
      height: calc(100vh - 250px); // 为有对话时设置更合适的高度
      min-height: 400px;
    }
  }
}
  
.chat-container {
  // 移除白色背景和边框
  flex: 1; // 占据剩余空间
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  height: calc(100vh - 350px); // 设置明确的高度
  min-height: 350px;
  // 移除这里的overflow-y，让.chat-messages处理滚动

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    // 移除白色背景

    // 美化Y向滚动条 - Webkit浏览器 (Chrome, Safari, Edge)
    &::-webkit-scrollbar {
      width: 6px;
      background: transparent;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.03);
      border-radius: 3px;
      margin: 4px 0;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 3px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.25);
        width: 8px;
      }

      &:active {
        background: rgba(0, 0, 0, 0.35);
      }
    }

    // 滚动条角落
    &::-webkit-scrollbar-corner {
      background: transparent;
    }

    // Firefox滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.03);

    // 平滑滚动
    scroll-behavior: smooth;

    // 确保滚动条始终可见但不占用内容空间
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
      }
    }

    // 当内容较少时隐藏滚动条
    &:not(:hover) {
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.08);
      }
    }
      
      .message-wrapper {
        margin-bottom: 16px;
        
        &.user-message {
          display: flex;
          justify-content: flex-end;
        }
        
        &.ai-message {
          display: flex;
          justify-content: flex-start;
        }
        
        .message-item {
          display: flex;
          max-width: 70%;
          gap: 12px;
          
          &.ai-item {
            .message-avatar {
              flex-shrink: 0;
            }
            
            .message-content {
              background: white;
              border-radius: 12px 12px 12px 4px;
              padding: 12px 16px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              
              .message-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                
                .sender-name {
                  font-weight: 600;
                  color: #1890ff;
                  font-size: 12px;
                }
                
                .message-time {
                  font-size: 11px;
                  color: #8c8c8c;
                }
              }
              
              .message-text {
                color: #262626;
                line-height: 1.6;
                word-wrap: break-word;
              }
            }
          }
          
          &.user-item {
            flex-direction: row-reverse;
            
            .message-avatar {
              flex-shrink: 0;
            }
            
            .message-content {
              background: #1890ff;
              color: white;
              border-radius: 12px 12px 4px 12px;
              padding: 12px 16px;
              box-shadow: 0 1px 3px rgba(0,0,0,0.1);
              
              .message-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                
                .sender-name {
                  font-weight: 600;
                  color: rgba(255,255,255,0.9);
                  font-size: 12px;
                }
                
                .message-time {
                  font-size: 11px;
                  color: rgba(255,255,255,0.7);
                }
              }
              
              .message-text {
                line-height: 1.6;
                word-wrap: break-word;
              }
            }
          }
        }
      }
      
      // 正在输入动画
      .typing-indicator {
        display: flex;
        gap: 4px;
        align-items: center;
        
        span {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #bfbfbf;
          animation: typing 1.4s infinite ease-in-out;
          
          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
    }
}

// 输入区域样式 - 完全复制智能办公智能体的样式
.input-container {
  margin: 20px auto 5px;
  padding: 0 20px;
  max-width: 1100px;
  width: 100%;
  flex: 0 0 30%;
}

.input-box {
  background: #fff;
  width: 100%;
  height: 120px;
  border: 1px solid #2c6cf7;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.input-textarea {
  flex-grow: 1;
  border: none;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.5;
  background: transparent;
  color: #333;
  width: 100%;
  // min-height: 60px; // 移除最小高度限制，与office-ai-agent保持一致

  &::placeholder {
    color: #999;
  }
}

.input-footer {
  display: flex;
  margin-top: 10px;
  align-items: flex-end;
}

.input-button {
  background-color: #f5f7fa;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 0 10px;
  margin-right: 10px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 24px;
}

.input-button i {
  margin-right: 5px;
}

.input-button:hover{
  background-color: #e9eefd;
  border-color: #e9eefd;
}

.input-button.active{
  color: #345de3;
  background-color: #e9eefd;
  border-color: #d3ddfe;
}

// 下拉菜单样式
:ng-deep .ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .ant-dropdown-menu-item {
    padding: 8px 16px;

    &:hover {
      background-color: #f5f7fa;
    }

    &.selected {
      background-color: #e9eefd;
      color: #345de3;

      .model-name {
        color: #345de3;
      }
    }
  }

  // 分组标题样式
  .ant-dropdown-menu-item-group-title {
    font-weight: 600;
    color: #1890ff;
    font-size: 12px;
    padding: 8px 12px 4px;
  }
}

// 模型选择器样式
.model-item {
  .model-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }

  .model-description {
    font-size: 12px;
    color: #666;
    line-height: 1.2;
  }
}

.loading-item, .no-data-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.send-button {
  margin-left: auto;
  background-color: #345de3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: not-allowed;
}

.send-button:not(:disabled) {
  cursor: pointer;
}

.send-button:not(:disabled):hover {
  background-color: #2c5dd6;
}

.footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 20px 0;
}

// 正在输入动画
@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 滚动条样式
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// Markdown内容样式
.markdown-content {
  line-height: 1.6;

  // 标题样式
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.25;

    &:first-child {
      margin-top: 0;
    }
  }

  h1 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 8px; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.1em; }
  h4, h5, h6 { font-size: 1em; }

  // 段落样式
  p {
    margin: 8px 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 列表样式
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;

    li {
      margin: 4px 0;
    }

    ul, ol {
      margin: 4px 0;
    }
  }

  // 代码样式
  code {
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    color: #d73a49;
  }

  // 代码块样式
  pre {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;

    code {
      background: none;
      padding: 0;
      color: inherit;
      font-size: 0.85em;
      line-height: 1.45;
    }
  }

  // 引用样式
  blockquote {
    border-left: 4px solid #dfe2e5;
    padding: 0 16px;
    margin: 12px 0;
    color: #6a737d;

    p {
      margin: 8px 0;
    }
  }

  // 表格样式
  table {
    border-collapse: collapse;
    margin: 12px 0;
    width: 100%;

    th, td {
      border: 1px solid #dfe2e5;
      padding: 6px 13px;
      text-align: left;
    }

    th {
      background: #f6f8fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background: #f6f8fa;
    }
  }

  // 链接样式
  a {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // 分割线样式
  hr {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 16px 0;
  }

  // 强调样式
  strong, b {
    font-weight: 600;
  }

  em, i {
    font-style: italic;
  }

  // 删除线
  del, s {
    text-decoration: line-through;
  }
}

import { Component, OnInit } from '@angular/core';

declare let $: any;
@Component({
    selector: 'app-container',
    templateUrl: './container.component.html',
    styleUrls: ['./container.component.less']
})
export class ContainerComponent implements OnInit {
    constructor(
    ) { }

    intro = {
        title: '容器服务(Kubernetes、Harbor)',
        enName: 'AIC Kubernetes、AIC Harbor',
        desc: 'AIC Kubernetes：基于原生Kubernetes的容器管理服务。提供高性能的容器化管理能力，能够实现对用户容器化应用的全生命周期管理。服务部署简单，免去运维成本。可根据业务一键增删节点，满足不同场景下的使用需求。<br><br>AIC Harbor：私有镜像仓库服务。用于存储、分发用户Docker镜像，一键部署，开箱即用，免去用户手动运维及管理的烦恼，后端可采用AWS S3对象存储方案，以多副本形式保存用户镜像数据，保证用户数据安全性。',
        bgColor: '#1a284d',
        orderLink: '/console/container/kubernetes',
        type: 'k8sProduction',
    }

    ngOnInit() { }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

}

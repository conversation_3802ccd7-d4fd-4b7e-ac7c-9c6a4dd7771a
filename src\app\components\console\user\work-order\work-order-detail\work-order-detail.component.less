@import '../../../../../style/common/_variable.less';

// .order-desc {
//     margin: 10px 0 20px;
// }
.work-order-container {
    border: 1px solid @light-gray;
    
    .work-order-info {
        padding: 16px 24px;
        border-bottom: 1px solid @light-gray;
        background-color: #fafafa;
        
        h5 {
            font-size: 16px;
            display: inline-block;
            font-weight: bold;
            margin-right: 20px;
        }
    }

    .work-order-info-list {
        display: inline-block;
        color: #999;
        font-size: 12px;
        li {
            display: inline-block;
            margin-right: 20px;
            position: relative;

            // &:after {
            //     position: absolute;
            //     content: '';
            //     top: 50%;
            //     right: -16px / 2;
            //     height: 20px;
            //     margin-top: -20px / 2;
            //     border-right: 1px solid #ccc;
            // }
        }
    }
}

.com-list {
    padding: 20px 24px;
    max-height: 500px;
    min-height: 300px;
    overflow: auto;

    &.solved {
        max-height: none;
    }

    .com-item {
        position: relative;
        margin: 20px 0 40px;
        .com-avatar {
            position: absolute;
            top: 0;

            .com-name {
                position: absolute;
                text-align: center;
                font-size: 12px;
                color: #aaa;
                margin-top: 3px;
                top: 100%;
                left: -20px;
                right: -20px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }

        .com-body {
            display: inline-block;
            margin-top: -20px;

            .com-time {
                font-size: 12px;
                color: #999;
                margin-bottom: 6px;
            }

            .com-content {
                border: 1px solid transparent;
                padding: 16px 18px;
                max-width: 600px;
                position: relative;
                color: #666;
                border-radius: 2px;
                word-break: break-all;
                word-wrap: break-word;
                white-space: pre-line;
                
                &:after {
                    content: '';
                    position: absolute;
                    width: 12px;
                    height: 12px;
                    border: 1px solid transparent;
                    transform: rotate(45deg);
                    top: 10px;
                }
            }
        }

        &.com-item-a {
            .com-avatar {
                left: 0;
            }

            .com-body {
                margin-left: 40px + 24px;

                .com-content {
                    // color: #1890ff;
                    background: #e6f7ff;
                    border-color: #91d5ff;

                    &:after {
                        left: -12px / 2;
                        border-bottom-color: #91d5ff;
                        border-left-color: #91d5ff;
                        background-color: #e6f7ff;
                    }
                }
            }
        }

        &.com-item-q {
            text-align: right;
            .com-avatar {
                right: 0;
            }

            .com-body {
                margin-right: 40px + 24px;

                .com-content {
                    text-align: left;
                    // color: #52c41a;
                    background: #f6ffed;
                    border-color: #b7eb8f;

                    &:after {
                        right: -12px / 2;
                        border-top-color: #b7eb8f;
                        border-right-color: #b7eb8f;
                        background-color: #f6ffed;
                    }
                }
            }
        }
    }
}

.reply-container {
    border-top: 1px dashed @light-gray;
    position: relative;
    background-color: #fafafa;
    padding: 24px;
    
    .reply-avatar {
        position: absolute;
        top: 20px;
        left: 24px;
    }

    .replay-body {
        margin-left: 40px + 24px;

        textarea {
            width: 100%;
            border-color: @light-gray;
            border-radius: 2px;
            padding: 12px 16px;
            transition: border-color 0.3s ease 0s;
            min-height: 200px;
            resize: vertical;

            &:focus {
                border-color: @primary;
            }
        }

        .reply-bar {
            margin-top: 5px;

            .hint {
                color: #999;
                margin-right: 10px;
            }

            button {
                min-width: 90px;
            }
        }
    }
}

.order-status {
    color: @green;
}
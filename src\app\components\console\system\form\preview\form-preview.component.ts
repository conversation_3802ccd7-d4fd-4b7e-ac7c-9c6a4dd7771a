import { Component, OnInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormService } from 'src/app/service/console/system/form.service';

// 导入form-js查看器
import { Form } from '@bpmn-io/form-js-viewer';

@Component({
    selector: 'app-form-preview',
    templateUrl: './form-preview.component.html',
    styleUrls: ['./form-preview.component.scss']
})
export class FormPreviewComponent implements OnInit, OnDestroy {
    @ViewChild('formContainer', { static: true }) formContainer!: ElementRef;

    private formViewer: Form;
    public formId: string | null = null;
    public formData: any = null;
    public formName: string = '表单预览';
    public loading = false;

    constructor(
        private route: ActivatedRoute,
        private msg: NzMessageService,
        private formService: FormService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.formId = this.route.snapshot.paramMap.get('id');
<<<<<<< HEAD:src/app/components/form-preview/form-preview.component.ts
        // 判断是否有传入id，如果有则调用接口获取数据，否则从sessionStorage获取
        if (this.formId && this.formId !== 'new') {
            this.loadFormDataFromApi();
        } else {
            // 从sessionStorage获取预览数据
            this.loadPreviewData();
        }
=======
        
        // 从sessionStorage获取预览数据
        this.loadPreviewData();
        
        // 初始化表单查看器
        this.initFormViewer();
>>>>>>> parent of 52d7ed2 (bpmn & form):src/app/components/console/system/form/preview/form-preview.component.ts
    }

    ngOnDestroy(): void {
        if (this.formViewer) {
            this.formViewer.destroy();
        }
    }

    // 从API加载表单数据
    private async loadFormDataFromApi(): Promise<void> {
        try {
            this.loading = true;
            this.formService.get(Number(this.formId)).then(rs => {
                if (rs.success) {
                    this.formData = rs.data;
                    // 保存表单名称
                    this.formName = rs.data.name || '表单预览';
                    console.log('加载预览数据:', this.formData);
                    // 初始化表单查看器
                    this.initFormViewer();
                } else {
                    this.msg.error(`获取表单数据失败${ rs.message ? ': ' + rs.message : '' }`);
                }
            })
            .catch(err => {
                this.msg.error('获取表单数据失败');
            });


            // if (response.success && response.data) {
            //     // 解析jsonData.schema作为参数
            //     if (response.data.jsonData && response.data.jsonData.schema) {
            //         this.formData = {
            //             schema: response.data.jsonData.schema,
            //             data: {} // 可以提供默认数据
            //         };
            //         console.log('从API加载表单数据成功:', this.formData);
            //         // 初始化表单查看器
            //         await this.initFormViewer();
            //     } else {
            //         this.msg.error('表单数据格式不正确');
            //     }
            // } else {
            //     this.msg.error(response.message || '获取表单数据失败');
            // }
        } catch (error) {
            console.error('从API加载表单数据失败:', error);
            this.msg.error('获取表单数据失败');
        } finally {
            this.loading = false;
        }
    }

    private loadPreviewData(): void {
        try {
            const previewDataStr = sessionStorage.getItem('formPreviewData');
            if (previewDataStr) {
                const previewData = JSON.parse(previewDataStr);
                this.formData = previewData;
                console.log('加载预览数据:', this.formData);
                // 初始化表单查看器
                this.initFormViewer();
            } else {
                this.msg.error('未找到预览数据');
            }
        } catch (error) {
            console.error('加载预览数据失败:', error);
            this.msg.error('加载预览数据失败');
        }
    }

    private async initFormViewer(): Promise<void> {
        if (!this.formData || !this.formData.schema) {
            this.msg.error('表单数据无效');
            return;
        }

        try {
            this.loading = true;

            // 创建表单查看器实例
            this.formViewer = new Form({
                container: this.formContainer.nativeElement
            });

            // 导入表单schema和数据
            await this.formViewer.importSchema(this.formData.schema, this.formData.data || {});

            console.log('表单查看器初始化成功');
            console.log('表单schema:', this.formData.schema);

            // 调试：检查渲染后的DOM结构并手动处理行布局
            setTimeout(() => {
                this.processRowLayout();
            }, 1000);

        } catch (error) {
            console.error('初始化表单查看器失败:', error);
            this.msg.error('初始化表单查看器失败');
        } finally {
            this.loading = false;
        }
    }



    // 关闭预览窗口
    closePreview(): void {
        window.close();
    }

    // 打印表单
    printForm(): void {
        window.print();
    }

    // 手动处理行布局
    private processRowLayout(): void {
        const container = this.formContainer.nativeElement;
        const formElement = container.querySelector('.fjs-form');

        if (!formElement || !this.formData?.schema?.components) {
            return;
        }

        console.log('开始处理行布局...');

        // 分析schema中的行布局信息
        const rowGroups = this.groupComponentsByRow(this.formData.schema.components);
        console.log('行分组结果:', rowGroups);

        // 查找所有表单字段元素
        const formFields = Array.from(formElement.querySelectorAll('.fjs-form-field')) as Element[];
        console.log('找到的表单字段:', formFields.length);

        // 为每个行组创建行布局
        Object.keys(rowGroups).forEach(rowId => {
            const componentsInRow = rowGroups[rowId];
            if (componentsInRow.length > 1) {
                this.createRowLayout(formElement, componentsInRow, formFields);
            }
        });
    }

    // 按行分组组件
    private groupComponentsByRow(components: any[]): { [rowId: string]: any[] } {
        const rowGroups: { [rowId: string]: any[] } = {};

        components.forEach(component => {
            if (component.layout && component.layout.row) {
                const rowId = component.layout.row;
                if (!rowGroups[rowId]) {
                    rowGroups[rowId] = [];
                }
                rowGroups[rowId].push(component);
            }
        });

        return rowGroups;
    }

    // 创建行布局
    private createRowLayout(formElement: Element, componentsInRow: any[], formFields: Element[]): void {
        console.log('为行创建布局:', componentsInRow.map(c => c.key || c.id));

        // 找到这些组件对应的DOM元素
        const fieldElements: Element[] = [];
        componentsInRow.forEach(component => {
            const fieldElement = formFields.find(field => {
                const fieldId = field.getAttribute('data-field-id') ||
                              field.querySelector('[data-field-id]')?.getAttribute('data-field-id') ||
                              field.querySelector(`[name="${component.key}"]`)?.closest('.fjs-form-field');
                return fieldId === component.id ||
                       field.querySelector(`[name="${component.key}"]`) ||
                       field.textContent?.includes(component.label);
            });
            if (fieldElement) {
                fieldElements.push(fieldElement);
            }
        });

        console.log('找到的字段元素:', fieldElements.length);

        if (fieldElements.length > 1) {
            // 创建行容器
            const rowContainer = document.createElement('div');
            rowContainer.className = 'fjs-layout-row custom-row-layout';

            // 创建列容器
            const columnsContainer = document.createElement('div');
            columnsContainer.className = 'fjs-columns';

            // 为每个字段创建列
            fieldElements.forEach(fieldElement => {
                const column = document.createElement('div');
                column.className = 'fjs-column';

                // 移动字段到列中
                const clonedField = fieldElement.cloneNode(true) as Element;
                column.appendChild(clonedField);
                columnsContainer.appendChild(column);

                // 隐藏原始字段
                (fieldElement as HTMLElement).style.display = 'none';
            });

            rowContainer.appendChild(columnsContainer);

            // 插入行容器到第一个字段的位置
            const firstField = fieldElements[0];
            firstField.parentNode?.insertBefore(rowContainer, firstField);

            console.log('行布局创建完成');
        }
    }

    // 导出表单数据
    exportData(): void {
        try {
            if (this.formViewer) {
                // 获取当前表单数据
                const data = this.formViewer.submit();
                const json = JSON.stringify(data, null, 2);

                const blob = new Blob([json], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `form-data-${this.formId || 'preview'}.json`;
                link.click();
                window.URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('导出表单数据失败:', error);
            this.msg.error('导出表单数据失败');
        }
    }
}

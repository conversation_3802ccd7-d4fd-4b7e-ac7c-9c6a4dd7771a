.logo{
  display: flex;
  li{
    display: flex;
    align-items: center;
    height: 76px;
    width: 276px;
    img{height: 76px;}
  }
}
.active{
  background-color: #fff !important;
  border: 2px solid #155eef !important;
}


.welcome-box{
  margin-top:12px;
  padding: 40px;
  background: url("src/assets/images/welcome-box.jpg");
  background-size: cover;
  .welcome-title{
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
  }
  .description{
    font-size: 16px;
    font-weight: 300;
  }
}

.welcome-content{
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px 20px 20px 40px;
  .welcome-card{
    padding: 16px 20px;
    background: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 4px 10px 0 rgba(190,214,255,.2);
    border: 1px solid #ececec;
  }
  .mouse-pointer{
    cursor: pointer;
  }
  .welcome-content-row{
    margin-bottom: 17px;
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    .welcome-content-header{
      font-weight: 700;
    }
    .welcome-content-column{
      .content-column {
        color: rgba(0, 0, 0, .87);
        flex: 1;
        min-width: 0;
        //height: 144px;
        display: flex;
        flex-direction: column;
        .quota{
          min-height: 265px;
          align-items: center;
          align-content: center;
          display: flex;
          nz-empty{
            margin: 0 auto;
          }
        }
      }
      .content-column-header{
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        img{
          width: 30px;
          height: 30px;
          margin-right: 10px;
        }
        span{
          font-size: 12px;
          font-weight: 300;
          color: #373737;
        }
      }
      .content-column-items{
        display: flex;
        .item{
          padding: 3px;
          font-size: 14px;
          font-weight: 300;
          display: block;
          color: #444444;
          line-height: 20px;
          //background-color: white;
          div{
            height:35px;
            line-height: 35px;
            padding-left: 15px;
          }
          div:first-child{
            font-weight: 700;
            background-color: #edf1ff;
            border-radius: 6px 6px 0 0;
            border:1px solid #fff;
          }
          div:nth-child(2){
            font-size: 12px;
            background-color: #fff;
            border-radius: 0 0 6px 6px;
            border-size: 0 1px 1px;
            border:solid #fff;
          }

        }
        .item:hover > div:first-child{
          color: #155eef;
        }
      }
    }
  }
  span.welcome-content-row{
    margin-bottom: 0;
    padding: 5px;
  }
}
.welcome-content div{
  min-width: 0;
}
.right-item{
  margin-left: auto;
}
.top-item{
  margin-bottom: auto;
}
.half-item{
  width: 50%;
}
.full-item{
  width: 100%;
}
.one-third-item{
  width: 33.33%;
}
.column{
  flex-direction: column;
}

#table {
  width: 100%;
  height: 265px; // 保持原有高度

  .cost-chart {
    width: 100%;
    height: 100%;
  }
}

.message{
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden;    /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分用省略号表示 */
    width: 100%; /* 设置一个固定的宽度或最大宽度 */
    display: block; /* 或者使用 inline-block */
}

.search-bar{
  font-size: 14px;
}

// 表格样式优化
:host ::ng-deep nz-table {
  .ant-table {
    table-layout: fixed !important;

    th, td {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    // 确保第一列（名称）有足够的宽度
    th:first-child, td:first-child {
      width: 40% !important;
      min-width: 200px;
    }

    // 其他列平均分配剩余宽度
    th:not(:first-child), td:not(:first-child) {
      width: 15% !important;
      min-width: 100px;
    }

    // 名称列特殊样式
    .name-column {
      text-align: left !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      cursor: default !important;
      max-width: 0 !important; // 强制触发省略号
      padding-left: 16px !important;
      padding-right: 16px !important;

      // 确保tooltip能正常显示
      &:hover {
        position: relative;
      }
    }
  }
}

// 额外的样式确保生效
:host ::ng-deep .ant-table-tbody > tr > td.name-column {
  text-align: left !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 0 !important;
}
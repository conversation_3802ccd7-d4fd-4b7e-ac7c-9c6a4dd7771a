<!--banner图-->
<div class="intro">
    <app-intro-banner [intro]="intro"></app-intro-banner>
    <!--锚点导航-->
    <div class="nav-choose">
        <div class="grid-1200">
            <a href="javascript:void(0)" id="box1"
                (click)="clickbox1()">产品优势</a>
            <a href="javascript:void(0)" id="box2"
                (click)="clickbox2()">应用场景</a>
            <!--<a href="javascript:void(0)" id="box3" (click)="clickbox3()">价格</a>-->
        </div>
    </div>
    <!--产品优势-->
    <div class="advantage-box" id="advantage">
        <div class="grid-1200">
            <h2 class="advantage-title">产品优势</h2>
            <div nz-row nzGutter="30">
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy server_picfour"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">低成本</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>按需取用，提高资源利用率，有效降低成本</p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy advantage_picone"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">高可用</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>自动监测虚拟机实例的健康状况，及时替换不健康实例，为您的业务保驾护航
                            </p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy server_picthree"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">自运维</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>无需人工干预，自动根据用户预设策略创建和释放虚拟机实例
                            </p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy advantage_picfour"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">灵活丰富</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>智能调度应对各种复杂场景，多模式兼容，可同时支持不同指标、定时和动态等多种伸缩模式
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--应用场景-->
    <div class="scene-box-network" id="scene">
        <div class="grid-1200">
            <h2 class="scene-title">应用场景</h2>
            <div nz-row>
                <div
                    nz-col nzSpan="12" class="scene-col scene-col-special">
                    <div class="scene-list">
                        <a href="javascript:void(0)"
                            class="scene-icon"><img
                                src="./assets/images/pro-scene-icon2.jpg"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">应用网站</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>通过配置弹性伸缩组及合适的伸缩策略及伸缩配置，用户无需提前准备大量云服务器，系统能够根据设置的策略自动调整云服务器资源，降低了系统稳定运行的成本
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="12" class="scene-col">
                    <div class="scene-list">
                        <a href="javascript:void(0)"
                            class="scene-icon"><img
                                src="./assets/images/pro-scene-icon1.jpg"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">数据处理及计算</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>使用云服务器进行内容处理，基于对象存储服务进行数据在线传输，同时搭配弹性伸缩服务使用实现灵活可自动横向扩展能力
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<div class="clear"></div>
<!--免费试用-->
<div class="try-box" id="try">
    <div class="grid-1200">
        <h2 class="advantage-title">免费试用</h2>
        <div class="maintitle-tip">
            <p>所有用户可免费试用AIC应用托管服务的全栈服务（试用时有资源限制）</p>
        </div>
    </div>
</div>

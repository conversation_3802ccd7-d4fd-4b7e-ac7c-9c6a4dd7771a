@import '../../../../style/common/_variable.less';
// .config-content{
//     padding: 20px 180px;
//     .panel{
//         background: #fff;
//         width: 100%;
//         margin: 0 auto;
//         padding: 0 20px;
//         .panel-body{
//             margin-right: 530px;
//         }

//         .panel-aside{
//             margin-right: 15px;
//             margin-top: 15px;
//             background: #f4f7fa;
//             padding: 3px 20px 20px 20px;
//             width: 470px;
//         }
//         .pined{
//             position: fixed;
//             top: 124px;
//             right: 190px;
//         }
//     }
// } 
.form-action {
    // border-top: 1px solid @light-gray;
    background-color: #fff;
    padding: 20px 0;
    text-align: center;

    button {
        min-width: 120px;
    }
}
.warning-text {
    padding-top: 20px;
}
@import '../../../../style/common/_variable.less';

.pull-right button:nth-child(2) {
    margin-left: 10px;
}
.center-panel {
    width: 400px;
    height: 380px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    // border: 1px solid red;
    box-shadow: 1px 1px 5px @blue;
    margin: 40px auto;
    .center-header {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        padding: 10px;
        border-bottom: 1px solid @light-gray;
        background: @blue;
        color: #fff;
        p {
            font-size: 20px;
            text-align: center;
            margin: 0;
        }
    }
    .center-body {
        padding: 20px;
        .select-container {
            margin: 30px 0;
            span {
                display: inline-block;
                margin-right: 5px;
                width: 60px;
            }
        }
        button {
            width: 100%;
            margin-top: 30px;
        }
    }
}
.password-tips {
    font-size: 12px;
    margin-top: 10px;
    color: @gray;
}
.tc-content {
    .select-tips {
        margin-right: 20px;
    }
    .folder-tips {
        margin-left: 75px;
        li {
            font-size: 12px;
            margin: 10px 0;
            color: @placeholder-color;
            list-style: decimal;
        }
    }
    .folder-tips-error {
        li {
            color: @red;
        }
    }
}

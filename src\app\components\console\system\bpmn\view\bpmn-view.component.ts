import { Component, OnInit, OnD<PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js查看器
import BpmnViewer from 'bpmn-js/lib/Viewer';

// 导入中文翻译模块
import TranslateModule from 'src/app/utils/bpmn-translate';

// 导入必要的模块来启用拖拽功能
import ZoomScrollModule from 'diagram-js/lib/navigation/zoomscroll';
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';

@Component({
    selector: 'app-bpmn-view',
    templateUrl: './bpmn-view.component.html',
    styleUrls: ['./bpmn-view.component.less']
})
export class BpmnViewComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;

    private bpmnViewer: any;
    public processId: string | null = null;
    public processData: any = null;
    public loading = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.processId = this.route.snapshot.paramMap.get('id');

        if (this.processId) {
            this.loadProcessData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initBpmnViewer();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.bpmnViewer) {
            this.bpmnViewer.destroy();
        }
    }

    // 加载流程数据
    private async loadProcessData(): Promise<void> {
        if (!this.processId) return;

        try {
            this.loading = true;
            const response = await this.bpmnService.getTestById(Number(this.processId));

            console.log('API响应:', response);

            if (response.success) {
                this.processData = response.data;
                console.log('流程数据:', this.processData);

                // 确定要导入的XML数据
                let xmlToImport = this.defaultBpmnXml;

                if (this.processData) {
                    // 如果processData是字符串，直接使用
                    if (typeof this.processData === 'string') {
                        xmlToImport = this.processData;
                    }
                    // 如果processData是对象且有bpmnXml属性
                    else if (this.processData.bpmnXml) {
                        xmlToImport = this.processData.bpmnXml;
                    }
                    // 如果processData是对象且有xml属性
                    else if (this.processData.xml) {
                        xmlToImport = this.processData.xml;
                    }
                }

                // 验证XML是否包含图形信息，如果没有则添加基本的图形信息
                if (xmlToImport && !xmlToImport.includes('bpmndi:BPMNDiagram')) {
                    console.warn('XML缺少图形信息，将添加基本的图形布局');
                    xmlToImport = this.addBasicDiagramInfo(xmlToImport);
                }

                console.log('将要导入的XML:', xmlToImport);

                // 如果BPMN查看器已初始化，导入XML
                if (this.bpmnViewer) {
                    this.importBpmnXml(xmlToImport);
                }
            } else {
                this.msg.error('加载流程数据失败: ' + (response.message || '未知错误'));
                console.error('API返回失败:', response);
            }
        } catch (error) {
            console.error('加载流程数据失败:', error);
            this.msg.error('加载流程数据失败');

            // 如果API调用失败，使用默认XML
            if (this.bpmnViewer) {
                this.importBpmnXml(this.defaultBpmnXml);
            }
        } finally {
            this.loading = false;
        }
    }

    // 初始化BPMN查看器
    private initBpmnViewer(): void {
        if (!this.bpmnContainer) {
            return;
        }

        try {
            // 创建BPMN查看器实例（只读模式，但支持拖拽）
            this.bpmnViewer = new BpmnViewer({
                container: this.bpmnContainer.nativeElement,
                additionalModules: [
                    TranslateModule,
                    ZoomScrollModule,
                    MoveCanvasModule
                ],
                // 启用键盘和鼠标交互
                keyboard: {
                    bindTo: window
                },
                // 启用画布交互
                canvas: {
                    deferUpdate: false
                }
            });

            // 启用画布拖拽功能
            this.enableCanvasInteraction();

            // 如果已经有流程数据，导入XML
            if (this.processData) {
                let xmlToImport = this.defaultBpmnXml;

                if (typeof this.processData === 'string') {
                    xmlToImport = this.processData;
                } else if (this.processData.bpmnXml) {
                    xmlToImport = this.processData.bpmnXml;
                } else if (this.processData.xml) {
                    xmlToImport = this.processData.xml;
                }

                this.importBpmnXml(xmlToImport);
            } else {
                // 如果没有数据，先导入默认XML
                this.importBpmnXml(this.defaultBpmnXml);
            }

        } catch (error) {
            console.error('初始化BPMN查看器失败:', error);
            this.msg.error('初始化BPMN查看器失败');
        }
    }

    // 启用画布交互功能
    private enableCanvasInteraction(): void {
        if (!this.bpmnViewer) return;

        try {
            // 获取必要的服务
            const canvas = this.bpmnViewer.get('canvas');
            const eventBus = this.bpmnViewer.get('eventBus');
            const zoomScroll = this.bpmnViewer.get('zoomScroll');
            const moveCanvas = this.bpmnViewer.get('moveCanvas');

            console.log('画布服务获取成功:', { canvas, eventBus, zoomScroll, moveCanvas });

            // 确保拖拽功能启用
            if (moveCanvas) {
                console.log('MoveCanvas模块已启用');
            }

            // 确保缩放功能启用
            if (zoomScroll) {
                console.log('ZoomScroll模块已启用');
            }

            // 监听画布事件
            eventBus.on('canvas.init', () => {
                console.log('画布初始化完成，交互功能已启用');
            });

            // 监听拖拽事件
            eventBus.on('canvas.move.start', () => {
                console.log('开始拖拽画布');
                const container = this.bpmnContainer.nativeElement;
                container.style.cursor = 'grabbing';
            });

            eventBus.on('canvas.move.end', () => {
                console.log('结束拖拽画布');
                const container = this.bpmnContainer.nativeElement;
                container.style.cursor = 'grab';
            });

            // 设置容器样式
            const container = this.bpmnContainer.nativeElement;
            container.style.cursor = 'grab';
            container.style.userSelect = 'none';

            // 添加键盘快捷键支持
            this.addKeyboardShortcuts();

            console.log('画布交互功能启用完成');

        } catch (error) {
            console.error('启用画布交互失败:', error);
        }
    }

    // 测试拖拽功能
    private testDragFunctionality(): void {
        if (!this.bpmnViewer) return;

        try {
            // 检查必要的模块是否存在
            const modules = ['canvas', 'eventBus', 'zoomScroll', 'moveCanvas'];
            const loadedModules = [];
            const missingModules = [];

            modules.forEach(moduleName => {
                try {
                    const module = this.bpmnViewer.get(moduleName);
                    if (module) {
                        loadedModules.push(moduleName);
                    } else {
                        missingModules.push(moduleName);
                    }
                } catch (error) {
                    missingModules.push(moduleName);
                }
            });

            console.log('已加载的模块:', loadedModules);
            if (missingModules.length > 0) {
                console.warn('缺失的模块:', missingModules);
            }

            // 检查拖拽功能
            const moveCanvas = this.bpmnViewer.get('moveCanvas');
            if (moveCanvas) {
                console.log('✅ 拖拽功能已启用');
            } else {
                console.error('❌ 拖拽功能未启用');
            }

            // 检查缩放功能
            const zoomScroll = this.bpmnViewer.get('zoomScroll');
            if (zoomScroll) {
                console.log('✅ 缩放功能已启用');
            } else {
                console.error('❌ 缩放功能未启用');
            }

        } catch (error) {
            console.error('测试拖拽功能失败:', error);
        }
    }

    // 强制启用拖拽功能
    private forceDragEnable(): void {
        if (!this.bpmnViewer) return;

        try {
            // 获取画布容器
            const container = this.bpmnContainer.nativeElement;
            const canvas = this.bpmnViewer.get('canvas');

            // 直接在容器上添加拖拽事件监听
            let isDragging = false;
            let lastX = 0;
            let lastY = 0;

            container.addEventListener('mousedown', (e) => {
                if (e.button === 0) { // 左键
                    isDragging = true;
                    lastX = e.clientX;
                    lastY = e.clientY;
                    container.style.cursor = 'grabbing';
                    e.preventDefault();
                }
            });

            container.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const deltaX = e.clientX - lastX;
                    const deltaY = e.clientY - lastY;

                    // 使用canvas的scroll方法移动画布
                    canvas.scroll({ dx: deltaX, dy: deltaY });

                    lastX = e.clientX;
                    lastY = e.clientY;
                    e.preventDefault();
                }
            });

            container.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    container.style.cursor = 'grab';
                }
            });

            container.addEventListener('mouseleave', () => {
                if (isDragging) {
                    isDragging = false;
                    container.style.cursor = 'grab';
                }
            });

            // 添加滚轮缩放
            container.addEventListener('wheel', (e) => {
                e.preventDefault();

                const zoomStep = 0.1;
                const currentZoom = canvas.zoom();
                let newZoom;

                if (e.deltaY < 0) {
                    // 向上滚动，放大
                    newZoom = currentZoom + zoomStep;
                } else {
                    // 向下滚动，缩小
                    newZoom = currentZoom - zoomStep;
                }

                // 限制缩放范围
                newZoom = Math.max(0.1, Math.min(5, newZoom));

                canvas.zoom(newZoom);
            });

            console.log('✅ 强制拖拽功能已启用');

        } catch (error) {
            console.error('强制启用拖拽功能失败:', error);
        }
    }

    // 添加键盘快捷键支持
    private addKeyboardShortcuts(): void {
        if (!this.bpmnViewer) return;

        try {
            const keyboard = this.bpmnViewer.get('keyboard');
            const canvas = this.bpmnViewer.get('canvas');

            // 绑定键盘事件
            keyboard.addListener((context: any) => {
                const event = context.keyEvent;

                // 阻止默认行为，避免与页面快捷键冲突
                if (event.ctrlKey || event.metaKey) {
                    switch (event.key) {
                        case '0':
                            event.preventDefault();
                            this.zoomReset();
                            return true;
                        case '=':
                        case '+':
                            event.preventDefault();
                            this.zoomIn();
                            return true;
                        case '-':
                            event.preventDefault();
                            this.zoomOut();
                            return true;
                        case '1':
                            event.preventDefault();
                            this.zoomFit();
                            return true;
                    }
                }

                // 方向键移动画布
                switch (event.key) {
                    case 'ArrowUp':
                        event.preventDefault();
                        canvas.scroll({ dx: 0, dy: 50 });
                        return true;
                    case 'ArrowDown':
                        event.preventDefault();
                        canvas.scroll({ dx: 0, dy: -50 });
                        return true;
                    case 'ArrowLeft':
                        event.preventDefault();
                        canvas.scroll({ dx: 50, dy: 0 });
                        return true;
                    case 'ArrowRight':
                        event.preventDefault();
                        canvas.scroll({ dx: -50, dy: 0 });
                        return true;
                }

                return false;
            });

            console.log('键盘快捷键已启用');
        } catch (error) {
            console.error('添加键盘快捷键失败:', error);
        }
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        if (!this.bpmnViewer) {
            console.error('BPMN查看器未初始化');
            return;
        }

        try {
            console.log('开始导入XML:', xml?.substring(0, 200) + '...');

            // 验证XML是否为空或无效
            if (!xml || xml.trim() === '') {
                console.warn('XML为空，使用默认XML');
                xml = this.defaultBpmnXml;
            }

            await this.bpmnViewer.importXML(xml);
            console.log('XML导入成功');

            // 自适应画布大小
            const canvas = this.bpmnViewer.get('canvas');
            canvas.zoom('fit-viewport');

            // 确保交互功能已启用
            this.enableCanvasInteraction();

            // 测试拖拽功能
            this.testDragFunctionality();

            // 强制启用拖拽功能
            this.forceDragEnable();

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败: ' + error.message);

            // 如果导入失败，尝试使用默认XML
            if (xml !== this.defaultBpmnXml) {
                console.log('尝试使用默认XML');
                try {
                    await this.bpmnViewer.importXML(this.defaultBpmnXml);
                    const canvas = this.bpmnViewer.get('canvas');
                    canvas.zoom('fit-viewport');
                } catch (defaultError) {
                    console.error('导入默认XML也失败:', defaultError);
                }
            }
        }
    }

    // 为BPMN XML添加基本的图形信息
    private addBasicDiagramInfo(xml: string): string {
        try {
            // 解析XML
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xml, 'text/xml');

            // 检查是否已经有图形信息
            const existingDiagram = xmlDoc.querySelector('bpmndi\\:BPMNDiagram, BPMNDiagram');
            if (existingDiagram) {
                return xml; // 已经有图形信息，直接返回
            }

            // 获取definitions元素
            const definitions = xmlDoc.querySelector('bpmn\\:definitions, definitions');
            if (!definitions) {
                return xml; // 无法找到definitions，返回原XML
            }

            // 添加必要的命名空间
            if (!definitions.hasAttribute('xmlns:bpmndi')) {
                definitions.setAttribute('xmlns:bpmndi', 'http://www.omg.org/spec/BPMN/20100524/DI');
            }
            if (!definitions.hasAttribute('xmlns:dc')) {
                definitions.setAttribute('xmlns:dc', 'http://www.omg.org/spec/DD/20100524/DC');
            }
            if (!definitions.hasAttribute('xmlns:di')) {
                definitions.setAttribute('xmlns:di', 'http://www.omg.org/spec/DD/20100524/DI');
            }

            // 获取process元素
            const process = xmlDoc.querySelector('bpmn\\:process, process');
            if (!process) {
                return xml;
            }

            const processId = process.getAttribute('id') || 'Process_1';

            // 创建BPMNDiagram元素
            const diagram = xmlDoc.createElementNS('http://www.omg.org/spec/BPMN/20100524/DI', 'bpmndi:BPMNDiagram');
            diagram.setAttribute('id', 'BPMNDiagram_1');

            // 创建BPMNPlane元素
            const plane = xmlDoc.createElementNS('http://www.omg.org/spec/BPMN/20100524/DI', 'bpmndi:BPMNPlane');
            plane.setAttribute('id', 'BPMNPlane_1');
            plane.setAttribute('bpmnElement', processId);

            // 为每个元素添加基本的图形信息
            let x = 100;
            let y = 100;
            const elements = process.querySelectorAll('*');

            elements.forEach((element, index) => {
                const elementId = element.getAttribute('id');
                if (elementId && (element.tagName.includes('Event') || element.tagName.includes('Task') || element.tagName.includes('Gateway'))) {
                    const shape = xmlDoc.createElementNS('http://www.omg.org/spec/BPMN/20100524/DI', 'bpmndi:BPMNShape');
                    shape.setAttribute('id', elementId + '_di');
                    shape.setAttribute('bpmnElement', elementId);

                    const bounds = xmlDoc.createElementNS('http://www.omg.org/spec/DD/20100524/DC', 'dc:Bounds');
                    bounds.setAttribute('x', x.toString());
                    bounds.setAttribute('y', y.toString());
                    bounds.setAttribute('width', '100');
                    bounds.setAttribute('height', '80');

                    shape.appendChild(bounds);
                    plane.appendChild(shape);

                    x += 150; // 水平间距
                    if (x > 600) {
                        x = 100;
                        y += 120; // 垂直间距
                    }
                }
            });

            diagram.appendChild(plane);
            definitions.appendChild(diagram);

            // 序列化回XML字符串
            const serializer = new XMLSerializer();
            return serializer.serializeToString(xmlDoc);

        } catch (error) {
            console.error('添加图形信息失败:', error);
            return xml; // 如果处理失败，返回原XML
        }
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/bpmn']);
    }

    // 缩放控制
    zoomIn(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(canvas.zoom() + 0.1);
    }

    zoomOut(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(canvas.zoom() - 0.1);
    }

    zoomFit(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom('fit-viewport');
    }

    zoomReset(): void {
        const canvas = this.bpmnViewer.get('canvas');
        canvas.zoom(1);
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnViewer.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnViewer.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processData?.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }
}
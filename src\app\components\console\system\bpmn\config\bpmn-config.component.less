// BPMN编辑器样式将通过angular.json全局导入

.bpmn-editor-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .bpmn-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;

    .toolbar-left {
      flex: 1;

      nz-form {
        margin: 0;

        nz-form-item {
          margin-bottom: 0;
          margin-right: 16px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .bpmn-description {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fff;

    nz-form-item {
      margin-bottom: 0;

      nz-form-label {
        width: 60px;
      }

      nz-form-control {
        flex: 1;
      }
    }
  }

  .bpmn-main-container {
    flex: 1;
    display: flex;
    height: calc(100% - 140px);

    .bpmn-canvas-container {
      flex: 1;
      position: relative;

      .bpmn-canvas {
        width: 100%;
        height: 100%;

        // 覆盖bpmn-js默认样式
        .djs-container {
          background: #fff;
        }

        // 工具栏样式调整
        .djs-palette {
          left: 20px;
          top: 20px;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        // 上下文菜单样式
        .djs-context-pad {
          .djs-context-pad-group {
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }

        // 选中元素样式
        .djs-shape.selected {
          .djs-outline {
            stroke: #1890ff;
            stroke-width: 2px;
          }
        }

        // 连接线样式
        .djs-connection.selected {
          .djs-outline {
            stroke: #1890ff;
            stroke-width: 2px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bpmn-editor-container {
    .bpmn-toolbar {
      flex-direction: column;
      align-items: flex-start;

      .toolbar-left {
        margin-bottom: 12px;
        width: 100%;
      }

      .toolbar-right {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}
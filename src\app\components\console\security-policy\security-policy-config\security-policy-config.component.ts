import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MessageService } from 'src/app/service/console/utils/message.service';

@Component({
    selector: 'app-security-policy-config',
    templateUrl: './security-policy-config.component.html',
    styleUrls: ['./security-policy-config.component.less']
})
export class SecurityPolicyConfigComponent implements OnInit {
    constructor(
        private fb: FormBuilder,
        private msg: MessageService,
    ) {}
    ngOnInit() {
    }

    resData(username) {
    }

    getInitData(id){

    }

    selectConfig(param){
    }

    securityComponentChange(changeType){

    }

    createSecurity(){
        
    }
}

.config-content {
    &.sm {
        .label-text {
            min-width: 80px;
        }

        // .select-group {

        // }

        .form-hint {
            padding-left: 80px;
        }
    }
}

.monitor-info {
    tbody {
        tr {
            td {
                padding: 10px 0;
            }

            .th {
                font-weight: bold;
                color: #555;
            }
        }
    }
}

.ip-item {
    width: 47px!important;
}

.selected {
    background-color: grey;
    color: white !important;
    height: 40px;
    padding: 8px;
}

.unSelected {
    height: 40px;
    padding: 8px;
}

<nz-modal [(nzVisible)]="isVisible" [nzMaskClosable]="false"
    [nzTitle]="type === 'vpc' ? '创建账户' : '创建账户'"
    nzOkText="创建" [nzOkLoading]="isCreating"
    [nzWidth]="500"
    [nzBodyStyle]="{padding: '0 24px'}"
    (nzAfterOpen)="modalOpened()"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="addOrUpdate()">
    <ng-container *nzModalContent>
        <form [formGroup]="user" class="config-content md network-form">
            <nz-form-item>
                <nz-form-label nzRequired>账户名称</nz-form-label>
                <nz-form-control nzErrorTip="请输入账户名称">
                    <input nz-input formControlName="name" placeholder="请输入账户名称" />
                </nz-form-control>
            </nz-form-item>
            <nz-form-item>
                <nz-form-label nzRequired>姓名</nz-form-label>
                <nz-form-control nzErrorTip="请输入姓名">
                    <input nz-input formControlName="displayName" placeholder="请输入姓名" />
                </nz-form-control>
            </nz-form-item>
            <nz-form-item>
                <nz-form-label>电子邮箱</nz-form-label>
                <nz-form-control nzErrorTip="请输入有效的电子邮箱">
                    <input nz-input formControlName="email" placeholder="请输入电子邮箱" pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" />
                </nz-form-control>
            </nz-form-item>
            <nz-form-item>
                <nz-form-label nzRequired>密码</nz-form-label>
                <nz-form-control nzErrorTip="请输入密码">
                    <input nz-input formControlName="password" placeholder="请输入密码" />
                </nz-form-control>
            </nz-form-item>
            <nz-form-item>
                <nz-form-label nzRequired>角色</nz-form-label>
                <nz-form-control nzErrorTip="请选择角色">
                    <nz-select formControlName="tenantAdmin" nzPlaceHolder="请选择角色">
                        <nz-option nzValue="" nzLabel="请选择"></nz-option>
                        <nz-option *ngFor="let item of roleList" [nzValue]="item.key"
                                   [nzLabel]="item.value">
                        </nz-option>
                    </nz-select>
                </nz-form-control>
            </nz-form-item>
        </form>
    </ng-container>
</nz-modal>

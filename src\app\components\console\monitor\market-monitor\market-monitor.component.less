@import '../../../../style/common/_variable.less';
p {
    margin: 0;
}
.action-bar-btn {
    margin-right: 10px;
}
.compare-btn {
    height: 30px;
    width: 120px;
    line-height: 30px;
    padding: 0 5px;
    margin: 0 5px;
}
.echarts-container {
    position: relative;
    display: inline-block;
    width: 45%;
    height: 400px;
    border: 1px solid #DBDEE3;
    border-radius: 3px;
    margin: 10px 15px;
    .tool-box {
        height: 10%;
        padding: 10px 20px;
        // border: 1px solid red;
        div {
            // display: inline-block;
            float: right;
            .fa {
                cursor: pointer;
                margin: 0 5px;
                &:hover {
                    color: @primary;
                }
            }
        }
    }
    .echarts {
        width: 100%;
        height: 90%;
    }
}
.empty-container {
    position: absolute;
    top: 50%;
    left: 40%;
    font-size: 20px;
    color: @gray;
}
.taller-sele {
    height: 80px;
}
.order-list {
    margin: 10px 0 0 10px;
    font-size: 12px;
    color: #aaa;
    li {
        list-style-type: decimal;
        margin: 8px 0;
    }
}
.warning-text {
    margin: 10px 0;
    color: @red;
}
.warning-text2 {
    color: @red;
}
.col-sele-2 {
    width: 20%;
}
.threshold-container {
    display: inline-block;
    &:nth-child(2) {
        margin: 0 7px;
    }
    .threshold-input {
        width: 55px;
    }
}
.warning-tips {
    display: inline-block;
    margin-left: 30px;
    font-size: 12px;
    color: @gray;
}
.red {
    color: @red;
}
.tc-content {
    position: relative;
}
.aggregation-container {
    display: inline-block;
    width: 33%;
    span {
        display: inline-block;
        margin-right: 8px;
    }
}
.break-word-title {
    word-break: break-all;
}
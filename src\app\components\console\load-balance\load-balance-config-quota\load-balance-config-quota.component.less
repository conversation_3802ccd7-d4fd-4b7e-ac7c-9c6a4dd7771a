@import '../../../../style/common/_variable.less';

.sub-title {
    text-align: left;
    font-weight: bold;
    margin: 5px 0 10px;
    line-height: 20px;

    &:before {
        content: '';
        display: inline-block;
        width: 2px;
        height: 20px;
        background-color: @primary;
        vertical-align: top;
        margin-right: 5px;
    }

    button {
        font-size: 12px;
    }
}

.sub-table-section {
    margin-top: 0;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }

    td {
        transition: none;
        transition: background-color 0.3s ease 0s;
        span {
            display: inline-block;
            line-height: 20px;
        }
    }

    tr.editing {
        td {
            padding-top: 7px;
            padding-bottom: 7px;
        }
    }

    // input {
    //      text-align: center;
    // }
}

.ecs-list {
    margin-top: 10px;
    li {
        margin-top: 10px;

        label {
            margin-right: 15px;
        }
    }
}

.table-content {
    td {
        vertical-align: top;
        padding-bottom: 20px !important;
    }
}

.selected-ecs {
    .ecs-name {
        width: 80%;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        vertical-align: top;
        text-overflow: ellipsis;
    }

    .weight {
        float: right;
    }
}
// 20200519 订单
.config-content{
    .table-content{
        background-color: @white;
    }

}

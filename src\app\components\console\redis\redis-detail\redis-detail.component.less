@import '../../../../style/common/_variable.less';

.instance-info {
    min-height: 21px;
}

.tip {
    color: #999;
}

.action-bar {
    margin-bottom: 20px;

    .auto-refresh {
        font-size: 12px;
        display: inline-block;
        line-height: 32px;
        margin-left: 18px;
        padding-left: 18px - 2px;
        border-left: 1px solid @light-gray;
    }
}

.section-title {
    font-size: 16px;
    margin-bottom: 15px;

    &:before {
        content: '';
        display: inline-block;
        height: 16px;
        width: 2px;
        background-color: @primary;
        vertical-align: middle;
        position: relative;
        top: -2px;
        margin-right: 10px;
    }
}

.chart-container {
    .chart-list {
        font-size: 0;
        margin: 0 -10px;

        .chart-item-container {
            display: inline-block;
            font-size: 12px;
            text-align: center;
            margin: 0 10px 20px;
            
            &:hover {
                .chart-item {
                    border-color: #666!important;
                    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.2z);
                    transform: translateY(-5px);

                    &:before,
                    &:after {
                        opacity: 1;
                    }

                    .on-chart {
                        filter: blur(4px);
                    }
                }
            }

            .chart-item {
                position: relative;
                border: 1px solid lighten(@light-border, 10%);
                transition: all .3s ease 0s;
                margin-bottom: 6px;
                border-radius: 2px;
                overflow: hidden;
                box-shadow: none;
                cursor: pointer;
                transform: translateY(0);

                &:before {
                    opacity: 0;
                    content: '';
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    background-color: rgba(0, 0, 0, 0.5);
                    transition: all 0.3s ease 0s;
                    z-index: 10;
                }

                &:after {
                    opacity: 0;
                    content: '查看详情';
                    position: absolute;
                    right: 0;
                    left: 0;
                    text-align: center;
                    height: 20px;
                    top: 50%;
                    margin-top: -20px / 2;
                    color: rgba(255, 255, 255, 0.8);
                    transition: all 0.3s ease 0s;
                    z-index: 11;
                }
            }

            .on-chart {
                width: 220px;
                height: 140px ;
            }

            .chart-title {
                .on-badge {
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 5px;
                }
            }
        }
    }
}

.detail-chart {
    width: 100%;
    height: 460px;
}

.debug-container {
    // .radio-btn {
    //     width: 100px;
    //     text-align: center;
    // }
    .form-hint {
        font-size: 12px;
        display: inline-block;
        margin-right: 20px;
        
        &.error {
            color: @red
        }
    }

    .debug-form {
        margin-top: 10px;
    }

    .output-preview {
        border-top: 1px solid @light-gray;
        margin-top: 10px;
        padding-top: 10px;

        .output-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .output-content {
            background-color: #f5f5f5;
            min-height: 260px;
            border-radius: 4px;
            padding: 10px;
        }
    }
}

.cluster-url-list {
    font-size: 12px;
    color: #777;

    li {
        padding: 2px 0;
    }

    .master {
        color: @green;
    }

    .slave {
        color: @gray;
    }
}
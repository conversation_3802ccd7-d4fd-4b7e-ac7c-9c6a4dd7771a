import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-index',
    templateUrl: './index.component.html',
    styleUrls: ['./index.component.less']
})
export class IndexComponent implements OnInit {
    constructor(
    ) {}

    showHtml = false;
    showMiddleWindow = false;

    searchName: null;
    tableList: any[] = [];
    refreshCheck = false;
    ngOnInit() {
        this.querydataList();
    }
    // 查询
    querydataList() {
    }

    /**
     * 点击备份恢复
     * @param index 点击项的下标
     */
    failOver(index) {
    }

    /**
     * 点击刷新按钮
     */
    clickRefresh() {
    }
}

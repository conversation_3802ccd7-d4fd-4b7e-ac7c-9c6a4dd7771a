import { Component, OnInit, OnD<PERSON>roy, AfterViewInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BpmnService } from 'src/app/service/console/system/bpmn.service';

// 导入bpmn-js相关模块
import BpmnModeler from 'bpmn-js/lib/Modeler';

// 导入中文翻译模块
import TranslateModule from 'src/app/utils/bpmn-translate';

@Component({
    selector: 'app-bpmn-config',
    templateUrl: './bpmn-config.component.html',
    styleUrls: ['./bpmn-config.component.less']
})
export class BpmnConfigComponent implements OnInit, AfterViewInit, OnDestroy {
    @Input() visible: boolean = false;
    @Input() editData: any = null;
    @Output() visibleChange = new EventEmitter<boolean>();
    @Output() onSave = new EventEmitter<void>();

    @ViewChild('bpmnContainer', { static: false }) bpmnContainer!: ElementRef;

    private bpmnModeler: any;
    public processForm: FormGroup;
    public loading = false;

    // 默认的BPMN XML模板
    private defaultBpmnXml = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

    constructor(
        private fb: FormBuilder,
        private msg: NzMessageService,
        private bpmnService: BpmnService
    ) {
        this.processForm = this.fb.group({
            name: ['', [Validators.required]],
            description: [''],
            category: [''],
            key: ['', [Validators.required]]
        });
    }

    ngOnInit(): void {
        // 监听visible变化，当弹框打开时初始化BPMN编辑器
        if (this.visible) {
            setTimeout(() => {
                this.initBpmnModeler();
            }, 100);
        }
    }

    ngAfterViewInit(): void {
        if (this.visible && this.bpmnContainer) {
            this.initBpmnModeler();
        }
    }

    ngOnDestroy(): void {
        if (this.bpmnModeler) {
            this.bpmnModeler.destroy();
        }
    }

    // 监听visible变化
    onVisibleChange(visible: boolean): void {
        this.visible = visible;
        this.visibleChange.emit(visible);

        if (visible) {
            // 重置表单
            this.processForm.reset();

            // 如果是编辑模式，填充表单数据
            if (this.editData) {
                this.processForm.patchValue({
                    name: this.editData.name,
                    description: this.editData.description,
                    category: this.editData.category,
                    key: this.editData.key
                });
            }

            // 延迟初始化BPMN编辑器，确保DOM已渲染
            setTimeout(() => {
                this.initBpmnModeler();
            }, 100);
        }
    }

    // 初始化BPMN建模器
    private initBpmnModeler(): void {
        if (!this.bpmnContainer) {
            return;
        }

        try {
            // 创建BPMN建模器实例
            this.bpmnModeler = new BpmnModeler({
                container: this.bpmnContainer.nativeElement,
                additionalModules: [
                    // TranslateModule // 暂时注释掉翻译模块
                ]
            });

            // 导入BPMN XML
            const xmlToImport = this.editData?.bpmnXml || this.defaultBpmnXml;
            this.importBpmnXml(xmlToImport);

        } catch (error) {
            console.error('初始化BPMN建模器失败:', error);
            this.msg.error('初始化BPMN建模器失败');
        }
    }

    // 导入BPMN XML
    private async importBpmnXml(xml: string): Promise<void> {
        try {
            await this.bpmnModeler.importXML(xml);

            // 自适应画布大小
            const canvas = this.bpmnModeler.get('canvas');
            canvas.zoom('fit-viewport');

        } catch (error) {
            console.error('导入BPMN XML失败:', error);
            this.msg.error('导入BPMN XML失败');
        }
    }

    // 保存流程
    async onOk(): Promise<void> {
        if (this.processForm.invalid) {
            Object.values(this.processForm.controls).forEach(control => {
                if (control.invalid) {
                    control.markAsDirty();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });
            return;
        }

        try {
            this.loading = true;

            // 获取BPMN XML
            const result = await this.bpmnModeler.saveXML({ format: true });
            const bpmnXml = result.xml;

            // 获取SVG图像
            const svgResult = await this.bpmnModeler.saveSVG();
            const svgImage = svgResult.svg;

            // 准备保存数据
            const formData = this.processForm.value;
            const saveData = {
                ...formData,
                bpmnXml: bpmnXml,
                svgImage: svgImage,
                id: this.editData?.id
            };

            // 调用服务保存
            await this.bpmnService.saveProcess(saveData);

            this.msg.success(this.editData ? '流程更新成功' : '流程创建成功');
            this.onVisibleChange(false);
            this.onSave.emit();

        } catch (error) {
            console.error('保存流程失败:', error);
            this.msg.error('保存流程失败');
        } finally {
            this.loading = false;
        }
    }

    // 取消
    onCancel(): void {
        this.onVisibleChange(false);
    }

    // 下载BPMN文件
    async downloadBpmn(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveXML({ format: true });
            const xml = result.xml;

            const blob = new Blob([xml], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processForm.value.name || 'process'}.bpmn`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载BPMN文件失败:', error);
            this.msg.error('下载BPMN文件失败');
        }
    }

    // 下载SVG图像
    async downloadSvg(): Promise<void> {
        try {
            const result = await this.bpmnModeler.saveSVG();
            const svg = result.svg;

            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.processForm.value.name || 'process'}.svg`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载SVG图像失败:', error);
            this.msg.error('下载SVG图像失败');
        }
    }
}

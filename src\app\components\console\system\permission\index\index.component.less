/*自有样式*/
.operation-btn {
  display: inline-block;
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin: 0 5px;
}
.operation-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  color: #808080;
  background-size: 100% 100%;
}
.able-icon:hover {
  color: #6EB8FF;
}
.disabled-btn:hover {
  color: #aaa;
}
.disabled-btn a{
  color: #aaa;
  cursor: not-allowed;
}
/*状态样式*/
.status-icon {
  display: inline-block;
  margin-right: 5px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
}
.fa-spin {
  margin-right: 5px;
}
.loading-status {
  color: #97a4b6;
}
.run-status {
  color: #45bb79;
}
.run-icon {
  background-color: #45bb79;
  border: 1px solid #45bb79;
}
.error-status {
  color: #ec5960;
}
.error-icon {
  background-color: #ec5960;
  border: 1px solid #ec5960;
}
/*弹出框*/
.popup-window2 {
  z-index: 100000;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: opacity 0.3s 0s, visibility 0s 0.3s;
  -moz-transition: opacity 0.3s 0s, visibility 0s 0.3s;
  transition: opacity 0.3s 0s, visibility 0s 0.3s;
}
.popup-window2.is-visible-window {
  opacity: 1;
  visibility: visible;
  -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
  -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
  transition: opacity 0.3s 0s, visibility 0s 0s;
}
.popup-container-window2 {
  position: relative;
  width: 80px;
  margin: 20em auto;
  height: 80px;
  /*padding: 20px;*/
  /*background: #FFF;*/
  -webkit-transform: scale(0.8);
  -moz-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-backface-visibility: hidden;
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  transition-property: transform;
  -webkit-transition-duration: 0.3s;
  -moz-transition-duration: 0.3s;
  -ms-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  transition-duration: 0.3s;
}
.popup-container-window2 .fa {
  color: #fff;
  font-size: 65px;
}

/*分页样式*/
.pagination-ul {
  float: right;
}
.pagination-li {
  cursor: pointer;
  margin: 0 2px;
  height: 27px;
  line-height: 27px;
}
.pagination-li:hover {
  background: #eee;
}
.page-num {
  width: 27px;
  /*margin: 0 5px;*/
}
.current-page {
  background: #007bff;
  cursor: auto;
  color: #fff;
}
.current-page:hover {
  background: #007bff;
  cursor: auto;
  color: #fff;
}
.page-title {
  width: 60px;
}
.disabled-page {
  color: #cacaca;
  cursor: not-allowed;
}
.disabled-page:hover {
  background: none;
}
.delete-txt {
  font-size: 16px;
  text-align: center;
}
p {
  margin: 0px;
}
.btn{cursor: pointer}
.active-btn{background: #35c067;color: #fff}
// 20200522 选择用户
.order_search {
    padding: 0 20px 20px 20px;
    width: 80%;
}
.order-accordion{
    width: 100%;
    padding: 0px 20px;
    height: 385px;
    overflow-y: auto;
    background: none;

    >.ant-collapse-item{
        border: none;
        margin-bottom: 2px;
        background-color: #f2f7fb;
    }
}





// .order-accordion{
//     width: 100%;
//     padding: 0px 20px;
//     height: 385px;
//     overflow-y: auto;

//     .ant-collapse,.ant-collapse-content{
//         border: none;
//     }
//     .ant-collapse{
//         background: #fff;

//         .ant-collapse-borderless>.ant-collapse-item{
           

//             >.ant-collapse-header{
                
//             }

//             .ant-collapse-content{
//                 .ant-collapse-content-box{
//                     padding: 0 0 10px 0;

//                     .ant-radio-group{
//                         display: block;
//                         width: 100%;
//                         padding: 12px 10px 12px 40px;
//                         border-bottom: 1px solid #eef4f9;

//                         &:nth-of-type(even){
//                             background-color: #f8fcff;
//                         }

//                         .ant-radio{
//                             vertical-align: middle;
//                         }
//                     }
//                 }
//             }
//             &.ant-collapse-item-active{
//                 .ant-collapse-header{
//                     background-color: #E7EDF2;
//                     color: #293A4F;
//                 }
//             }
//         }

//     }
    


// }
// .order_search{
//     padding: 0 20px 20px 20px;
//     width: 80%;
// }
// button.ant-modal-close{
//     &:active,&:focus{
//         border-style: none;
//         outline:  0 !important;
//     }
//     .ant-modal-close-x{
//         outline: 0 !important;
//         &:focus{
//             outline: 0;
//         }
//     }

// }
// .mb5{
//     margin-bottom: 10px !important;
// }


import { Component, OnInit } from '@angular/core';
declare let $: any;

@Component({
    selector: 'app-proprietary-network',
    templateUrl: './proprietary-network.component.html',
    styleUrls: ['./proprietary-network.component.less']
})
export class ProprietaryNetworkComponent implements OnInit {
    constructor() {
    }

    intro = {
        title: '专有网络',
        enName: 'Proprietary Network',
        desc: '专有网络是一个私有的隔离网络，可以自定义专有网络的IP地址范围、网段和网关等可以通过多种方式接入公网，也可以建完全与公网隔离的网络。',
        bgColor: '#1a284d',
        orderLink: null,
        type: 'VPCProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

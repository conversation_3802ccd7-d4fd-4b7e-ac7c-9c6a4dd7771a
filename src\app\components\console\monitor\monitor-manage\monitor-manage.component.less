.select-container {
    margin: 15px 0;
}
.select-tips {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 20px;
    width: 100px;
    color: #666;
}
textarea.ant-input {
    vertical-align: top;
}
.warning-tips {
    width: 300px;
    margin-left: 120px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #f5222d;
}
.warning {
    color: #FFBB55;
}
.immediate {
    color: #F5222D;
}
.critical {
    color: #6F0FF9;
}
.collapse {
    width: 550px;
    display: inline-block;
}
.margin-span {
    display: inline-block;
    margin: 0 5px;
}
.operator-box {
    margin: 10px 0;
}
// 增加内容样式
.choosable-add-text {
    cursor: pointer;
    width: 120px;
    line-height: 30px;
    margin-left: 10px;
    i {
        display: inline-block;
        margin-right: 8px
    }
}
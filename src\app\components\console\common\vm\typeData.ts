export const typeData = [
    {
        name: 'SERVER',
        title: '云主机',
    },
    {
        name: 'STORAGE',
        title: '云盘',
    },
    {
        name: 'IPSECVPN',
        title: 'IPSECVPN',
    },
    {
        name: 'ELASTIC_IP',
        title: '弹性公网IP',
    },
    {
        name: 'CLOUD_MYSQL',
        title: 'RDS-MYSQL',
    },
    {
        name: 'FILE_STORAGE',
        title: '文件存储',
    },
    {
        name: 'VPC',
        title: '专有网络',
    },
    {
        name: 'LOAD_BALANCER',
        title: '负载均衡',
    },
    {
        name: 'CLOUD_REDIS',
        title: 'Redis',
    },
    {
        name: 'BARE_METAL_CLOUD',
        title: '物理机',
    },
    {
        name: 'CLOUD_CSSP',
        title: '云安全管理平台',
    },
    {
        name: 'CLOUD_ANTITAMPERING',
        title: '网页防篡改',
    },
    {
        name: 'CLOUD_WEB_FIREWALL',
        title: 'WEB应用防火墙',
    },
    {
        name: 'CLOUD_FIREWALL',
        title: '云下一代防火墙',
    },
    {
        name: 'CLOUD_SERVER_SECURITY',
        title: '云主机安全',
    },
    {
        name: 'CLOUD_SSLVPN',
        title: 'SSLVPN',
    },
    {
        name: 'CLOUD_LOG_AUDIT',
        title: '日志审计',
    },
    {
        name: 'CLOUD_VULNERABILITY_SCAN',
        title: '漏洞扫描',
    },
    {
        name: 'CLOUD_BASTION_HOST',
        title: '云堡垒机',
    },
    {
        name: 'CLOUD_DATABASE_AUDIT',
        title: '数据库审计',
    },
    {
        name: 'CLOUD_INTRUSION_PREVENTION',
        title: '入侵防御系统',
    },
    {
        name: 'SITUATION_AWARENESS',
        title: '态势感知',
    },
    {
        name: 'CLOUD_NET',
        title: '云专线',
    },
    {
        name: 'BACKUP',
        title: '备份',
    },
    {
        name: 'DDOS',
        title: '抗DDoS',
    },
    {
        name: 'OBJECT_STORAGE_BUCKET',
        title: '对象存储',
    },
    {
        name: 'VPCPEER',
        title: '云内互联',
    },
    {
        name: 'DSSP',
        title: '数据安全'
    }
];

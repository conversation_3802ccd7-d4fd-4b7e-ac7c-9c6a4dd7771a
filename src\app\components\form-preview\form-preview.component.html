<!-- 独立的表单预览页面 - 全屏布局 -->
<div class="form-preview-standalone">
    <!-- 顶部工具栏 -->
    <div class="preview-header">
        <div class="header-left">
            <div class="logo-section">
                <img class="unfold-logo" src="./assets/images/logo/{{logo}}.png" style="height:28px;"/>
            </div>
            <div class="divider"></div>
            <h1 class="preview-title">
                <i nz-icon nzType="eye"></i>
                {{ formName }}
                <span class="form-id" *ngIf="formId && formId !== 'new'">- {{ formId }}</span>
            </h1>
        </div>

        <div class="header-right">
            <button nz-button nzType="default" (click)="printForm()" nz-tooltip="打印表单" style="margin-left: 8px;">
                <i nz-icon nzType="printer"></i>
                打印
            </button>
<!--            <button nz-button nzType="default" (click)="exportData()" nz-tooltip="导出表单数据" style="margin-left: 8px;">-->
<!--                <i nz-icon nzType="download"></i>-->
<!--                导出数据-->
<!--            </button>-->
            <button nz-button nzType="primary" (click)="closePreview()" style="margin-left: 16px;">
                <i nz-icon nzType="close"></i>
                关闭
            </button>
        </div>
    </div>

    <!-- 表单预览主体 -->
    <div class="preview-body" [class.loading]="loading">
        <!-- 表单容器 -->
        <div class="form-preview-content">
            <div class="form-wrapper">
                <div class="form-header">
                    <h2 class="form-title">{{ formData?.schema?.title || formName }}</h2>
                    <p class="form-description" *ngIf="formData?.schema?.description">
                        {{ formData.schema.description }}
                    </p>
                </div>
                
                <!-- form-js查看器容器 -->
                <div class="form-container" #formContainer>
                    <!-- FormViewer会在这里渲染表单 -->
                </div>
                
                <div class="form-footer">
                    <p class="powered-by">Powered by {{pageTitle}} Form Engine</p>
                </div>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div class="loading-mask" *ngIf="loading">
            <nz-spin nzSize="large" nzTip="加载表单中..."></nz-spin>
        </div>
    </div>
</div>

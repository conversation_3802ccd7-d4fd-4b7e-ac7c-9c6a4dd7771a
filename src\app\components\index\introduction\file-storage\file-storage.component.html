<!--banner图-->
<div class="intro">
    <app-intro-banner [intro]="intro"></app-intro-banner>
    <!--锚点导航-->
    <div class="nav-choose">
        <div class="grid-1200">
            <a href="javascript:void(0)" id="box1"
                (click)="clickbox1()">产品优势</a>
            <a href="javascript:void(0)" id="box2"
                (click)="clickbox2()">应用场景</a>
        </div>
    </div>
    <!--产品优势-->
    <div class="advantage-box" id="advantage">
        <div class="grid-1200">
            <h2 class="advantage-title">产品优势</h2>
            <div nz-row nzGutter="30">
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy server_picfour"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">兼容性强</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>提供标准NFS协议访问，使用本地文件存储的应用可以无缝切换到OFS文件存储
                            </p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy advantage_pictwo"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">高可靠</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p> 支持99.99999%数据可靠性</p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"><a
                            href="javascript:void(0)"
                            class="lazy private_pictwo"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">弹性扩容</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p>支持灵活的在线扩容，应用完全不受影响</p>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="6">
                    <div class="advantage-list updown"> <a
                            href="javascript:void(0)"
                            class="lazy advantage_picfour"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">高可用</a>
                            </h3>
                            <div class="blue-line"></div>
                            <p> 无单点故障，支持99.95%以上的服务可用性</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--应用场景-->
    <div class="scene-box-black" id="scene">
        <div class="grid-1200">
            <h2 class="scene-title">应用场景</h2>
            <div nz-row nzGutter="30">
                <div
                    nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)"
                            class="scene-icon lazy scene-oos-one"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">多媒体存储</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>媒体文件一般大小在数十MB到数十GB之间，且一般都是顺序访问。
                                    OFS文件存储对大文件顺序读写的场景进行了优化，保证媒体文件的读写高效稳定地运行
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)"
                            class="scene-icon lazy scene-oos-two"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">海量数据收集</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>OFS文件存储支持共享访问，可以无缝完成数据收集和汇总，节省了额外进行数据汇总的开销。且可以根据容量需求，进行灵活地扩展，避免了因容量不够而导致的数据迁移
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    nz-col nzSpan="8" class="scene-sc">
                    <div class="scene-list bigsmall">
                        <a href="javascript:void(0)"
                            class="scene-icon lazy scene-oos-three"></a>
                        <div class="list-caption">
                            <h3><a
                                    href="javascript:void(0)">文件共享</a>
                            </h3>
                            <div class="white-line"></div>
                            <div class="scene-txt">
                                <p>协同办公需要频繁地共享文件，多台服务器需要同时访问共享文件存储。
                                    OFS文件存储支持多主机并发访问同一个文件系统，节约大量拷贝和同步成本。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
  </div>
<div class="clear"></div>
<!--免费试用-->
<div class="try-box" id="try">
    <div class="grid-1200">
        <h2 class="advantage-title">免费试用</h2>
        <div class="maintitle-tip">
            <p>所有用户可免费试用AIC应用托管服务的全栈服务（试用时有资源限制）</p>
        </div>
    </div>
</div>

import { Component, OnInit, ViewChild } from '@angular/core';
import { NzTabPosition } from 'ng-zorro-antd/tabs';
import { HelpComponent } from '../help/help.component'
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

@Component({
    selector: 'app-install',
    templateUrl: './install.component.html',
    styleUrls: ['./install.component.less']
})
export class InstallComponent implements OnInit {
    constructor(
    ) { }

    cols = [
        {
            title: '采集规则名称',
            ColumnKey: "name",
            width: '370px'
        },
        {
            title: '关联实例',
            ColumnKey: "status",
            width: '500px'
        },
        {
            title: '操作',
            ColumnKey: ""
        }
    ];
    onResize({ width }: NzResizeEvent, col: string): void {
        this.cols = this.cols.map(e => (e.title === col ? { ...e, width: `${width}px` } : e));
    }

    @ViewChild('helpcomponent', { static: false })
    public helpcomponent: HelpComponent;

    position: NzTabPosition = 'left';

    isVisible = false;
    isOkLoading = false;

    switchValue: boolean = false;
    choose: boolean = false;
    quesion: boolean = false;;

    ngOnInit() { }

    showModal() {
        this.isVisible = true;
    }

    handleOk() {
        this.isVisible = false;
    }

    handleCancel() {
        this.isVisible = false;
    }

    menuInit(){
        if(this.switchValue){
            this.choose = false;
        }
    }

    changeselfMenu(menu){
      
        this.helpcomponent.change(menu);
        this.changeMenu(menu);
    }

    changeMenu(menu){ 
      if(menu === "windowsTool"){
            this.quesion = false;
            this.choose = true;
        } else if(menu === "question") {
            this.choose = false;
            this.quesion = true;
        }
    }
}

 @import '../../../../style/common/_variable.less';
 
 .tc-content .sele {
   height: auto;
 }
.col-sele-2 {
  width: 20%;
  clear: both;
}
.col-sele-11, .sele-label {
  display: inline-block;
  clear: both;
  float: none;
}
.sele-label {
  width: 20%;
  vertical-align: top;
}
.col-sele-11 {
  line-height: 30px;
  word-break: break-all;
  a {
    color: @primary;
  }
}
// zlh
.pull-right {
  button {
    margin: 0 10px;
  }
}
.back {
  display: inline-block;
  float: none;
  color: #0088e0;
  font-size: 12px;
  margin-top: 5px;
  cursor: pointer;
}
.statistic-view {
  width: 100%;
  text-align: center;
}
.back2 {
  display: inline-block;
  margin-left: 30px;
  font-size: 14px;
  color: #666666;
  margin-bottom: 5px;
  cursor: pointer;
}
.back2:hover {
  color: rgb(0, 128, 255);
}
.folder-name {
  color: @primary;
  cursor: pointer;
}
.upload-tips {
  font-size: 12px;
  margin: 10px 0;
  color: @placeholder-color;
}
.upload-tips-error {
  color: @red;
}
.modify-title {
  margin: 0 0 10px 30px;
  font-size: 14px;
  color: @gray;
}
.operate {
  margin: 10px;
}
.folder-tips {
  margin-left: 40px;
  li {
    list-style: decimal;
  }
}
.folder-tips-error {
  li {
    color: @red;
  }
}

.warning-tips-container {
  margin-left: 30px;
  color: @red;
}
.underlint-text {
  font-size: 12px;
  color: @primary;
  text-decoration: underline;
}
.choosable-delete-icon {
  cursor: pointer;
  margin-left: 10px;
}
.select-tips {
  display: inline-block;
  margin-bottom: 20px;
  width: 100px;
  color: #666;
}
.ip-input {
  width: 300px; margin: 5px 0
}
// 增加内容样式
.choosable-add-text {
  cursor: pointer;
  width: 150px;
  line-height: 30px;
  margin-left: 10px;
  i {
      display: inline-block;
      margin-right: 8px
  }
}
.unit-txt {
  display: inline-block;
  margin-left: 10px;
}
td {
  span {
    word-break: break-all;
  }
}

.upload-input{
  position: absolute;
  opacity: 0;
  z-index: 1;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
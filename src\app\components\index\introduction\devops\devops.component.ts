import { Component, OnInit } from '@angular/core';

declare let $: any;
@Component({
    selector: 'app-devops',
    templateUrl: './devops.component.html',
    styleUrls: ['./devops.component.less']
})
export class DevopsComponent implements OnInit {
    constructor(
    ) { }

    intro = {
        title: '云效平台',
        enName: 'AIC DevOps',
        desc: '云效平台（AIC DevOps）是一站式企业研发平台，为企业用户提供从“需求->开发->测试->发布->运维”端到端的协同服务和研发工具支撑。',
        bgColor: '#1a284d',
        orderLink: '/console/devops',
        type: 'devopsProduction',
    }

    ngOnInit() {
    }

    // 锚点导航
    clickbox1() {
        $('html, body').animate({
            scrollTop: $('#advantage').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox2() {
        $('html, body').animate({
            scrollTop: $('#scene').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox3() {
        $('html, body').animate({
            scrollTop: $('#price').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }

    clickbox4() {
        $('html, body').animate({
            scrollTop: $('#try').offset().top - 50
        }, {
            duration: 500, easing: 'swing'
        });
        return false;
    }
}

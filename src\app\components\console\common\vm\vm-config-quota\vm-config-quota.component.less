@import '../../../../../style/common/_variable.less';

// .server-config-list {
//     display: inline-block;
//     padding: 0;
//     margin: 0;
//     vertical-align: middle;
//     li {
//         border: 1px solid ;
//         display: inline-block;
//         // 20200519 订单
//         // border: 1px solid @light-border;
//         // 20200519 订单
//         width: 104px;
//         height: 115px;
//         cursor: pointer;
//         margin-right: 10px;
//         text-align: center;
//         padding: 15px 0;
//         // 20200519 订单
//         background: #F2F6F9;
//         color: #69707F;
//         border: 1px solid #E9E9E9;
//         // 20200519 订单

//         &.active {
//             // 20200519 订单
//             // color: @primary;
//             // border-color: @primary;
//             // background-color: @light-blue;
//             color: @primary-check;
//             border-color: @primary-check;
//             background-color: @primary-check-background;
//             // 20200519 订单
//         }

//         p {
//             margin-top: 3px;
//             margin-bottom: 0;

//             &:first-child {
//                 margin-top: 0;
//             }

//             &.charge {
//                 margin-top: 15px;
//             }
//         }
//     }
// }

// .link {
//     color: @primary;
// }

// .input-group-suffix {
//     margin-bottom: .5rem;
// }

// .ant-radio-button-wrapper {
//     min-width: 104px;
//     text-align: center;
//     // 20200519 订单
//     background: #F2F6F9;
//     color: #69707F;
//     border: 1px solid #E9E9E9;
    
//     &.ant-radio-button-wrapper-checked{
//         color: @primary-check;
//         border-color: @primary-check;
//         background-color: @primary-check-background;

//     }
//     // 20200519 订单
// }

// .ant-select {
//     & + .ant-select {
//         margin-left: 10px;
//     }
// }

// .bandwidth-picker,
// .disk-picker {
//     display: inline-block;
//     width: 600px;
//     vertical-align: middle;
// }

// form .ant-select {
//     width: 276px;
// }

// .hl {
//     color: #dc3545;
// }

// .vertical-top {
//     display: inline-block;
//     vertical-align: top;
//     position: relative;
//     top: 7px;
// }

// .password-hint {
//     font-size: 12px;
//     height: 0;
//     overflow: hidden;
//     transition: height 0.3s ease 0s;

//     ul {
//         margin: 0;
//         padding: 0;

//         li {
//             color: @placeholder-color;
//             padding: 3px 0;
            
//             .icon {
//                 position: relative;
//                 top: -1px;
//                 margin-right: 4px;
//             }

//             &.checked {
//                 color: @green;
//             }
//         }
//     }

//     &.unfold {
//         height: 100px;
//     }
// }

// .image-item {
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
// }
// .config-content{
//     .field-section{
//         .field-item{
//             margin: 5px 0;
    
//             .label-text{
//                 display: inline-block;
//                 min-width: 120px;
//                 font-size: 14px;
//                 vertical-align: middle;
//                 color: #666;
//             }
//             input[type=text]{
//                 padding: 2px 11px;
//                 width: 240px;
//                 height: 32px;
//                 line-height: 32px;
//                 font-size: 13px;
//             }
//         }
//         label{
//             white-space: normal;
//         }

//     }
    
    
// }  